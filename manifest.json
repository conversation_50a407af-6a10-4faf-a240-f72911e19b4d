{
    "name" : "山西综改区智慧水务",
    "appid" : "__UNI__71E8613",
    "description" : "山西综改区-智慧水务应用中心",
    "versionName" : "1.0.1",
    "versionCode" : 2,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "permissions" : {
            //如果想要在APP-PLUS平台中使用window.addEventListener('message')来进行消息传递，
            "system" : {
                "custom" : [ "custom://*" ]
            }
        },
        "compatible" : {
            "ignoreVersion" : true //true忽略版本检测提示框
        },
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Barcode" : {},
            "Camera" : {},
            "UIWebview" : {},
            "Maps" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "maps" : {
                    "amap" : {
                        "name" : "amap_13802401570BxhGpsMxc",
                        "appkey_ios" : "0dcbc761d88f10427bc28ecde70478ee",
                        "appkey_android" : "0dcbc761d88f10427bc28ecde70478ee"
                    }
                }
            },
            "splashscreen" : {
                "iosStyle" : "common",
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "static/uview/common/1080x1882.png",
                    "xhdpi" : "static/uview/common/1080x1882.png",
                    "xxhdpi" : "static/uview/common/1080x1882.png"
                },
                "useOriginalMsgbox" : true
            },
            "icons" : {
                "android" : {
                    "hdpi" : "static/uview/common/72x72.png",
                    "xhdpi" : "static/uview/common/96x96.png",
                    "xxhdpi" : "static/uview/common/144x144.png",
                    "xxxhdpi" : "static/uview/common/192x192.png"
                },
                "ios" : {
                    "appstore" : "static/uview/common/1024x1024.png",
                    "iphone" : {
                        "app@2x" : "static/uview/common/144x144.png",
                        "app@3x" : "static/uview/common/180x180.png",
                        "spotlight@2x" : ""
                    },
                    "ipad" : {
                        "app@2x" : "static/uview/common/180x180.png",
                        "proapp@2x" : "static/uview/common/180x180.png",
                        "app" : "static/uview/common/72x72.png"
                    }
                }
            }
        }
    },
    // "nativePlugins" : {
    //     "YangChuan-YCiOSFileSelect" : {
    //         "__plugin_info__" : {
    //             "name" : "ios-uniapp 文件选取word,pdf,xls等文件",
    //             "description" : "uniapp iOS文件选取 iOS选取text,pdf,word,doc,xls,ppt",
    //             "platforms" : "iOS",
    //             "url" : "https://ext.dcloud.net.cn/plugin?id=1311",
    //             "android_package_name" : "com.potevio.shanxi.water",
    //             "ios_bundle_id" : "com.potevio.shanxi.water",
    //             "isCloud" : true,
    //             "bought" : 1,
    //             "pid" : "1311",
    //             "parameters" : {}
    //         }
    //     },
    //     "Mpaas-Scan" : {
    //         "AppId" : "ALIPUB07A89DD290957",
    //         "License" : "DwoPWzm0Tk48ufUWWklZcaQrwCUBCtyWfo1nE2Yb9n2g5w+kkEouenZVRKr+ortfpD/GUKSSAu66kV/TG8w9ElRUsijdyU1AcyX1bJ/52jUaw87KEiGY7MZgE5oKE+KQrcfuUqxoAkbHSns5nEq+MZe8xEBpmEUwYKRe5pGhbomvC6RQAvxabmOHadZeo0UTnKmKOgn2gi6MzWSB/+Hv5sE6H0wvgqgdGXIeMoXXpj2ZPAm/pO2P2mgtFwtqTknITpfIiDKAEvRTKK4FKgaS1anRM7H953JQPU1SEsuHqJAa3T4Co9lUC43hBlj6xROrz5psHN5dOUADgXv5+NFv/A==",
    //         "WorkspaceId" : "default",
    //         "__plugin_info__" : {
    //             "name" : "支付宝原生扫码插件",
    //             "description" : "支付宝原生扫码组件，包体积仅0.7MB，15分钟即可完成接入。同时，mPaaS提供「扫码分析」大盘",
    //             "platforms" : "Android,iOS",
    //             "url" : "https://ext.dcloud.net.cn/plugin?id=2636",
    //             "android_package_name" : "com.potevio.shanxi.water",
    //             "ios_bundle_id" : "com.potevio.shanxi.water",
    //             "isCloud" : true,
    //             "bought" : 1,
    //             "pid" : "2636",
    //             "parameters" : {
    //                 "AppId" : {
    //                     "des" : "Android平台的AppId，请填写Android的config文件中的appId对应的值",
    //                     "key" : "mobilegw.appid",
    //                     "value" : ""
    //                 },
    //                 "License" : {
    //                     "des" : "Android平台的License,，请填写Android的config文件中的mpaasConfigLicense对应的值",
    //                     "key" : "mpaasConfigLicense",
    //                     "value" : ""
    //                 },
    //                 "WorkspaceId" : {
    //                     "des" : "Android平台的WorkspaceId，请填写Android的config文件中的workspaceId对应的值",
    //                     "key" : "workspaceId",
    //                     "value" : ""
    //                 }
    //             }
    //         }
    //     }
    // }
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxba0a86b058b5a157",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "2",
    "locale" : "zh-Hans",
    "h5" : {
        "router" : {
            "mode" : "hash",
            "base" : "./"
        },
        "title" : "智慧水务数字中心",
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "",
                    "securityJsCode" : "",
                    "serviceHost" : ""
                }
            }
        }
    }
}
