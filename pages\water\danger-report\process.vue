<template>
    <view class="container">
        <!-- 顶部导航栏 -->
        <view class="header">
            <view class="nav-bar">
                <uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
                <text class="title">隐患处理</text>
                <view style="width: 20px;"></view>
            </view>
        </view>

        <!-- 表单内容 -->
        <scroll-view class="content" scroll-y="true">
            <!-- 处理方式 -->
            <view class="form-item">
                <view class="form-label">
                    <text class="label-text">处理方式</text>
                    <text class="required">*</text>
                </view>
                <view class="form-value" @click="showProcessMethodPicker">
                    <text class="value-text" :class="{ placeholder: !formData.processMethod }">{{ formData.processMethod
                        || '请选择处理方式' }}</text>
                    <uni-icons type="right" size="16" color="#ccc"></uni-icons>
                </view>
            </view>

            <!-- 处理内容 -->
            <view class="form-item">
                <view class="form-label">
                    <text class="label-text">处理内容</text>
                    <text class="required">*</text>
                </view>
                <view class="textarea-container">
                    <textarea class="textarea-input" v-model="formData.processContent" placeholder="请输入处理内容"
                        maxlength="500" auto-height></textarea>
                </view>
            </view>

            <!-- 上传图片 -->
            <view class="form-item">
                <view class="form-label">
                    <text class="label-text">上传图片</text>
                </view>
                <view class="image-upload">
                    <view v-for="(image, index) in formData.images" :key="index" class="image-item">
                        <image :src="image" class="uploaded-image" mode="aspectFill"></image>
                        <view class="delete-btn" @click="deleteImage(index)">
                            <uni-icons type="close" size="16" color="#fff"></uni-icons>
                        </view>
                    </view>
                    <view v-if="formData.images.length < 9" class="add-image" @click="chooseImage">
                        <uni-icons type="plus" size="30" color="#ccc"></uni-icons>
                    </view>
                </view>
            </view>
        </scroll-view>

        <!-- 底部提交按钮 -->
        <view class="bottom-button">
            <button class="submit-btn" @click="submitForm" :disabled="!canSubmit">提交</button>
        </view>

        <!-- 处理方式选择器 -->
        <!-- 将 uni-popup 替换为 u-popup -->
        <u-popup v-model="showProcessMethodPicker" mode="bottom">
            <view class="picker-container">
                <view class="picker-header">
                    <text class="picker-cancel" @click="cancelProcessMethod">取消</text>
                    <text class="picker-title">选择处理方式</text>
                    <text class="picker-confirm" @click="confirmProcessMethod">确定</text>
                </view>
                <picker-view class="picker-view" v-model="pickerValue" @change="onPickerChange">
                    <picker-view-column>
                        <view v-for="(item, index) in processMethodOptions" :key="index" class="picker-item">
                            <text>{{ item }}</text>
                        </view>
                    </picker-view-column>
                </picker-view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            formData: {
                processMethod: '',
                processContent: '',
                images: []
            },
            processMethodOptions: [
                '现场处理',
                '上报上级',
                '联系相关部门',
                '制定整改计划',
                '临时封闭',
                '其他'
            ],
            pickerValue: [0],
            tempPickerValue: [0]
        };
    },
    computed: {
        canSubmit() {
            return this.formData.processMethod && this.formData.processContent.trim();
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 显示处理方式选择器
        showProcessMethodPicker() {
            this.showProcessMethodPicker = true;
        },

        // 取消选择处理方式
        cancelProcessMethod() {
            this.tempPickerValue = [...this.pickerValue];
            this.showProcessMethodPicker = false;
        },

        // 确认选择处理方式
        confirmProcessMethod() {
            this.pickerValue = [...this.tempPickerValue];
            this.formData.processMethod = this.processMethodOptions[this.pickerValue[0]];
            this.showProcessMethodPicker = false;
        },

        // 选择器值变化
        onPickerChange(e) {
            this.tempPickerValue = e.detail.value;
        },

        // 选择图片
        chooseImage() {
            const remainCount = 9 - this.formData.images.length;
            uni.chooseImage({
                count: remainCount,
                sizeType: ['compressed'],
                sourceType: ['camera', 'album'],
                success: (res) => {
                    this.formData.images.push(...res.tempFilePaths);
                },
                fail: (err) => {
                    console.error('选择图片失败:', err);
                }
            });
        },

        // 删除图片
        deleteImage(index) {
            this.formData.images.splice(index, 1);
        },

        // 提交表单
        submitForm() {
            if (!this.canSubmit) {
                uni.showToast({
                    title: '请填写完整信息',
                    icon: 'none'
                });
                return;
            }

            uni.showLoading({
                title: '提交中...'
            });

            // 模拟提交
            setTimeout(() => {
                uni.hideLoading();
                uni.showToast({
                    title: '提交成功',
                    icon: 'success'
                });

                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            }, 2000);
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;

    .header {
        background: #1890ff;
        padding-top: var(--status-bar-height);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;

        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;

            .title {
                width: 100%;
                color: #fff;
                font-size: 18px;
                font-weight: 500;
                text-align: center;
            }
        }
    }
}

.content {
    flex: 1;
    padding: 16px;
    margin-top: calc(var(--status-bar-height) + 44px);
    margin-bottom: 80px;
}

.form-item {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;

    .form-label {
        display: flex;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        .label-text {
            font-size: 16px;
            color: #333;
        }

        .required {
            color: #ff4d4f;
            margin-left: 4px;
        }
    }

    .form-value {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        min-height: 24px;

        .value-text {
            font-size: 16px;
            color: #333;
            flex: 1;

            &.placeholder {
                color: #ccc;
            }
        }
    }

    .textarea-container {
        padding: 16px;

        .textarea-input {
            width: 100%;
            min-height: 100px;
            font-size: 16px;
            color: #333;
            line-height: 1.5;
            border: none;
            outline: none;
            resize: none;
        }
    }

    .image-upload {
        display: flex;
        flex-wrap: wrap;
        padding: 16px;
        gap: 12px;

        .image-item {
            position: relative;
            width: 80px;
            height: 80px;

            .uploaded-image {
                width: 100%;
                height: 100%;
                border-radius: 8px;
            }

            .delete-btn {
                position: absolute;
                top: -6px;
                right: -6px;
                width: 20px;
                height: 20px;
                background: #ff4d4f;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .add-image {
            width: 80px;
            height: 80px;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }
    }
}

.bottom-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    z-index: 999;

    .submit-btn {
        width: 100%;
        height: 48px;
        background: #1890ff;
        color: #fff;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;

        &:disabled {
            background: #d9d9d9;
            color: #fff;
        }
    }
}

.picker-container {
    background: #fff;
    border-radius: 16px 16px 0 0;

    .picker-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        .picker-cancel,
        .picker-confirm {
            font-size: 16px;
            color: #1890ff;
        }

        .picker-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
    }

    .picker-view {
        height: 200px;

        .picker-item {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            font-size: 16px;
            color: #333;
        }
    }
}
</style>