<!DOCTYPE html>
<html>
<head>
    <title>创建导航图标</title>
</head>
<body>
    <canvas id="canvas" width="48" height="48" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon()">下载导航图标</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 清除画布
        ctx.clearRect(0, 0, 48, 48);
        
        // 设置样式
        ctx.fillStyle = '#ffffff';
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        
        // 绘制导航箭头
        ctx.beginPath();
        ctx.moveTo(24, 8);  // 顶点
        ctx.lineTo(16, 32); // 左下
        ctx.lineTo(24, 28); // 中间
        ctx.lineTo(32, 32); // 右下
        ctx.closePath();
        ctx.fill();
        
        // 添加小圆点
        ctx.beginPath();
        ctx.arc(24, 24, 3, 0, 2 * Math.PI);
        ctx.fillStyle = '#1890ff';
        ctx.fill();
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'navigation.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
