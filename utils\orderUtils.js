	import {
		orderStates
	} from "../api/sys.js"
	// 工单工具
	export function getOrderStateStr(item) { //工单流程状态
		// console.log("getInsOrderStateStr")
		if (item.orderState == null) {
			if (isActive(item.orderStartTime)) {
				return "已生效"
			} else {
				return "未生效"
			}
		} else {
			if (isActive(item.orderStartTime)) {
				return getOrderState(item);
			} else {
				return "未生效"
			}
		}

	};
	export function isActive(time) {
		// console.log("isActive")
		if (time == null) return true
		else {
			let dateStart = new Date(time);
			let dateCurr = new Date();
			return dateStart.getTime() < dateCurr.getTime()
		}
	};
	export function getOrderState(item) { //将数字转为文字工单状态
		// console.log("getOrderState");
		var orderStateList = uni.getStorageSync('orderState');
		if (orderStateList == null || orderStateList.length == 0) {
			// console.log("orderStateList == null ");
			orderStates().then(res => {
				console.log("getOrderState back");
				uni.setStorageSync('isSys', false);
				if (res.success) {
					console.log("get orderState back");
					orderStateList = res.data
					uni.setStorageSync('orderState', orderStateList);
				} else {
					console.log("get orderState 失败信息：" + res.msg);
					this.$u.toast(res.msg, 1000);
				}

			});
		}
		if (item == "" || item == null) return "";
		// console.log("orderStateList :" + JSON.stringify(orderStateList))
		var type = item.orderState + "";
		var stateStr = "状态";

		orderStateList.forEach(function(element, index, array) {
			if (element.no == type) {
				stateStr = element.name;
				console.info("stateStr:"+stateStr); //当前元素的值
				return false; //中断循环
			}
			// console.info("index: " + index); //当前下标
			// 	// console.info(array); //数组本身
		});
		console.log("getOrderState end"+stateStr);
		return stateStr;
	};

	export function colorBg(item) { //根据工单状态改变背景颜色
		var type = item.orderState;
		switch (type) {
			case 0: //未生效
				return "#e73c11";
			case 1: //已生效
				return "#409EFF";
			case 2: //待审核
				return "#e73cef";
			case 3: //审核拒绝
				return "#e7c511";
			case 4: //审核通过
				return "#333aa1";
			case 5: //已分派
				return "#12dc11";
			case 6: //已接受
				return "#1F6CBF";
			case 7: //已执行
				return "#1abCa0";
			case 8: //专员确认拒绝
				return "#DB2525";
			case 9: //专员确认通过
				return "#21B879";
			case 10: //部门确认拒绝
				return "#DB2525";
			case 11: //部门确认通过
				return "#21B879";
			case 12: //水厂（技术中心）确认拒绝
				return "#DB2525";
			case 13: //水厂（技术中心）确认通过
				return "#21B879";
			case 14: //已完成
				return "#0EB55C";
			case 15: //已取消
				return "#F56C6C";
			default:
				if (getOrderStateStr(item) == "已生效")
					return "#aae55d"
				else
					return "#aaa";
		}

	}