<template>

<!-- style="margin-left: 30rpx;margin-right: 30rpx;" -->
	<view class="line" >
		<text class="title">{{title}}</text>
		<u--input class="info" :type="inputType" input-align="right" placeholder="请输入内容"
			:disabled="isDisabled" :value="value" v-model="value"></u--input>
		<!-- -->

	</view>


</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => ""
			},
			inputType: {
				type: String,
				default: () => "text"
			},
			isDisabled: {
				type: Boolean,
				default: () => false
			}
		},
		watch: {
			value() {
				this.$emit('update:value', this.value);
			}
		},
		methods: {


		}
	}
</script>

<style lang="scss" scoped>

</style>