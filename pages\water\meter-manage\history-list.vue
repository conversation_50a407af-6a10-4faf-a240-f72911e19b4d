<!-- 历史记录 -->
<template>
  <view class="meter-main">
    <image class="meter-bg" mode="widthFix" src="/static/img/h-bg.png"></image>
    <!-- 		<Navbar :hideBtn="true" bgColor="#4582FD;" title="历史记录"></Navbar> -->
    <view class="content-box">
      <view class="top-box">
        <u-search v-model="keyword" :showAction="false" bgColor="rgba(243,246,252,0.63)"
                  placeholder="请输入册本名称" @blur="search()" @search="search()"></u-search>
        <text class="title1">{{ volumeName }}</text>
      </view>

      <view class="table-box">
        <scroll-view scroll-y="true" style="height: 75vh;">
          <u-empty v-if="listData.length==0"
                   icon="http://cdn.uviewui.com/uview/empty/history.png"
                   mode="history"
          >
          </u-empty>
          <view class="ceben-box">
            <view v-for="(item,index) in listData" :key="index" class="ceben-list">
              <text>时间段：{{ index }}</text>
              <view class="time-lists">
                <view v-for="(item1,index1) in item" :key="index1" class="time-list"
                      @click="navTo(item1)">
                  <view class="left">
                    <image mode="" src="/static/img/icon1.png"></image>
                    <view class="info">
                      <label for="">{{ item1.volumeName }}</label>
                      <span>水表数量：{{ item1.meterCount }}</span>
                    </view>
                  </view>
                  <view class="right">
                    <span>时间：{{ item1.executeTime }}</span>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>
<script>
import Navbar from './navbar/Navbar.vue'
import {getVolumesHistoryList} from "@/api/meter-manage";

export default {
  components: {
    Navbar,
  },
  data() {
    return {
      listData: [],
      info: null,
      keyword: '',
      queryParams: {
        volumeNum: null
      },
      volumeName: ''
    };
  },
  onLoad(options) {
    const queryString = options.item; // options.data是从url参数中获取的数据
    const item = JSON.parse(decodeURIComponent(queryString));
    this.queryParams.volumeNum = item.volumeNum;
    this.volumeName = item.volumeName;
    this.getList()
  },
  methods: {
    navTo(item) {
      const queryString = encodeURIComponent(JSON.stringify(item));
      // debugger
      uni.navigateTo({
        url: '/pages/water/meter-manage/read-record?item=' + queryString
      })
    },
    search() {
      this.getList()
    },
    // 获取册本列表
    async getList() {
      const {data} = await getVolumesHistoryList(this.queryParams)
      this.listData = data
    },
  },
};
</script>
<style lang="scss" scoped>
.meter-main {
  overflow-y: hidden;
  height: 100vh;

  .meter-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 40;
  }

  .content-box {
    position: relative;
    z-index: 42;


    .top-box {
      padding: 24rpx;
      height: 286rpx;
      box-sizing: border-box;

      .title1 {
        display: block;
        font-size: 54rpx;
        font-weight: 500;
        color: #275EEE;
        line-height: 56rpx;
        margin-top: 70rpx;
        margin-left: 20rpx;
      }

      span {
        margin-top: 22rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #00044B;
        line-height: 56rpx;
        margin-left: 20rpx;
      }
    }

    .table-box {
      width: 750rpx;
      background-color: #EFF3F9;

      .ceben-box {
        padding: 24rpx;


        .ceben-list {
          margin-bottom: 24rpx;

          text {
            font-size: 26rpx;
            font-weight: 400;
            color: #000000;
            line-height: 44rpx;
          }

          .time-lists {
            background: #FFFFFF;
            border-radius: 20rpx;
            padding: 0 24rpx;
            margin-top: 24rpx;

            .time-list {
              padding: 24rpx 0;
              border-bottom: 2rpx solid #F5F5F5;
              display: flex;
              align-items: flex-start;
              justify-content: space-between;

              .left {
                display: flex;
                align-items: flex-start;

                image {
                  width: 70rpx;
                  height: 70rpx;
                  margin-right: 26rpx;
                }

                .info {
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: flex-start;

                  label {
                    display: block;
                    font-size: 30rpx;
                    font-weight: 400;
                    color: #1172FD;
                    line-height: 44rpx;
                  }

                  span {
                    font-size: 22rpx;
                    font-weight: 400;
                    color: #999999;
                    line-height: 44rpx;
                  }
                }

              }

              .right {
                font-size: 24rpx;
                font-weight: 400;
                color: #888888;
                line-height: 44rpx;
              }
            }
          }
        }
      }
    }
  }
}
</style>
