<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="nav-bar">
        <uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
        <text class="title">我的巡检任务</text>
        <uni-icons type="reload" size="20" color="#fff" @click="refreshTasks"></uni-icons>
      </view>
    </view>

    <!-- 日期选择器 -->
    <view class="date-selector">
      <scroll-view scroll-x="true" class="date-scroll">
        <view v-for="(date, index) in dateList" :key="index" class="date-item"
          :class="{ active: selectedDateIndex === index }" @click="selectDate(index)">
          <text class="date-text">{{ date.label }}</text>
        </view>
      </scroll-view>
      <view class="calendar-icon" @click="showDatePicker">
        <uni-icons type="calendar" size="20" color="#666"></uni-icons>
      </view>
    </view>

    <!-- 日期选择器弹窗 -->
    <uni-datetime-picker ref="datetimePicker" v-model="selectedDate" type="date"
      @change="onDateChange"></uni-datetime-picker>

    <!-- 开启巡检定位按钮 -->
    <view class="location-button" @click="startLocationInspection" :class="{ active: isLocationActive }">
      <text class="location-text">{{ isLocationActive ? '关闭巡检定位' : '开启巡检定位' }}</text>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text class="loading-text">正在加载任务...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="taskList.length === 0" class="empty-container">
        <text class="empty-text">暂无巡检任务</text>
      </view>

      <!-- 任务列表 -->
      <view v-else v-for="task in taskList" :key="task.id" class="task-card" @click="viewTaskDetail(task)">
        <view class="task-header">
          <text class="task-title">{{ task.title }}</text>
        </view>
        <view class="task-info">
          <text class="task-frequency">{{ task.frequency }}</text>
        </view>
        <view class="task-progress">
          <view class="progress-info">
            <text class="progress-text">{{ task.completed }} 任务</text>
            <text class="progress-total">已巡 {{ task.inspected }}</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: task.progressPercent + '%' }"></view>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script>
// 导入接口函数
import { getUserId, getInspectionTaskList } from '@/api/inspection.js';

export default {
  name: 'InspectionTaskIndex',
  data() {
    return {
      selectedDateIndex: 0,
      selectedDate: '',
      dateList: [],
      queryParams: {
        userId: '',
        startDate: '',
        endDate: '',
      },
      taskList: [],
      loading: false,
      // 添加定位相关变量
      locationTimer: null,
      isLocationActive: false,
      currentLocation: null
    }
  },
  onLoad() {
    this.initDateList()
    this.loadTasks()
  },
  methods: {
    // 初始化日期列表
    initDateList() {
      const today = new Date();
      const dateList = [];

      // 生成前后几天的日期
      for (let i = 0; i <= 5; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() + i);

        let label = '';
        if (i === 0) {
          label = '今天';
        } else {
          label = `${date.getMonth() + 1}月${date.getDate()}日`;
        }

        dateList.push({
          date: date,
          label: label,
          value: this.formatDate(date)
        })
      }

      this.dateList = dateList;
      this.selectedDate = this.formatDate(today);
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 选择日期
    selectDate(index) {
      this.selectedDateIndex = index;
      this.selectedDate = this.dateList[index].value;
      this.loadTasks();
    },

    // 显示日期选择器
    showDatePicker() {
      this.$refs.datetimePicker.show();
    },

    // 日期改变事件
    onDateChange(e) {
      if (!e) return;
      this.selectedDate = e;
      this.updateDateList(e);
      this.loadTasks();
    },

    // 更新日期列表
    updateDateList(selectedDate) {
      const selected = new Date(selectedDate);
      const dateList = [];

      for (let i = -3; i <= 3; i++) {
        const date = new Date(selected);
        date.setDate(selected.getDate() + i);

        let label = '';
        const today = new Date();
        if (this.isSameDay(date, today)) {
          label = '今天';
        } else {
          label = `${date.getMonth() + 1}月${date.getDate()}日`;
        }

        dateList.push({
          date: date,
          label: label,
          value: this.formatDate(date)
        })

        if (this.formatDate(date) === selectedDate) {
          this.selectedDateIndex = i + 3;
        }
      }

      this.dateList = dateList;
    },

    // 判断是否同一天
    isSameDay(date1, date2) {
      return date1.getFullYear() === date2.getFullYear() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getDate() === date2.getDate()
    },

    // 加载任务列表
    async loadTasks() {
      try {
        this.loading = true

        // 1. 先获取用户ID
        if (!this.queryParams.userId) {
          const userId = await getUserId()
          this.queryParams.userId = userId
        }

        // 2. 设置查询参数
        const params = {
          //userId: this.queryParams.userId,
          startDate: this.selectedDate + ' 00:00:00',
          endDate: this.selectedDate + ' 23:59:59',
          pageNum: 1,
          pageSize: 20
        }

        console.log('查询巡检任务参数：', params)

        // 3. 查询巡检任务列表
        const result = await getInspectionTaskList(params);
        console.log('查询巡检任务结果：', result)
        if (result.data && result.data.list) {
          this.taskList = result.data.list.map(task => ({
            id: task.taskId,
            title: task.taskName || task.title || '未命名任务',
            frequency: task.planTypeName || '未设置频率',
            completed: task.pointNum || 0,
            inspected: task.completeNum || 0,
            progressPercent: task.pointNum > 0 ?
              Math.round((task.completeNum / task.pointNum) * 100) : 0,
            // 保留原始数据以备后用
            originalData: task
          }))
        } else {
          this.taskList = []
        }

        console.log('加载任务列表成功，共', this.taskList.length, '条任务')

      } catch (error) {
        console.error('加载任务列表失败：', error)
        uni.showToast({
          title: '加载任务失败',
          icon: 'none',
          duration: 2000
        })
        // 发生错误时显示空列表
        this.taskList = []
      } finally {
        this.loading = false
      }
    },

    // 开启巡检定位
    startLocationInspection() {
      if (this.isLocationActive) {
        // 如果已经开启，则关闭定位
        this.stopLocationInspection()
        return
      }

      this.isLocationActive = true
      uni.showToast({
        title: '开启巡检定位',
        icon: 'success'
      })

      // 立即获取一次位置
      this.getLocationForInspection()

      // 设置定时器，每20秒获取一次位置
      this.locationTimer = setInterval(() => {
        this.getLocationForInspection()
      }, 20000) // 20秒

      // 将定位状态存储到本地，供map页面使用
      uni.setStorageSync('inspectionLocationActive', true)
    },

    // 停止巡检定位
    stopLocationInspection(showToast = true) {
      this.isLocationActive = false
      if (this.locationTimer) {
        clearInterval(this.locationTimer)
        this.locationTimer = null
      }
      uni.removeStorageSync('inspectionLocationActive')
      if (showToast) {
        uni.showToast({
          title: '关闭巡检定位',
          icon: 'none'
        })
      }
    },

    // 获取定位信息
    getLocationForInspection() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.currentLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            timestamp: Date.now()
          }
          // 将位置信息存储到本地，供map页面使用
          uni.setStorageSync('currentInspectionLocation', this.currentLocation)
          console.log('获取巡检定位成功:', this.currentLocation)
        },
        fail: (err) => {
          console.error('获取巡检定位失败:', err)
        }
      })
    },

    // 查看任务详情
    viewTaskDetail(task) {
      uni.navigateTo({
        url: `/pages/water/inspection-task/map?taskId=${task.id}`
      })
    },

    // 返回上一页
    // 返回上一页
    goBack() {
      // 如果定位处于开启状态，先静默关闭
      if (this.isLocationActive) {
        this.stopLocationInspection(false) // 静默关闭，不显示提示
      }
      uni.navigateBack()
    },

    // 刷新任务
    refreshTasks() {
      this.loadTasks()
      uni.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    }
  },

  // 页面销毁时清理定时器
  onUnload() {
    // 只有在定位仍然开启时才调用停止方法
    if (this.isLocationActive) {
      this.stopLocationInspection(false) // 静默关闭，不显示提示
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;

  .header {
    background: #1890ff;
    padding-top: var(--status-bar-height);

    .nav-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;

      .title {
        width: 100%;
        color: #fff;
        font-size: 18px;
        font-weight: 500;
        text-align: center;
      }
    }
  }
}

.date-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;

  .date-scroll {
    flex: 1;
    white-space: nowrap;

    .date-item {
      display: inline-block;
      padding: 8px 16px;
      margin-right: 8px;
      border-radius: 20px;
      background-color: #f8f8f8;
      transition: all 0.3s;

      &.active {
        background-color: #1890ff;

        .date-text {
          color: #fff;
        }
      }

      .date-text {
        font-size: 14px;
        color: #333;
      }
    }
  }

  .calendar-icon {
    padding: 8px;
  }
}

.location-button {
  margin: 16px;
  padding: 16px;
  background: #1890ff;
  border-radius: 8px;
  text-align: center;
  transition: background-color 0.3s;

  &.active {
    background: #f5222d;
  }

  .location-text {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
  }
}

.task-list {
  padding: 0 16px;

  .task-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .task-header {
      margin-bottom: 8px;

      .task-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .task-info {
      margin-bottom: 12px;

      .task-frequency {
        font-size: 14px;
        color: #666;
      }
    }

    .task-progress {
      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .progress-text {
          font-size: 14px;
          color: #333;
        }

        .progress-total {
          font-size: 14px;
          color: #4A90E2;
        }
      }

      .progress-bar {
        height: 4px;
        background-color: #f0f0f0;
        border-radius: 2px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
          border-radius: 2px;
          transition: width 0.3s;
        }
      }
    }
  }
}

.loading-container,
.empty-container {
  padding: 40px 16px;
  text-align: center;

  .loading-text,
  .empty-text {
    font-size: 14px;
    color: #999;
  }
}
</style>