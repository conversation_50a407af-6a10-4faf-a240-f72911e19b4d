<template>
	<view class="score-level-box" :style="{'width':reactWH+'px','height':reactWH+'px'}">
		<!-- baseCanvas -->
		<canvas id="scoreLevelBase" canvas-id="scoreLevelBase"
			:style="{'width':reactWH+'px','height':reactWH+'px'}"></canvas>
		<!-- progressCanvas -->
		<canvas id="scoreLevelProgress" canvas-id="scoreLevelProgress"
			style="position: absolute;left: 0;top:0"
			:style="{'width':reactWH+'px','height':reactWH+'px'}"></canvas>
		<view class="score-view" v-if="item.volumesStatus!=0">
			<!-- {{scoreString}} -->
			<label for="">{{item.volumesName}}</label>
			<span>{{item.havingReadingCount+item.noReadingCount}}</span>
			<text>已抄: {{item.havingReadingCount}}  未抄:{{ item.noReadingCount}}</text>
			<text style="margin-top: 20rpx;">已下载</text>
		</view>
		<view class="score-view" v-if="item.volumesStatus==0">
			<label for="">{{item.volumesName}}</label>
			<image class="img-b" src="/static/img/nodata.png" mode="widthFix" style="width: 392rpx;"></image>
			<text style="height: 220rpx; display: flex;align-items: flex-end;">暂无数据，请先下载</text>
			<view class="down" @click="down(item.meterVolumesNum)">
				<image src="/static/img/down.png" mode="widthFix" style="width: 42rpx;"></image>
				下载</view>
		</view>
	</view>
</template>

<script>
	import * as IndexApi from '@/api/meter-manage/index.js'
	export default {
		name: "scoreLevelCanvas",
		props: {
			// canvas视图的宽高(矩形->正方形)
			reactWH: {
				type: Number,
				default: () => 270,
			},
			// 进度
			progress: {
				type: Number,
				default: () => 0.4
			},
			// bookTotal: {
			// 	type: Number,
			// 	default: () => 20,
			// },
			// 数据
			item: {
				type: Object,
			},
		},
		data() {
			return {
				baseCtx: null,
				progressCtx: null,
			};
		},
		computed: {
			// 计算中心点X
			centerPointX() {
				return this.reactWH / 2;
			},
			// 计算中心点Y
			centerPointY() {
				return this.reactWH / 2;
			},
			//计算半径
			radius() {
				return this.reactWH / 2 * 0.9;
			},
			//计算小线段绘制的起始 - 偏内0.1个半径
			dotLineMoveTo() {
				return this.reactWH / 2 * (0.9 - 0.1);
			},
			//计算小线段绘制的结束 - 偏外0.1个半径
			dotLineLineTo() {
				return this.reactWH / 2 * (0.9 + 0.1);
			},
			//计算进度环的厚度
			progressWidth() {
				// 进度环的厚度, 设置为半径的8%
				return (this.reactWH / 2) * 0.08;
			},
			//计算小线段的厚度
			dotLineWidth() {
				// 小线段的厚度, 同圆环厚度
				return (this.reactWH / 2) * 0.08;
			},
			//计算进度环颜色
			strokeColor() {
				let strokeColor = ""
				if (this.progress < 0.6) {
					strokeColor = "#91CB74"
				} else if (this.progress >= 0.6 && this.progress < 0.8) {
					strokeColor = "#91CB74"
				} else if (this.progress >= 0.8) {
					strokeColor = "#D4E4F4"
				}
				return strokeColor
			},
			//计算得分字段
			scoreString() {
				return (this.score || "") + "分"
			},
			//计算得分字体大小
			scoreFontSize() {
				return this.radius * 0.4
			}
		},
		mounted() {
			// 绘制 基础圆样式
			this.drawScoreLevelBaseView()
			// 绘制 动态进度圆
			this.drawScoreLevelProgressView()
			// 最终绘制 - draw()
			this.draw()
		},
		methods: {
			// 绘制 基础圆样式
			drawScoreLevelBaseView() {
				const baseCtx = uni.createCanvasContext("scoreLevelBase", this);
				baseCtx.save();
				// 把圆心移到矩形中心点
				baseCtx.translate(this.centerPointX, this.centerPointY);

				//0.1把画布先 逆时针旋转90度, 从0点绘制开始绘制
				baseCtx.rotate(-90 * Math.PI / 180)

				//0.2绘制圆心, 方便观察
				// baseCtx.beginPath()
				// baseCtx.setStrokeStyle('#090')
				// baseCtx.arc(0, 0, 3, 0, 2 * Math.PI)
				// baseCtx.stroke()

				//1.绘制基础圆
				baseCtx.beginPath()
				baseCtx.setStrokeStyle("#EAEAEA")
				baseCtx.setLineWidth(this.progressWidth)
				baseCtx.setLineCap('round')
				baseCtx.arc(0, 0, this.radius, 0, 2 * Math.PI)
				baseCtx.stroke()
				//1.1恢复旋转角度(目的是恢复画布)
				baseCtx.rotate(90 * Math.PI / 180)

				this.baseCtx = baseCtx;
			},
			// 绘制 进度圆
			drawScoreLevelProgressView() {
				const progressCtx = uni.createCanvasContext("scoreLevelProgress", this);
				progressCtx.save();
				// 把圆心移到矩形中心点
				progressCtx.translate(this.centerPointX, this.centerPointY);

				//0.1把画布先 逆时针旋转90度, 从0点绘制开始绘制
				progressCtx.rotate(-90 * Math.PI / 180)

				//0.2绘制圆心, 方便观察
				// progressCtx.beginPath()
				// progressCtx.setStrokeStyle('#113')
				// progressCtx.arc(0, 0, 3, 0, 2 * Math.PI)
				// progressCtx.stroke()

				//1.绘制基础圆


				progressCtx.beginPath()
				progressCtx.setStrokeStyle(this.strokeColor)
				progressCtx.setLineWidth(this.progressWidth)
				progressCtx.setLineCap('round')
				progressCtx.arc(0, 0, this.radius, 0, 2 * this.progress * Math.PI)
				progressCtx.stroke()
				//1.1恢复旋转角度(目的是恢复画布)
				progressCtx.rotate(90 * Math.PI / 180)
				this.progressCtx = progressCtx;
			},
			draw() {
				setTimeout(() => {
					this.baseCtx.draw();
					this.progressCtx.draw();
				}, 50)
			},
			down(num){
				console.log(num)
				const app = this
				IndexApi.downloadVolumes(num).then(res => {
					console.log(res)
					this.$emit("down")
				})
			}
		}
	}
</script>

<style lang="scss">
	.score-level-box {
		position: relative;
		// background-color: #91f;

		.score-view {
			position: absolute;
			width: 100%;
			height: 100%;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-weight: 500;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			label{
				position: relative;
				z-index: 2;
				font-size: 48rpx;
				font-weight: 500;
				color: #000000;
				line-height: 1.2;
			}
			span{
				margin-top: 28rpx;
				font-size: 60rpx;
				font-weight: 500;
				color: #0C71FF;
				line-height: 1.2;
				margin-bottom: 20rpx;
			}
			
			text{
				position: relative;
				z-index: 2;
				font-size: 32rpx;
				font-weight: 500;
				color: #A0A1A3;
				
			}
			.img-b{
				position: absolute;
				z-index: 1;
			}
			.down{
				position: relative;
				z-index: 2;
				font-size: 32rpx;
				font-weight: 500;
				color: #0A90FE;
				margin-top: 20rpx;
			}

		}
	}
</style>

