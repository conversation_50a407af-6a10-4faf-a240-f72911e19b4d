<!-- 多选 -->
<template>
	<view>
		<u-popup :show="show" mode="bottom" @close="close" @open="open">
			<view>
				<u-checkbox-group v-model="checkboxValue1" placement="column" @change="checkboxChange">
					<u-checkbox :customStyle="{marginBottom: '8px'}" v-for="(item, index) in choices" :key="index"
						:label="item.name" :name="item.name">
					</u-checkbox>
				</u-checkbox-group>
			</view>
		</u-popup>

		<u-cell :value="value" v-model="value" :isLink="true" @click="show=true">
			<view slot="title" class="u-slot-title">
				<text class="u-cell-text">{{title}}</text>
			</view>
		</u-cell>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => "请选择"
			},
			show: {
				type: <PERSON><PERSON><PERSON>,
				default () {
					false
				}
			},

			choices: {
				type: Array,
				default () {
					return []
				}
			},
			checkboxValue1: [],
			chosen: [],
		},
		data: {
			return{
				chosen;
			}
		},

		methods: {
			checkboxChange(n) {
				console.log('change', n);
			},
			confirmChoice(e) {
				console.log("confirmChoice" + e.value[0]);
				this.value = e.value[0];
				this.$emit('update:msg', e.value[0])
				this.show = false;
			},
			changeHandlerMeasure(e) {
				console.log("changeHandlerMeasure");
				const {
					columnIndex,
					value,
					values,
					index,
					picker = this.$refs.uPicker
				} = e
				if (columnIndex === 0) {
					picker.setColumnValues(1, this.columnData[index]);

				}
			},
			close() {
				this.show = false
			},
			open() {
				// console.log('open');
			},
		}
	}
</script>

<style lang="scss" scoped>


</style>