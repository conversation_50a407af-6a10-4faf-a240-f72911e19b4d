<template>
  <view class="meter-main">
    <view class="search-box">
      <u-search v-model="queryParams.selectKey" :showAction="false" bgColor="#fff"
                placeholder="客户名称、客户户号、水表编号"
                @blur="getList()"
                @search="getList()"></u-search>
    </view>
    <view class="content-box">
      <view class="title-box">
        <span>{{ info.volumeName + '/' + info.periodNum }}</span>
        <!-- <view class="sort">综合排序</view> -->
      </view>

      <view v-for="(item,index) in list" :key="index" class="list">
        <view class="top">
          <view class="left">
            <em></em>
            <span>抄表时间：{{ item.readingTime || '暂无' }} </span>
          </view>
          <view class="right" @click="open(index)">
            备注
            <image :class="item.isOpen?'':'gt'" mode="widthFix" src="/static/img/zk.png"
                   style="width: 18rpx;margin-left: 6rpx;"></image>
          </view>
        </view>
        <view class="info-box">
          <view class="addr">
            <image mode="widthFix" src="/static/img/a1.png" style="width: 24rpx;"></image>
            <span>水表地址：</span>
            <label for="">{{ item.meterAddress || '暂无' }}</label>
          </view>
		  <view class="addr">
		    <image mode="widthFix" src="/static/img/i2.png" style="width: 24rpx;"></image>
		    <span>户主：</span>
		    <label for="">{{ item.userName || '暂无' }}</label>
		  </view>
          <view class="info-list">
            <image mode="widthFix" src="/static/img/i2.png" style="width: 24rpx;"></image>
            <span>水表编号：</span>
            <label for="">{{ item.meterNum || '暂无' }}</label>
          </view>
          <view class="info-list">
            <image mode="widthFix" src="/static/img/i2.png" style="width: 24rpx;"></image>
            <span>用水量：</span>
            <label for="">{{ item.waterConsumption || '暂无' }}</label>
          </view>
          <view class="info-list">
            <image mode="widthFix" src="/static/img/i3.png" style="width: 24rpx;"></image>
            <span>上期读数：</span>
            <label for="">{{ item.previousReading || '暂无' }}</label>
          </view>
          <view class="info-list">
            <image mode="widthFix" src="/static/img/i3.png" style="width: 24rpx;"></image>
            <span>本期读数：</span>
            <label for="">{{ item.currentReading || '暂无' }}</label>
          </view>
        </view>
        <view v-if="item.isOpen" class="remark">
          {{ item.remark ? item.remark : '暂无' }}
        </view>
      </view>

    </view>
  </view>
</template>
<script>
import {getWxVolumesReadingList} from "@/api/meter-manage";

export default {

  data() {
    return {
      keyword: '',
      list: [],
      info: null,
      queryParams: {
        volumeSchedulingId: null,
        selectKey: null
      }
    };
  },
  onLoad(options) {
    const queryString = options.item; // options.data是从url参数中获取的数据
    const item = JSON.parse(decodeURIComponent(queryString));
    this.queryParams.volumeSchedulingId = item.volumeSchedulingId
    this.info = item
    this.getList()
  },
  methods: {
    open(i) {
      this.list.forEach((item, index) => {
        if (i == index) {
          item.isOpen = !item.isOpen
        } else {
          item.isOpen = false
        }
      })
    },
    // 获取册本列表
    async getList() {
      const {rows} = await getWxVolumesReadingList(this.queryParams)
      rows.forEach((item1, index) => {
        item1.isOpen = false
      })
      this.list = rows
    },
  },
};
</script>
<style lang="scss" scoped>
.meter-main {
  width: 100vw;
  min-height: 100vh;
  background-color: #EFF3F9;
  padding: 24rpx;
  box-sizing: border-box;

  .search-box {
    width: 100%;
    margin-bottom: 60rpx;
  }

  .content-box {
    .title-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      span {
        font-size: 34rpx;
        color: #000000;
      }

      .sort {
        font-size: 28rpx;
        font-weight: 400;
        color: #2F3849;
        line-height: 30rpx;
      }

    }

    .list {
      padding: 20rpx 15rpx;
      background-color: #fff;
      margin-bottom: 15rpx;
	  box-sizing: border-box;

      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          display: flex;
          align-items: center;

          em {
            display: block;
            width: 8rpx;
            height: 28rpx;
            background: #1172FD;
            margin-right: 20rpx;
          }

          span {
            font-size: 26rpx;
            font-weight: 400;
            color: #8A9095;
          }
        }

        .right {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          font-weight: 400;
          color: #1172FD;

          .gt {
            transform: rotate(-90deg);

          }
        }
      }

      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .addr {
          width: 100%;
          margin-bottom: 10rpx;

          span {
            color: #4D4D4D;
            font-size: 24rpx;
            margin-left: 8rpx;
          }

          label {
            color: #999999;
            font-size: 24rpx;
            margin-left: 8rpx;
          }
        }

        .info-list {
          width: 50%;
          margin-bottom: 10rpx;

          span {
            color: #4D4D4D;
            font-size: 24rpx;
            margin-left: 8rpx;
          }

          label {
            color: #0C71FF;
            font-size: 24rpx;
            margin-left: 8rpx;

          }
        }
      }

      .remark {
        border-top: 2rpx dotted #D2D2D2;
        padding-top: 18rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #999999;
        line-height: 1.5;
        letter-spacing: 1rpx;;

      }


    }
  }
}
</style>
