<template>
	<view class="tree-node">
		<view class="node-content" @click.stop="toggle(node)">
			<view class="checkbox" @click.stop="select(node)">
				<uni-icons :type="node.selected ? 'checkbox-filled' : 'circle'" size="20"
					:color="node.selected ? '#1890ff' : '#999'"></uni-icons>
			</view>
			<uni-icons :type="hasChildren ? (node.open ? 'folder-open-filled' : 'folder-filled') : 'document-filled'" size="20" color="#1890ff"></uni-icons>
			<text class="node-name">{{ node.name }}</text>
			<view class="arrow-icon" v-if="hasChildren">
				<uni-icons v-if="node.loading" type="spinner-cycle" size="16" color="#1890ff"></uni-icons>
				<uni-icons v-else :type="node.open ? 'arrowdown' : 'arrowright'" size="16" color="#999"></uni-icons>
			</view>
		</view>
		<view class="node-children" v-if="shouldShowChildren">
			<tree-node v-for="child in node.children" :key="child.id" :node="child" @toggle="$emit('toggle', $event)" @select="$emit('select', $event)"></tree-node>
		</view>
	</view>
</template>

<script>
export default {
	name: 'tree-node',
	props: {
		node: {
			type: Object,
			required: true
		}
	},
	computed: {
		hasChildren() {
			return (this.node.children && this.node.children.length > 0) || this.node.hasChildren;
		},
		shouldShowChildren() {
			return this.node.open && this.node.children && this.node.children.length > 0;
		}
	},
	methods: {
		toggle(node) {
			if (this.hasChildren) {
				this.$emit('toggle', node);
			}
		},
		select(node) {
			this.$emit('select', node);
		}
	}
};
</script>

<style scoped>
.tree-node {
	padding-left: 20px;
}

.node-content {
	display: flex;
	align-items: center;
	padding: 10px 0;
	cursor: pointer;
}

.checkbox {
	margin-right: 8px;
}

.node-name {
	margin-left: 8px;
	flex: 1;
}

.arrow-icon {
	margin-left: auto;
}

.node-children {
	/* transition: all 0.3s ease; */
}
</style>