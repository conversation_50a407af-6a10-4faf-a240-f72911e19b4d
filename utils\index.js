
export function formatNumberWithCommas(valueStr, maximum) {
  if (valueStr === undefined || valueStr === null || valueStr.trim() === "") {
    return "0"; // 对于空或无效输入，返回 '0'
  }
  const num = parseFloat(valueStr);
  if (isNaN(num)) {
    return "0"; // 如果解析失败，返回 '0'
  }
  // 使用 toLocaleString 来实现千位分隔符
  // 'en-US' 通常用于标准的逗号分隔，您可以根据需要调整地区设置
  return num.toLocaleString("en-US", {
    maximumFractionDigits: maximum,
  }); // 保留最多两位小数
}

// 导出坐标转换工具
export * from './coordinateUtils.js';