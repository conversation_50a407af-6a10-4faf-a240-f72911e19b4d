	import permision from "@/js_sdk/wa-permission/permission.js"
	export function judgeIosPermission(permisionID) {
		var result = permision.judgeIosPermission(permisionID)
		console.log(result);
		var strStatus = (result) ? "已" : "未"
		uni.showModal({
			content: permisionID + '权限' + strStatus + "获得授权",
			showCancel: false
		});
	}
	export function gotoAppPermissionSetting() {
		permision.gotoAppPermissionSetting()
	}
	export function gotoAndroidPermissionSetting() {
		var main = plus.android.runtimeMainActivity(); //获取activity  
		var Intent = plus.android.importClass('android.content.Intent');
		var Settings = plus.android.importClass('android.provider.Settings');
		var intent = new Intent(Settings
			.ACTION_WIRELESS_SETTINGS); //可设置http://ask.dcloud.net.cn/question/14732这里所有Action字段
		main.startActivity(intent);
	}
	