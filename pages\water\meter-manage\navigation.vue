<template>
  <uni-popup ref="container" :mask-click="false" class="navigation-popup" type="center">

    <!-- 弹窗标题栏 -->
    <view class="popup-header">
      <text class="popup-title">抄表导航</text>
      <view class="close-icon" @click="closeNavigation">
        <text class="close-text">×</text>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <!--
        markers 多个标记地点
       -->
      <map id="amapContainers" :enable-3D="true" :enable-overlooking="true" :enable-poi="true"
           :enable-rotate="true" :enable-scroll="true" :enable-zoom="true" :latitude="mapCenter.latitude"
           :longitude="mapCenter.longitude"
           :markers="markers" :polyline="polyline"
           :scale="mapScale" :show-location="true" :zIndex="1" class="map" @markertap="onMarkerTap"
           @regionchange="onRegionChange" @tap="onMapTap">
      </map>

      <!-- 地图控制按钮 -->
      <cover-view class="map-controls">
        <cover-image class="location-btn" src="../../../static/icon/location.png"
                     @click="centerToLocation"></cover-image>
      </cover-view>


    </view>
  </uni-popup>
</template>

<script>
// 导航组件 - 专用于抄表员导航功能
import amapConfig from '@/config/amap.js';

export default {
  data() {
    return {
      // 地图中心点（山西太原坐标）
      mapCenter: {
        longitude: 112.53,
        latitude: 37.87
      },
      // 地图缩放级别
      mapScale: 13,
      // 标记点数组
      markers: [],
      // 地图上下文
      mapContext: null,
      // 用户位置
      userLocation: null,
      // 用户位置标记
      userLocationMarker: null,
      // 水表位置标记
      meterLocationMarker: null,
      // 路线规划相关
      polyline: [],
      // 水表数据
      fjdsj: {},
      // 导航状态
      isNavigating: false,
      // 路线距离信息
      routeDistance: ''
    };
  },

  async onLoad() {
    // 初始化地图上下文
    this.initMapContext();
  },

  onReady() {
    // 页面渲染完成后初始化地图上下文
    this.initMapContext();
  },

  onUnload() {
    // 清理资源
    this.mapContext = null;
  },



  methods: {
    // 初始化导航地图
    async initNavigationMap() {
      try {
        uni.showLoading({
          title: '正在获取位置...'
        });

        // 获取抄表员当前位置
        await this.getCurrentLocationForNavigation();

        // 创建抄表员和水表位置标记
        this.createNavigationMarkers();

        // 规划路线
        await this.planRoute();

        // 调整地图视野以显示完整路线
        this.adjustMapView();

        // 打开弹窗显示地图
        this.$refs.container.open();

        // 延迟初始化地图上下文，确保弹窗完全打开后再初始化
        setTimeout(() => {
          this.initMapContext();
        }, 300);

        uni.hideLoading();
        uni.showToast({
          title: '导航准备完成',
          icon: 'success'
        });

      } catch (error) {
        console.error('初始化导航失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '导航初始化失败',
          icon: 'none'
        });
      }
    },

    // 获取抄表员当前位置（用于导航）
    getCurrentLocationForNavigation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            this.userLocation = {
              latitude: res.latitude,
              longitude: res.longitude
            };
            console.log('获取到抄表员位置:', this.userLocation);
            resolve(res);
          },
          fail: (err) => {
            console.error('获取位置失败:', err);
            reject(err);
          }
        });
      });
    },

    // 创建导航标记点（抄表员位置和水表位置）
    createNavigationMarkers() {
      const markers = [];

      // 抄表员位置标记
      if (this.userLocation) {
        this.userLocationMarker = {
          id: 'user-location',
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude,
          title: '抄表员位置',
          iconPath: '/static/icons/marker-normal.png',
          width: 32,
          height: 32,
          callout: {
            content: '抄表员位置',
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#1890ff',
            padding: 6,
            display: 'ALWAYS'
          }
        };
        markers.push(this.userLocationMarker);
      }

      // 水表位置标记
      if (this.fjdsj.meterLongitude && this.fjdsj.meterLatitude) {
        this.meterLocationMarker = {
          id: 'meter-location',
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude),
          title: '水表位置',
          iconPath: '/static/icons/marker-error.png',
          width: 32,
          height: 32,
          callout: {
            content: `目标水表\n${this.fjdsj.meterAddress || ''}`,
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#52c41a',
            padding: 6,
            display: 'ALWAYS'
          }
        };
        markers.push(this.meterLocationMarker);
      }

      this.markers = markers;
      console.log('创建导航标记点:', markers);
      console.log('当前标记点数量:', this.markers.length);
      console.log('导航面板显示状态:', this.showNavigationPanel);

      // 强制触发视图更新
      this.$nextTick(() => {
        console.log('下一帧导航面板显示状态:', this.showNavigationPanel);
      });
    },

    // 规划路线 - 使用高德地图API
    async planRoute() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        console.error('缺少位置信息，无法规划路线');
        return;
      }

      try {
        const startPoint = {
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude
        };

        const endPoint = {
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude)
        };

        console.log('开始规划步行路线:', { startPoint, endPoint });

        // 调用高德地图步行路线规划API
        const routeResult = await this.getWalkingRoute(startPoint, endPoint);

        if (routeResult && routeResult.success) {
          // 使用高德API返回的路线
          this.polyline = routeResult.polyline;
          this.routeDistance = routeResult.distance;

          console.log('高德路线规划成功:', {
            distance: this.routeDistance,
            polylineCount: this.polyline.length
          });

          // 显示距离信息
          uni.showToast({
            title: `步行距离约${this.routeDistance}`,
            icon: 'none',
            duration: 3000
          });
        } else {
          // 高德API失败时使用直线路线作为备用
          console.warn('高德路线规划失败，使用直线路线');
          this.createSimpleRoute(startPoint, endPoint);
        }

      } catch (error) {
        console.error('路线规划失败:', error);
        // 创建简单的直线路线作为备用
        this.createSimpleRoute();
      }
    },

    // 调用高德地图步行路线规划API
    async getWalkingRoute(startPoint, endPoint) {
      try {
        // 检查API密钥配置
        if (!amapConfig.webServiceKey || amapConfig.webServiceKey === 'YOUR_AMAP_WEB_SERVICE_KEY') {
          console.warn('高德地图API密钥未配置，请在 config/amap.js 中设置正确的密钥');
          return { success: false, error: 'API密钥未配置' };
        }

        // 构建请求URL
        const origin = `${startPoint.longitude},${startPoint.latitude}`;
        const destination = `${endPoint.longitude},${endPoint.latitude}`;
        const config = amapConfig.apiConfig.walkingRoute;

        const url = `${config.baseUrl}?` +
          `origin=${origin}&` +
          `destination=${destination}&` +
          `key=${amapConfig.webServiceKey}&` +
          `output=json&` +
          `extensions=${config.extensions}`;

        console.log('高德API请求URL:', url);

        // 发起请求
        const response = await uni.request({
          url: url,
          method: 'GET',
          timeout: config.timeout
        });

        console.log('高德API响应:', response);

        if (response.statusCode === 200 && response.data.status === '1') {
          console.log('高德API调用成功');
          const route = response.data.route;
          if (route && route.paths && route.paths.length > 0) {
            const path = route.paths[0];

            // 解析路线坐标点
            const polylinePoints = this.parseAmapPolyline(path.steps);

            // 创建地图polyline对象
            const walkingStyle = amapConfig.mapStyle.polyline.walking;
            const polyline = [{
              points: polylinePoints,
              color: walkingStyle.color,
              width: walkingStyle.width,
              dottedLine: walkingStyle.dottedLine,
              arrowLine: walkingStyle.arrowLine,
              borderColor: walkingStyle.borderColor,
              borderWidth: walkingStyle.borderWidth
            }];

            // 格式化距离
            const distance = parseInt(path.distance);
            const formattedDistance = distance > 1000 ?
              `${(distance / 1000).toFixed(1)}公里` :
              `${distance}米`;

            return {
              success: true,
              polyline: polyline,
              distance: formattedDistance,
              duration: path.duration,
              rawData: response.data
            };
          }
        }

        return { success: false, error: '路线数据解析失败' };

      } catch (error) {
        console.error('高德API调用失败:', error);
        return { success: false, error: error.message };
      }
    },

    // 解析高德地图返回的路线坐标
    parseAmapPolyline(steps) {
      const points = [];

      steps.forEach(step => {
        if (step.polyline) {
          // 高德返回的polyline格式: "lng,lat;lng,lat;..."
          const coords = step.polyline.split(';');
          coords.forEach(coord => {
            const [lng, lat] = coord.split(',');
            if (lng && lat) {
              points.push({
                longitude: parseFloat(lng),
                latitude: parseFloat(lat)
              });
            }
          });
        }
      });

      return points;
    },

    // 创建简单路线（备用方案）
    createSimpleRoute(startPoint, endPoint) {
      if (!startPoint && (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude)) {
        return;
      }

      const start = startPoint || {
        longitude: this.userLocation.longitude,
        latitude: this.userLocation.latitude
      };

      const end = endPoint || {
        longitude: parseFloat(this.fjdsj.meterLongitude),
        latitude: parseFloat(this.fjdsj.meterLatitude)
      };

      const fallbackStyle = amapConfig.mapStyle.polyline.fallback;
      this.polyline = [{
        points: [start, end],
        color: fallbackStyle.color,
        width: fallbackStyle.width,
        dottedLine: fallbackStyle.dottedLine,
        arrowLine: fallbackStyle.arrowLine,
        borderColor: fallbackStyle.borderColor,
        borderWidth: fallbackStyle.borderWidth
      }];

      // 计算并显示直线距离
      const distance = this.calculateDistance(start, end);
      this.routeDistance = distance > 1000 ?
        `${(distance / 1000).toFixed(1)}公里` :
        `${distance.toFixed(0)}米`;

      uni.showToast({
        title: `直线距离约${this.routeDistance}`,
        icon: 'none',
        duration: 2000
      });
    },

    // 计算两点间距离（米）
    calculateDistance(point1, point2) {
      const R = 6371000; // 地球半径（米）
      const lat1Rad = point1.latitude * Math.PI / 180;
      const lat2Rad = point2.latitude * Math.PI / 180;
      const deltaLatRad = (point2.latitude - point1.latitude) * Math.PI / 180;
      const deltaLngRad = (point2.longitude - point1.longitude) * Math.PI / 180;

      const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
          Math.cos(lat1Rad) * Math.cos(lat2Rad) *
          Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      return R * c;
    },

    // 调整地图视野以显示完整路线
    adjustMapView() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        return;
      }

      const userLng = this.userLocation.longitude;
      const userLat = this.userLocation.latitude;
      const meterLng = parseFloat(this.fjdsj.meterLongitude);
      const meterLat = parseFloat(this.fjdsj.meterLatitude);

      // 计算中心点
      const centerLng = (userLng + meterLng) / 2;
      const centerLat = (userLat + meterLat) / 2;

      // 计算合适的缩放级别
      const lngDiff = Math.abs(userLng - meterLng);
      const latDiff = Math.abs(userLat - meterLat);
      const maxDiff = Math.max(lngDiff, latDiff);

      let scale = 16;
      if (maxDiff > 0.01) scale = 14;
      if (maxDiff > 0.02) scale = 13;
      if (maxDiff > 0.05) scale = 12;
      if (maxDiff > 0.1) scale = 11;

      // 更新地图中心和缩放级别
      this.mapCenter = {
        longitude: centerLng,
        latitude: centerLat
      };
      this.mapScale = scale;

      console.log('调整地图视野:', this.mapCenter, 'scale:', scale);
    },

    // 开始导航
    startNavigation() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        uni.showToast({
          title: '位置信息不完整',
          icon: 'none'
        });
        return;
      }

      this.isNavigating = true;

      // 可以在这里添加实时位置更新逻辑
      uni.showModal({
        title: '开始导航',
        content: '是否要打开外部地图应用进行导航？',
        success: (res) => {
          if (res.confirm) {
            this.openExternalMap();
          }
        }
      });
    },

    // 打开外部地图应用
    openExternalMap() {
      const meterLng = this.fjdsj.meterLongitude;
      const meterLat = this.fjdsj.meterLatitude;
      const meterName = this.fjdsj.meterAddress || '水表位置';

      // #ifdef APP-PLUS
      // 构建高德地图URL
      const amapUrl = `amaps://route/plan/?sid=BGVIS1&slat=${this.userLocation.latitude}&slon=${this.userLocation.longitude}&sname=当前位置&did=BGVIS2&dlat=${meterLat}&dlon=${meterLng}&dname=${encodeURIComponent(meterName)}&dev=0&t=0`;

      // 构建百度地图URL（备用）
      const baiduUrl = `baidumap://map/direction?origin=${this.userLocation.latitude},${this.userLocation.longitude}&destination=${meterLat},${meterLng}&mode=walking`;

      // 尝试打开高德地图
      plus.runtime.openURL(amapUrl, () => {
        console.log('打开高德地图失败，尝试百度地图');
        // 如果高德地图打开失败，尝试百度地图
        plus.runtime.openURL(baiduUrl, () => {
          console.log('打开百度地图也失败');
          uni.showToast({
            title: '请安装地图应用',
            icon: 'none'
          });
        });
      });
      // #endif

      // #ifdef H5
      // H5环境下打开网页版地图
      const webUrl = `https://uri.amap.com/navigation?from=${this.userLocation.longitude},${this.userLocation.latitude},当前位置&to=${meterLng},${meterLat},${encodeURIComponent(meterName)}&mode=walking`;
      window.open(webUrl, '_blank');
      // #endif
    },

    // 清除路线
    clearRoute() {
      this.polyline = [];
      this.isNavigating = false;
      this.routeDistance = '';
      uni.showToast({
        title: '已清除路线',
        icon: 'success'
      });
    },

    // 重新规划路线
    async replanRoute() {
      uni.showLoading({
        title: '重新规划中...'
      });

      try {
        await this.getCurrentLocationForNavigation();
        this.createNavigationMarkers();
        await this.planRoute();
        this.adjustMapView();

        uni.hideLoading();
        uni.showToast({
          title: '路线已更新',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '重新规划失败',
          icon: 'none'
        });
      }
    },
    openEvent(data) {
      console.log('接收到水表数据:', data);
      this.fjdsj = data;

      // 检查水表坐标是否存在
      if (!data.meterLongitude || !data.meterLatitude) {
        uni.showToast({
          title: '水表位置信息不完整',
          icon: 'none'
        });
        return;
      }

      // 获取抄表员当前位置并显示导航
      this.initNavigationMap();
    },
    // 初始化地图上下文
    initMapContext() {
      // #ifdef APP-PLUS
      this.mapContext = uni.createMapContext('amapContainers', this);
      console.log('高德地图上下文初始化成功');
      // #endif
    },


    // 标记点点击事件
    onMarkerTap(e) {
      console.log('标记点被点击:', e.detail);
      const markerId = e.detail.markerId;
      const marker = this.markers.find(m => m.id === markerId);

      if (marker) {
        uni.showModal({
          title: marker.title || '位置信息',
          content: `经度: ${marker.longitude}\n纬度: ${marker.latitude}`,
          showCancel: false
        });
      }
    },

    // 地图区域变化事件
    onRegionChange(e) {
      if (e.type === 'end') {
        console.log('地图区域变化:', e.detail);
      }
    },

    // 地图点击事件
    onMapTap(e) {
      console.log('地图点击:', e.detail);
    },

    // 定位到当前位置
    // 定位到当前位置
    centerToLocation() {
      console.log("这里是否执行");
      if (this.userLocation) {
        // 更新地图中心点
        this.mapCenter = {
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude
        };
        // 放大地图层级
        this.mapScale = 17;
        // 添加红色定位点标记
        this.addUserLocationMarker();
        console.log('定位到当前位置:', this.mapCenter);
        this.mapContext.moveToLocation();
      } else {
        this.getCurrentLocation();
        uni.showToast({
          title: '正在获取位置...',
          icon: 'loading'
        });
      }
    },
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };

          // 更新地图中心点到当前位置
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          this.mapScale = 16;

          // 添加用户位置标记
          this.addUserLocationMarker();

          uni.showToast({
            title: '定位成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '获取位置失败',
            icon: 'none'
          });
        }
      });
    },
    // 添加用户位置标记
    addUserLocationMarker() {
      if (!this.userLocation) return;

      // 创建红色定位点标记
      this.userLocationMarker = {
        id: 'user-location',
        longitude: this.userLocation.longitude,
        latitude: this.userLocation.latitude,
        title: '我的位置',
        iconPath: '/static/icons/user-location.png', // 使用红色定位图标
        width: 36,
        height: 36,
        callout: {
          content: '我的位置',
          color: '#ffffff',
          fontSize: 12,
          borderRadius: 6,
          bgColor: '#ff4d4f', // 红色背景
          padding: 8,
          borderWidth: 1,
          borderColor: '#ff4d4f',
          display: 'ALWAYS',
          textAlign: 'center'
        },
        anchor: {
          x: 0.5,
          y: 1
        }
      };

      // 将用户位置标记添加到标记数组中
      this.updateMarkersWithUserLocation();
    },

    // 更新标记数组，包含用户位置标记
    updateMarkersWithUserLocation() {
      // 移除之前的用户位置标记
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');

      // 添加新的用户位置标记
      if (this.userLocationMarker) {
        this.markers = [...this.markers, this.userLocationMarker];
      }
    },

    // 移除用户位置标记
    removeUserLocationMarker() {
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');
      this.userLocationMarker = null;
    },


    // 关闭导航弹窗
    closeNavigation() {
      this.$refs.container.close();
      // 清理导航状态
      this.isNavigating = false;
      this.polyline = [];
      this.routeDistance = '';
      this.markers = [];
      this.userLocation = null;
      this.userLocationMarker = null;
      this.meterLocationMarker = null;
    }
  }
};
</script>

<style lang="scss" scoped>
// 弹窗样式
.navigation-popup {
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: #1890ff;
    border-radius: 12px 12px 0 0;

    .popup-title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }

    .close-icon {
      width: 30px;
      height: 30px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .close-text {
        color: #ffffff;
        font-size: 20px;
        font-weight: bold;
        line-height: 1;
      }

      &:active {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .map-container {
    width: 90vw;
    height: 70vh;
    position: relative;
    background-color: #f5f5f5;
    border-radius: 0 0 12px 12px;
    overflow: hidden;

    .map {
      width: 100%;
      height: 100%;
      position: relative;
    }

    // 地图控制按钮
    .map-controls {
      position: absolute;
      right: 15px;
      top: 15px;
      z-index: 10;

      .location-btn {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);

        &:active {
          transform: scale(0.95);
          background: rgba(255, 255, 255, 1);
        }

        .location-icon {
          width: 24px;
          height: 24px;
        }
      }
    }


  }

}

// 弹窗整体样式
/deep/ .uni-popup {
  .uni-popup__wrapper {
    .uni-popup__wrapper-box {
      background: transparent;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      max-width: 90vw;
      max-height: 80vh;
    }
  }
}
</style>
