# 项目说明

## 技术框架
采用uniapp框架开发，兼容H5、小程序、App

## 开发环境
windows10

## 目录结构
- api/ - API 请求封装
- 这个目录专门用于存放与后端接口交互的代码，是非常好的实践。
- server.js : 核心的请求封装文件，基于 luch-request 库，统一处理了请求的配置、拦截器（请求前添加 baseURL 和 Token ，响应后处理错误），实现了代码的复用。
- config.js : 存放不同环境（开发、生产）下的 API 地址，实现了配置与代码的分离。
- login.js , device.js , workorder.js 等：按业务模块对 API 进行了拆分，使得接口的管理更加清晰。
- common/ - 公共资源

- 用于存放项目中的公共资源和工具函数。
- http.api.js 和 http.interceptor.js : 这两个文件看起来与 api/ 目录中的功能有些重叠，建议未来可以整合，避免维护两套请求逻辑。
- locales/ : 存放多语言文件，是实现国际化的标准做法。
- mixin.js : 存放 Vue 的混入（mixin），便于在多个组件间共享逻辑。
- components/ - 可复用组件

- 存放全局或可复用的 Vue 组件。将组件按功能（如 etherealwheat-track 、 stepChoice ）分目录存放，结构清晰。
- pages/ - 页面

- 这是项目最重要的目录，包含了所有的业务页面。
- tabs/ : 存放 tabBar 页面，如 home.vue 、 mission.vue 等。
- water/ : 按业务模块（水务）对页面进行归类，这是一个很好的组织方式，使得项目结构一目了然。
- static/ - 静态资源

- 用于存放图片、字体、JSON 文件等静态资源。uni-app 在编译时会将此目录下的文件直接复制到输出目录。
- store/ - Vuex 状态管理

- index.js : Vuex 的入口文件，定义了 state 、 mutations 、 actions 和 getters 。
- $u.mixin.js : 看起来是一个与 uView UI 框架结合的混入，用于简化 Vuex 的使用。
- uni_modules/ - uni-app 插件模块

- 存放符合 uni_modules 规范的插件，便于插件的统一管理和更新。您项目中的 uview-ui 、 z-paging 等都存放在这里。
- utils/ - 工具函数

- 存放项目级的工具函数，例如时间格式化 timeUtils.js 、权限处理 permissionUtils.js 等，提高了代码的复用性。

