<template>
	<view>

		<view class="line">
			<text class="title">{{title}}</text>
			<u--textarea :value="value" v-model="value" class="info" placeholder="请输入内容" autoHeight></u--textarea>


		</view>

	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => ""
			},

		},
		watch: {
			value() {
				console.log("input block change: " + this.value);
				this.$emit('update:value', this.value);
			}
		},
		methods: {


		}
	}
</script>

<style lang="scss" scoped>

</style>