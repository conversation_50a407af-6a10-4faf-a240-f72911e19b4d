// import request from './request.js';


// // 册本详情列表
// export const getWxVolumesReadingList = (params) => request.get('/charge/wx/getWxVolumesReadingList', params)




// // 册本详情状态统计
// export const getVolumesReadingStat = (params) => request.get('/charge//wx/getVolumesReadingStat/'+ params)




// // 抄表册本小区统计列表
// export const getMeterReadingDistrictList = (params) => request.get('/charge/wx/getMeterReadingDistrictList', params)




// // 历史记录
// export const getVolumesHistoryList = (params) => request.get('/charge/wx/getVolumesHistoryList', params)