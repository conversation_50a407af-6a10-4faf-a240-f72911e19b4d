<!-- 事件记录 -->
<template>
  <view class="meter-main">
    <image class="meter-bg" mode="widthFix" src="/static/img/event-bg.png"></image>
    <view class="content-box">
      <view class="top-box">
        <!-- 	<u-search :showAction="false" bgColor="rgba(243,246,252,0.63)" placeholder="搜索"
            v-model="keyword"></u-search> -->
        <!-- <div class="sort">

          <text style="margin-right: 20rpx;">综合排序</text>
          <u-icon name="arrow-down-fill"></u-icon>
        </div> -->
      </view>

      <view class="table-box">
        <scroll-view class="scroll-box" scroll-y="true" style="height: 78vh;">
          <u-empty v-if="!listData"
                   icon="http://cdn.uviewui.com/uview/empty/data.png"
                   mode="data"
          >
          </u-empty>

          <view class="lists">
            <div v-for="(item,index) in listData" :key="index" class="list">
              <div class="list-top">
                <text style="width: 60%;">地址：{{ item.addr || '暂无' }}</text>
                <text style="width: 35%;">时间：{{ item.recordDate || '暂无' }}</text>
              </div>
              <div class="list-bottom">
                <image mode="widthFix" src="/static/img/event.jpg"></image>
                <text>{{ item.recordRemark || '暂无' }}</text>
              </div>
            </div>
            <!-- 	<div class="list">
                <div class="list-top">
                  <text>地址：某某小区某某期几单元几层001</text>
                  <text>时间：2023-10-28  </text>
                </div>
                <div class="list-bottom">
                  <image src="/static/img/event.jpg" mode="widthFix"></image>
                  <text>记录内容记录内容记录内容记录内容记录内容记录内容记录内容，记录内容记录内容，记录内容记录内容记录内容，记录内容记录内容记录内容记录内容记录内容记录内容，记录内容记录内容记录内容。</text>
                </div>
              </div> -->
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>
<script>
import Navbar from './navbar/Navbar.vue'
import * as EventApi from '@/api/meter-manage/event.js'

export default {
  components: {
    Navbar,
  },
  data() {
    return {
      listData: []
    };
  },
  onLoad(options) {
    console.log(options)
    const queryString = options.item; // options.data是从url参数中获取的数据
    const item = JSON.parse(decodeURIComponent(queryString));
    console.log(item)
    // this.getList(item)
  },

  methods: {
    navTo(url, item) {
      const queryString = encodeURIComponent(JSON.stringify(item));
      uni.navigateTo({
        url: url + queryString
      })
    },
    // 获取事件记录列表
    getList(item) {
      const app = this
      EventApi.getWxItemRecordList({
        meterVolumesNum: item.meterVolumesNum
      }).then(res => {
        console.log(res)
        // res.data.
        app.listData = res.rows

      })
    },
  },
};
</script>
<style lang="scss" scoped>
.meter-main {
  overflow-y: hidden;
  height: 100vh;
  background-color: #EFF3F9;

  .meter-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 40;
  }

  .content-box {
    position: relative;
    z-index: 42;


    .top-box {
      padding: 24rpx;
      height: 286rpx;
      box-sizing: border-box;

      .sort {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 188rpx;
        height: 56rpx;
        background: #FFFFFF;
        opacity: 0.66;
        border-radius: 8rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #2F3849;
        line-height: 30rpx;
        margin-top: 60rpx;
      }
    }

    .table-box {
      position: relative;


      width: 100%;

      .scroll-box {
        position: absolute;
        background-color: #EFF3F9;
        top: -30px;
        left: 0;
        z-index: 77;
        padding: 0 22rpx;
        //
        box-sizing: border-box;
      }

      .lists {
        min-height: 100%;
        background: #FFFFFF;
        box-shadow: 0rpx 6rpx 9rpx 1rpx rgba(107, 146, 214, 0.26);
        border-radius: 16rpx 16rpx 0rpx 0rpx;
        padding: 32rpx;
        box-sizing: border-box;

        .list {
          width: 100%;
          padding-bottom: 18rpx;
          border-bottom: 2rpx solid #ececec;
          margin-top: 20rpx;

          .list-top {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 24rpx;
            font-weight: 400;
            color: #1172FD;
            line-height: 30rpx;

          }

          .list-bottom {
            margin-top: 20rpx;
            width: 100%;
            border: 2px solid #F1F3F2;
            background: linear-gradient(45deg, #EFF7FD 0%, #F2F7FD 100%);
            border-radius: 12rpx;
            display: flex;
            align-items: flex-start;
            padding: 20rpx 32rpx;
            box-sizing: border-box;

            image {
              width: 206rpx;
              height: 186rpx;
              margin-right: 20rpx;


            }

            text {
              flex: 1;
              font-size: 22rpx;
              font-weight: 400;
              color: #647398;
              line-height: 32rpx;
            }
          }

          &:nth-child(1) {
            margin-top: 0;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }


    }
  }
}
</style>
