<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="nav-bar">
				<uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
				<text class="title">{{ deviceName }}</text>

			</view>
		</view>
		<view class="content">
			<view class="chart-section">
				<view class="chart-container">
					<qiun-data-charts type="line" :opts="chartOpts" :chartData="chartData" />
				</view>
			</view>
			<view class="table-section">
				<view class="table-title-bar">
					<text class="table-title">{{ paramName }}</text>
					<view class="date-picker" @click="showDatePicker = true">
						<text>{{ selectedDate }}</text>
						<uni-icons type="calendar" size="16" color="#999"></uni-icons>
					</view>
				</view>
				<view class="table-container">
					<view class="table-header">
						<view class="table-cell" v-for="(header, i) in headers" :key="i"><rich-text
								:nodes="header.text"></rich-text></view>
					</view>
					<scroll-view scroll-y class="table-body">
						<view class="table-row" v-for="(row, index) in tableData" :key="index">
							<view class="table-cell">{{ row.time }}</view>
							<view class="table-cell">{{ row.value }}</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
		<u-datetime-picker :show="showDatePicker" v-model="currentDate" mode="date" @confirm="onDateConfirm"
			@cancel="showDatePicker = false"></u-datetime-picker>
	</view>
</template>

<script>
import { getHistoryData } from "../../../api/history.js";
import QiunDataCharts from "../../../uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue";
import { formateDate, formatDateTime } from '../../../utils/timeUtils.js';

export default {
	props: ['name', 'pname', 'did'],
	components: { QiunDataCharts },
	data() {
		return {
			currentDate: new Date().toISOString(),
			showDatePicker: false,
			deviceId: '',
			deviceName: '',
			paramId: '',
			paramName: '',
			chartData: {},
			chartOpts: {
				color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
				padding: [15, 10, 0, 15],
				dataLabel: false,
				dataPointShape: false,
				legend: {},
				xAxis: {
					disableGrid: true,
					rotateLabel: true,
					rotateAngle: 45,
					labelCount: 10
				},
				yAxis: {
					gridType: "dash",
					dashLength: 2
				},
				extra: {
					line: {
						type: "curve"
					},
					tooltip: {
						show: false
					}
				}
			},
			selectedDate: formateDate(new Date()),
			headers: [
				{ text: '时间' },
				{ text: '值' },
			],
			tableData: []
		};
	},
	async onLoad(options) {
		this.deviceId = options.deviceId;
		this.deviceName = options.deviceName || '设备名称';
		this.paramName = options.paramName || '历史数据';
		this.paramId = options.paramId;
		this.getHistory(() => {
			this.prepareChartData();
		});
	},
	onMounted() { },
	methods: {
		goBack() {
			uni.navigateBack();
		},
		onDateConfirm(e) {
			const date = new Date(e.value);
			this.selectedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			this.showDatePicker = false;
			// a faked data change
			// this.tableData = this.tableData.map(item => {
			// 	return {
			// 		...item,
			// 		value: (Math.random() * 10 + 405).toFixed(3),
			// 	}
			// });
			this.getHistory(() => {
				this.prepareChartData();
			});
		},
		prepareChartData() {
			let categories = this.tableData.map(item => item.time);
			let series = [
				{
					name: this.paramName,
					data: this.tableData.map(item => parseFloat(item.value))
				}
			];
			this.chartData = {
				categories,
				series
			};
			console.log("chartData", this.chartData);
		},
		async getHistory(callback) {
			const params = {
				paramIds: { [this.deviceId]: [this.paramId] },
				startDate: formatDateTime(new Date(this.selectedDate), 'yyyy-MM-dd 00:00'),
				endDate: formatDateTime(new Date(this.selectedDate), 'yyyy-MM-dd 23:55'),
				interval: 60
			};
			const res = await getHistoryData(params);
			if (res.code === 200 && res.data.length > 0) {
				console.log("res", res);
				for (let i = 0; i < res.data.length; i++) {
					for (let j = 0; j < res.data[i].tsModelList.length; j++) {
						let dataList = [];
						const historyList = res.data[i].tsModelList[j].historyList;
						for (let k = 0; k < historyList.length; k++) {
							if (historyList.length > 0) {
								dataList.push({ "time": historyList[k].dateTime.substring(11, 16), "value": historyList[k].value });
							}
						}
						this.tableData = dataList;
						if (callback) {
							callback();
						}
					}
				}
				// chart.value.setOption(options, true);
			} else {
				console.error("Failed to fetch data");
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.chart-section {
	background-color: #fff;
	padding: 10px;
	border-radius: 8px;
}

.legend-bar {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	margin-bottom: 10px;
}

.legend-item {
	display: flex;
	align-items: center;
	margin-right: 15px;
	font-size: 12px;
}

.color-box {
	width: 10px;
	height: 10px;
	margin-right: 5px;
}

.blue {
	background-color: #2979ff;
}

.green {
	background-color: #00c853;
}

.orange {
	background-color: #ff9100;
}

.purple {
	background-color: #d500f9;
}

.table-section {
	background-color: #fff;
	padding: 10px;
	border-radius: 8px;
}

.table-title-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
}

.table-title {
	margin-left: 10px;
	font-size: 16px;
	font-weight: bold;
}

.date-picker {
	display: flex;
	align-items: center;
	border: 1px solid #dcdfe6;
	padding: 5px 10px;
	border-radius: 4px;
	font-size: 14px;
}

.date-picker text {
	margin-right: 5px;
}

.table-header .table-cell {
	font-weight: normal;
	background-color: #fff;
	white-space: pre-wrap;
	line-height: 1.4;
}

.table-cell {
	border-bottom: 1px solid #e0e0e0;
}

.table-row:last-child .table-cell {
	border-bottom: none;
}

.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: #1890ff;
	padding-top: var(--status-bar-height);
	// 添加阴影效果
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;

		.title {
			width: 100%;
			color: #fff;
			font-size: 18px;
			font-weight: 500;
			text-align: center;
		}
	}
}

.content {
	padding: 10px;
}

.chart-container {
	height: 250px;
	background-color: #fff;
	margin-bottom: 10px;
	border-radius: 8px;
}

.table-container {
	background-color: #fff;
	border-radius: 8px;
	height: calc(100vh - 460px);
}

.table-header {
	display: flex;
	background-color: #f0f0f0;
	font-weight: bold;
}

.table-row {
	display: flex;
}

.table-cell {
	flex: 1;
	padding: 10px;
	text-align: center;
	border: 1px solid #e0e0e0;
}

.table-body {
	height: calc(100% - 50px);
}
</style>