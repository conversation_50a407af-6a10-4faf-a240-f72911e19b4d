// 工单接口返回处理
import {
	proxy
} from './config.js'
import {
	request
} from './server.js'

// var pre = ''
var pre = '/potevio-mro'

export const orderList =
	function(repairOrMaintain, queryHis, isFinish, pageNum, pageSize) {
		var url;
		if(isFinish){
			url = pre+'/workorder/myWorkorderListPaging?repairOrMaintain=' +
				repairOrMaintain + "&queryHis=" + queryHis +
				"&isFinish=" + isFinish +
				"&pageNum=" + pageNum +
				"&pageSize=" + pageSize
		}else{
			url = pre+'/workorder/myWorkorderListPaging?repairOrMaintain=' +
				repairOrMaintain + "&queryHis=" + queryHis +
				"&pageNum=" + pageNum +
				"&pageSize=" + pageSize
		}
	

		// console.log("orderList："+url);
		return request.get(url)
			.then(data => {
				return data.data
			})
	}
export const getHistoryActivities =
	function(pkId) {

		// var url = '/workorder/getHistoryActivities?pkId=' + pkId
		var url = pre+'/workorder/getHistoryActivities?pkId=' + pkId

		// console.log(url);
		return request.get(url)
			.then(data => {
				return data.data
			})
	}
//维修详情
export const getDetailRepair =
	function(pkId) {
		var url = pre+'/workorder/getDetailRepair?pkId=' + pkId
		// var url = '/workorder/getDetailRepair?pkId=' + pkId

		// console.log(url);
		return request.get(url)
			.then(data => {
				return data.data
			})
	}

//刪除文件图片

export const deleteFileById =
	function(pkId) {
		var url =  pre+'/ram-file/deleteById?pkId='+pkId;
		// var url = '/ram-file/deleteById?pkId='+pkId;
		// console.log(url);
		return request.post(url)
			.then(data => {
				return data.data
			})
	}

//保养详情
export const getDetailMaintain =
	function(pkId) {
		var url =  pre+'/workorder/getDetailMaintain?pkId=' + pkId
		// var url = '/potevio-mro/workorder/getDetailMaintain?pkId=' + pkId

		// console.log(url);
		return request.get(url)
			.then(data => {
				return data.data
			})
	}
//获取图片
export const getFile =
	function(pkId) {
			uni.setStorageSync('isDevice', false);
		// var url = '/potevio-mro/ram-file/queryListByObjectId?objectId=' + pkId
		var url =  pre+'/ram-file/queryListByObjectId?objectId=' + pkId

		console.log(url);
		return request.get(url)
			.then(data => {
				return data.data
			})
	}
//通用表单接口
export const submit =
	function(params) {
		// var url = '/potevio-mro/form/submit'
		var url =  pre+'/workorder/submit'

		return request.post(url, params)
			.then(data => {
				return data.data
			})
	}
//保养
//分派
// export const maintainWorkorderAssign =
// 	function(params) {
// 		var url =  pre+'/workorder/maintainWorkorderAssign'

// 		return request.post(url, params)
// 			.then(data => {
// 				return data.data
// 			})
// 	}
//认领
export const maintainWorkorderClaim =
	function(params) {
		var url =  pre+'/workorder/maintainWorkorderClaim'
		// var url = '/potevio-mro/workorder/maintainWorkorderClaim'

		return request.post(url, params)
			.then(data => {
				return data.data
			})
	}
//保养项查询、巡检项
export const checkItemPaging =
	function(workorderId, pageNum, pageSize) {
		var url = pre+ '/workorder-item/queryListPagingItem?workorderId=' + workorderId + "&pageNum=" + pageNum +
			"&pageSize=" + pageSize
			// var url = '/potevio-mro/workorder-item/queryListPagingItem?workorderId=' + workorderId + "&pageNum=" + pageNum +
			// "&pageSize=" + pageSize

		return request.get(url)
			.then(data => {
				return data.data
			})
	}
	//巡检详情
	export const getDetailInspection =
		function(pkId) {
			var url = pre+'/workorder/getDetailInspection?pkId=' + pkId
			// var url = '/workorder/getDetailRepair?pkId=' + pkId
	
			// console.log(url);
			return request.get(url)
				.then(data => {
					return data.data
				})
		}
	
	//送检详情
	export const getDetailVerification=
		function(pkId) {
			var url = pre+'/workorder/getDetailVerification?pkId=' + pkId
	
			// console.log(url);
			return request.get(url)
				.then(data => {
					return data.data
				})
		}
	