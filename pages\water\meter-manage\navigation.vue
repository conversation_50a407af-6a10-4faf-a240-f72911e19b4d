<template>
  <uni-popup ref="container" class="container">

    <!-- 地图容器 -->
    <view class="map-container">
      <!--
        markers 多个标记地点
       -->
      <map id="amapContainers" :enable-3D="true" :enable-overlooking="true" :enable-poi="true"
           :enable-rotate="true" :enable-scroll="true" :enable-zoom="true" :latitude="mapCenter.latitude" :longitude="mapCenter.longitude"
           :markers="markers" :polyline="polyline"
           :scale="mapScale" :show-location="true" :zIndex="1" class="map" @markertap="onMarkerTap"
           @regionchange="onRegionChange" @tap="onMapTap">
      </map>

      <!-- 地图控制按钮 -->
      <cover-view class="map-controls">
        <cover-image class="location-btn" src="../../../static/icon/location.png"
                     @click="centerToLocation"></cover-image>
        <cover-view class="close-btn" @click="closeNavigation">
          <cover-text class="close-text">×</cover-text>
        </cover-view>
      </cover-view>

      <!-- 导航控制面板 -->
      <cover-view v-if="isNavigating || polyline.length > 0" class="navigation-panel">
        <cover-view class="nav-buttons">
          <cover-view v-if="!isNavigating" class="nav-btn start-nav" @click="startNavigation">
            <cover-text class="nav-btn-text">开始导航</cover-text>
          </cover-view>
          <cover-view class="nav-btn replan" @click="replanRoute">
            <cover-text class="nav-btn-text">重新规划</cover-text>
          </cover-view>
          <cover-view class="nav-btn clear" @click="clearRoute">
            <cover-text class="nav-btn-text">清除路线</cover-text>
          </cover-view>
        </cover-view>
      </cover-view>
    </view>
  </uni-popup>
</template>

<script>
import {getRealtimeData} from '@/api/realtime.js';
import {formatDateTime} from '../../../utils/timeUtils.js';
import {wgs84ToGcj02} from '@/utils/coordinateUtils.js';

export default {
  data() {
    return {
      // 地图中心点（山西太原坐标）
      mapCenter: {
        longitude: 112.53,
        latitude: 37.87
      },
      // 地图缩放级别
      mapScale: 13,
      // 标记点数组
      markers: [],
      // 实时数据
      realtimeData: [],
      // 地图上下文
      mapContext: null,
      // 用户位置
      userLocation: null,
      // 用户位置标记
      userLocationMarker: null,
      // 水表位置标记
      meterLocationMarker: null,
      // 路线规划相关
      polyline: [],
      // 水表数据
      fjdsj: {},
      // 导航状态
      isNavigating: false
    };
  },

  async onLoad() {
    // 初始化地图上下文
    this.initMapContext();
    this.getCurrentLocation();
  },

  onReady() {
    // 页面渲染完成后初始化地图上下文
    this.initMapContext();
  },

  onUnload() {
    // 清理资源
    this.mapContext = null;
  },

  methods: {
    // 初始化导航地图
    async initNavigationMap() {
      try {
        uni.showLoading({
          title: '正在获取位置...'
        });

        // 获取抄表员当前位置
        await this.getCurrentLocationForNavigation();

        // 创建抄表员和水表位置标记
        this.createNavigationMarkers();

        // 规划路线
        await this.planRoute();

        // 调整地图视野以显示完整路线
        this.adjustMapView();

        // 打开弹窗显示地图
        this.$refs.container.open();

        uni.hideLoading();
        uni.showToast({
          title: '导航准备完成',
          icon: 'success'
        });

      } catch (error) {
        console.error('初始化导航失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '导航初始化失败',
          icon: 'none'
        });
      }
    },

    // 获取抄表员当前位置（用于导航）
    getCurrentLocationForNavigation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            this.userLocation = {
              latitude: res.latitude,
              longitude: res.longitude
            };
            console.log('获取到抄表员位置:', this.userLocation);
            resolve(res);
          },
          fail: (err) => {
            console.error('获取位置失败:', err);
            reject(err);
          }
        });
      });
    },

    // 创建导航标记点（抄表员位置和水表位置）
    createNavigationMarkers() {
      const markers = [];

      // 抄表员位置标记
      if (this.userLocation) {
        this.userLocationMarker = {
          id: 'user-location',
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude,
          title: '抄表员位置',
          iconPath: '/static/icons/user-location.png',
          width: 36,
          height: 36,
          callout: {
            content: '抄表员当前位置',
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#1890ff',
            padding: 8,
            display: 'ALWAYS'
          }
        };
        markers.push(this.userLocationMarker);
      }

      // 水表位置标记
      if (this.fjdsj.meterLongitude && this.fjdsj.meterLatitude) {
        this.meterLocationMarker = {
          id: 'meter-location',
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude),
          title: '水表位置',
          iconPath: '/static/icons/marker-normal.png',
          width: 36,
          height: 36,
          callout: {
            content: `水表位置\n${this.fjdsj.meterAddress || ''}`,
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#52c41a',
            padding: 8,
            display: 'ALWAYS'
          }
        };
        markers.push(this.meterLocationMarker);
      }

      this.markers = markers;
      console.log('创建导航标记点:', markers);
    },

    // 规划路线
    async planRoute() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        console.error('缺少位置信息，无法规划路线');
        return;
      }

      try {
        // 创建简单的直线路线（实际项目中可以调用高德地图路线规划API）
        const startPoint = {
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude
        };

        const endPoint = {
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude)
        };

        // 计算距离
        const distance = this.calculateDistance(startPoint, endPoint);
        console.log(`路线距离: ${distance.toFixed(2)}米`);

        // 创建路线polyline
        this.polyline = [{
          points: [startPoint, endPoint],
          color: '#1890ff',
          width: 6,
          dottedLine: false,
          arrowLine: true,
          borderColor: '#ffffff',
          borderWidth: 2
        }];

        // 显示距离信息
        uni.showToast({
          title: `距离约${distance > 1000 ? (distance / 1000).toFixed(1) + '公里' : distance.toFixed(0) + '米'}`,
          icon: 'none',
          duration: 3000
        });

      } catch (error) {
        console.error('路线规划失败:', error);
        // 即使路线规划失败，也创建一个简单的直线
        this.createSimpleRoute();
      }
    },

    // 创建简单路线（备用方案）
    createSimpleRoute() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        return;
      }

      this.polyline = [{
        points: [
          {
            longitude: this.userLocation.longitude,
            latitude: this.userLocation.latitude
          },
          {
            longitude: parseFloat(this.fjdsj.meterLongitude),
            latitude: parseFloat(this.fjdsj.meterLatitude)
          }
        ],
        color: '#1890ff',
        width: 4,
        dottedLine: true
      }];
    },

    // 计算两点间距离（米）
    calculateDistance(point1, point2) {
      const R = 6371000; // 地球半径（米）
      const lat1Rad = point1.latitude * Math.PI / 180;
      const lat2Rad = point2.latitude * Math.PI / 180;
      const deltaLatRad = (point2.latitude - point1.latitude) * Math.PI / 180;
      const deltaLngRad = (point2.longitude - point1.longitude) * Math.PI / 180;

      const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
          Math.cos(lat1Rad) * Math.cos(lat2Rad) *
          Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      return R * c;
    },

    // 调整地图视野以显示完整路线
    adjustMapView() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        return;
      }

      const userLng = this.userLocation.longitude;
      const userLat = this.userLocation.latitude;
      const meterLng = parseFloat(this.fjdsj.meterLongitude);
      const meterLat = parseFloat(this.fjdsj.meterLatitude);

      // 计算中心点
      const centerLng = (userLng + meterLng) / 2;
      const centerLat = (userLat + meterLat) / 2;

      // 计算合适的缩放级别
      const lngDiff = Math.abs(userLng - meterLng);
      const latDiff = Math.abs(userLat - meterLat);
      const maxDiff = Math.max(lngDiff, latDiff);

      let scale = 16;
      if (maxDiff > 0.01) scale = 14;
      if (maxDiff > 0.02) scale = 13;
      if (maxDiff > 0.05) scale = 12;
      if (maxDiff > 0.1) scale = 11;

      // 更新地图中心和缩放级别
      this.mapCenter = {
        longitude: centerLng,
        latitude: centerLat
      };
      this.mapScale = scale;

      console.log('调整地图视野:', this.mapCenter, 'scale:', scale);
    },

    // 开始导航
    startNavigation() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        uni.showToast({
          title: '位置信息不完整',
          icon: 'none'
        });
        return;
      }

      this.isNavigating = true;

      // 可以在这里添加实时位置更新逻辑
      uni.showModal({
        title: '开始导航',
        content: '是否要打开外部地图应用进行导航？',
        success: (res) => {
          if (res.confirm) {
            this.openExternalMap();
          }
        }
      });
    },

    // 打开外部地图应用
    openExternalMap() {
      const meterLng = this.fjdsj.meterLongitude;
      const meterLat = this.fjdsj.meterLatitude;
      const meterName = this.fjdsj.meterAddress || '水表位置';

      // #ifdef APP-PLUS
      // 构建高德地图URL
      const amapUrl = `amaps://route/plan/?sid=BGVIS1&slat=${this.userLocation.latitude}&slon=${this.userLocation.longitude}&sname=当前位置&did=BGVIS2&dlat=${meterLat}&dlon=${meterLng}&dname=${encodeURIComponent(meterName)}&dev=0&t=0`;

      // 构建百度地图URL（备用）
      const baiduUrl = `baidumap://map/direction?origin=${this.userLocation.latitude},${this.userLocation.longitude}&destination=${meterLat},${meterLng}&mode=walking`;

      // 尝试打开高德地图
      plus.runtime.openURL(amapUrl, () => {
        console.log('打开高德地图失败，尝试百度地图');
        // 如果高德地图打开失败，尝试百度地图
        plus.runtime.openURL(baiduUrl, () => {
          console.log('打开百度地图也失败');
          uni.showToast({
            title: '请安装地图应用',
            icon: 'none'
          });
        });
      });
      // #endif

      // #ifdef H5
      // H5环境下打开网页版地图
      const webUrl = `https://uri.amap.com/navigation?from=${this.userLocation.longitude},${this.userLocation.latitude},当前位置&to=${meterLng},${meterLat},${encodeURIComponent(meterName)}&mode=walking`;
      window.open(webUrl, '_blank');
      // #endif
    },

    // 清除路线
    clearRoute() {
      this.polyline = [];
      this.isNavigating = false;
      uni.showToast({
        title: '已清除路线',
        icon: 'success'
      });
    },

    // 重新规划路线
    async replanRoute() {
      uni.showLoading({
        title: '重新规划中...'
      });

      try {
        await this.getCurrentLocationForNavigation();
        this.createNavigationMarkers();
        await this.planRoute();
        this.adjustMapView();

        uni.hideLoading();
        uni.showToast({
          title: '路线已更新',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '重新规划失败',
          icon: 'none'
        });
      }
    },
    openEvent(data) {
      console.log('接收到水表数据:', data);
      this.fjdsj = data;

      // 检查水表坐标是否存在
      if (!data.meterLongitude || !data.meterLatitude) {
        uni.showToast({
          title: '水表位置信息不完整',
          icon: 'none'
        });
        return;
      }

      // 获取抄表员当前位置并显示导航
      this.initNavigationMap();
    },
    // 初始化地图上下文
    initMapContext() {
      // #ifdef APP-PLUS
      this.mapContext = uni.createMapContext('amapContainers', this);
      console.log('高德地图上下文初始化成功');
      // #endif
    },

    // 获取实时数据
    async loadRealtimeData() {
      try {
        const res = await getRealtimeData({pageNum: 1, pageSize: 500});
        // 确保 res 是数组
        const dataArray = Array.isArray(res) ? res : (res && Array.isArray(res.data) ? res.data : []);

        this.realtimeData = dataArray.filter(item => item.deviceExternalInfo.deviceLocation && item.deviceExternalInfo.deviceLocation !== "null").map(item => {
          const deviceLocation = JSON.parse(item.deviceExternalInfo.deviceLocation);
          return {
            id: item.id,
            name: item.name,
            longitude: deviceLocation.longitude,
            latitude: deviceLocation.latitude,
            realList: item.realList,
            status: item.state,
            type: item.type || 'normal',
            time: item.realList.length ? formatDateTime(new Date(item.realList[0].ts)) : ''
          }
        });

        if (dataArray.length > 0) {
          this.createMarkersFromData(this.realtimeData);
        } else {
          // 如果没有实时数据，添加示例标记点
          this.createSampleMarkers();
        }
      } catch (error) {
        console.error('获取实时数据失败:', error);
        // 添加示例标记点
        this.createSampleMarkers();
      }
    },

    // 根据实时数据创建标记点
    createMarkersFromData(dataArray) {
      const newMarkers = dataArray.map((item, index) => {
        // 如果数据来源是WGS84坐标，需要转换
        const longitude = item.lng || item.longitude || (112.53 + (Math.random() - 0.5) * 0.1);
        const latitude = item.lat || item.latitude || (37.87 + (Math.random() - 0.5) * 0.1);

        // 检查是否需要坐标转换（根据数据来源判断）
        if (this.needCoordinateConversion(item)) {
          const converted = wgs84ToGcj02(longitude, latitude);
          longitude = converted.lng;
          latitude = converted.lat;
        }

        return {
          id: `marker_${index}`,
          longitude: longitude,
          latitude: latitude,
          title: item.name || `设备${index + 1}`,
          iconPath: this.getMarkerIcon(item.type || item.status),
          width: 32,
          height: 32,
          callout: {
            content: item.name || `设备${index + 1}`,
            color: '#333',
            fontSize: 14,
            borderRadius: 8,
            bgColor: '#ffffff',
            padding: 8,
            display: 'BYCLICK'
          },
          data: item // 添加 data 属性，包含完整的设备信息
        };
      });

      this.markers = newMarkers;
    },

    // 判断是否需要坐标转换
    needCoordinateConversion(item) {
      // 根据数据来源或标识判断是否需要转换
      // 例如：如果数据来源标识为'gps'或'wgs84'，则需要转换
      return item.coordinateSystem === 'wgs84' || item.source === 'gps';
    },

    createSampleMarkers() {
      const sampleData = [
        {
          name: '南方风机',
          longitude: 112.5264,
          latitude: 37.8736,
          pressure: '408.930 kPa',
          status: 'normal'
        },
        {
          name: '生态空调',
          longitude: 112.5384,
          latitude: 37.8756,
          pressure: '408.930 kPa',
          status: 'normal'
        },
        {
          name: '信华金属',
          longitude: 112.5444,
          latitude: 37.8696,
          pressure: '408.930 kPa',
          status: 'warning'
        },
        {
          name: '东云天下',
          longitude: 112.5504,
          latitude: 37.8616,
          pressure: '408.930 kPa',
          status: 'error'
        }
      ];

      const newMarkers = sampleData.map((item, index) => {
        // 根据状态选择不同的图标
        let iconPath = '/static/icons/marker-normal.png';
        let bgColor = '#1890ff';

        switch (item.status) {
          case 'warning':
            iconPath = '/static/icons/marker-warning.png';
            bgColor = '#fa8c16';
            break;
          case 'error':
            iconPath = '/static/icons/marker-error.png';
            bgColor = '#ff4d4f';
            break;
          default:
            iconPath = '/static/icons/marker-normal.png';
            bgColor = '#52c41a';
        }

        return {
          id: index,
          longitude: item.longitude,
          latitude: item.latitude,
          title: item.name,
          iconPath: iconPath,
          width: 32,
          height: 32,
          callout: {
            content: `${item.name}\n压力: ${item.pressure}`,
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: bgColor,
            padding: 10,
            display: 'BYCLICK'
          },
          data: item
        };
      });
      this.markers = newMarkers;
    },

    // 标记点点击事件
    // 标记点点击事件
    onMarkerTap(e) {
      console.log('标记点被点击:', e.detail); // 添加调试日志
      const markerId = e.detail.markerId;
      const marker = this.markers.find(m => m.id === markerId);

      console.log('找到的标记点:', marker); // 添加调试日志

      if (marker) {
        if (marker.data) {
          // 显示详细信息弹窗
          this.showMarkerDetail(marker.data);
        } else {
          console.log('标记点没有 data 属性');
          // 可以显示基本信息
          uni.showModal({
            title: marker.title || '设备信息',
            content: '暂无详细数据',
            showCancel: false
          });
        }
      } else {
        console.log('未找到对应的标记点');
      }
    },

    // 显示标记点详细信息（简化版）
    showMarkerDetail(data) {
      let content = `\n`;

      if (data.realList && Array.isArray(data.realList) && data.realList.length > 0) {
        //content += `\n实时数据:\n`;
        const latestData = formatDateTime(new Date(data.realList[0].ts));
        content += `数据更新时间: ${latestData}\n\n`;

        data.realList.forEach(param => {
          // 使用特殊字符来标识需要高亮的值
          content += `${param.name}: 【${param.value}】 ${param.unit || ''}\n`;
        });
      } else {
        content += `\n暂无实时数据`;
      }

      if (data.status) {
        content += `\n状态: ${this.getStatusText(data.status)}`;
      }

      uni.showModal({
        title: `${data.name || '未知设备'}`,
        content: content,
        showCancel: true,
        cancelText: '关闭'
      });
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'normal':
          return '正常';
        case 'warning':
          return '警告';
        case 'error':
          return '故障';
        default:
          return '未知';
      }
    },

    // 获取标记图标
    getMarkerIcon(type) {
      // 根据设备类型或状态返回对应的图标
      switch (type) {
        case 'normal':
          return '/static/icons/marker-normal.png';
        case 'warning':
          return '/static/icons/marker-warning.png';
        case 'error':
          return '/static/icons/marker-error.png';
        case 'pressure': // 压力设备
          return '/static/icons/marker-normal.png';
        case 'flow': // 流量设备
          return '/static/icons/marker-normal.png';
        case 'level': // 液位设备
          return '/static/icons/marker-normal.png';
        default:
          return '/static/icons/marker-normal.png';
      }
    },

    // 地图区域变化事件
    onRegionChange(e) {
      if (e.type === 'end') {
        console.log('地图区域变化:', e.detail);
      }
    },

    // 地图点击事件
    onMapTap(e) {
      console.log('地图点击:', e.detail);
    },

    // 定位到当前位置
    // 定位到当前位置
    centerToLocation() {
      console.log("这里是否执行");
      if (this.userLocation) {
        // 更新地图中心点
        this.mapCenter = {
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude
        };
        // 放大地图层级
        this.mapScale = 17;
        // 添加红色定位点标记
        this.addUserLocationMarker();
        console.log('定位到当前位置:', this.mapCenter);
        this.mapContext.moveToLocation();
      } else {
        this.getCurrentLocation();
        uni.showToast({
          title: '正在获取位置...',
          icon: 'loading'
        });
      }
    },
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };

          // 更新地图中心点到当前位置
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          this.mapScale = 16;

          // 添加用户位置标记
          this.addUserLocationMarker();

          uni.showToast({
            title: '定位成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '获取位置失败',
            icon: 'none'
          });
        }
      });
    },
    // 添加用户位置标记
    addUserLocationMarker() {
      if (!this.userLocation) return;

      // 创建红色定位点标记
      this.userLocationMarker = {
        id: 'user-location',
        longitude: this.userLocation.longitude,
        latitude: this.userLocation.latitude,
        title: '我的位置',
        iconPath: '/static/icons/user-location.png', // 使用红色定位图标
        width: 36,
        height: 36,
        callout: {
          content: '我的位置',
          color: '#ffffff',
          fontSize: 12,
          borderRadius: 6,
          bgColor: '#ff4d4f', // 红色背景
          padding: 8,
          borderWidth: 1,
          borderColor: '#ff4d4f',
          display: 'ALWAYS',
          textAlign: 'center'
        },
        anchor: {
          x: 0.5,
          y: 1
        }
      };

      // 将用户位置标记添加到标记数组中
      this.updateMarkersWithUserLocation();
    },

    // 更新标记数组，包含用户位置标记
    updateMarkersWithUserLocation() {
      // 移除之前的用户位置标记
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');

      // 添加新的用户位置标记
      if (this.userLocationMarker) {
        this.markers = [...this.markers, this.userLocationMarker];
      }
    },

    // 移除用户位置标记
    removeUserLocationMarker() {
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');
      this.userLocationMarker = null;
    },

    // 刷新标记点
    async refreshMarkers() {
      uni.showLoading({
        title: '刷新中...'
      });

      try {
        await this.loadRealtimeData();
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 刷新任务（导航栏按钮）
    refreshTasks() {
      this.refreshMarkers();
    },

    // 跳转到历史数据页面
    goToHistory(data) {
      if (data.id) {
        uni.navigateTo({
          url: `/pages/water/realtime-monitor/historyData?deviceId=${deviceId}&deviceName=${paramId}`
        });
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 关闭导航弹窗
    closeNavigation() {
      this.$refs.container.close();
      // 清理导航状态
      this.isNavigating = false;
      this.polyline = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;

  .header {
    background: #1890ff;
    padding-top: var(--status-bar-height);

    .nav-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;

      .title {
        width: 100%;
        color: #fff;
        font-size: 18px;
        font-weight: 500;
        text-align: center;
      }
    }
  }

  .map-container {
    // width: 100%;
    // height: calc(100vh - 75px);
    width: 500rpx;
    height: 800rpx;
    position: relative;
    // background-color: pink;

    .map {
      width: 100%;
      height: 100%;
      position: relative;
    }

    // 地图控制按钮
    .map-controls {
      position: absolute;
      right: 15px;
      bottom: 80px;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .location-btn {
        width: 29px;
        height: 29px;
        background: #fff;
        border-radius: 30%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }

        .location-icon {
          width: 20px;
          height: 20px;
        }
      }

      .close-btn {
        width: 29px;
        height: 29px;
        background: #ff4d4f;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
        }

        .close-text {
          color: #ffffff;
          font-size: 18px;
          font-weight: bold;
          line-height: 1;
        }
      }
    }

    // 导航控制面板
    .navigation-panel {
      position: absolute;
      bottom: 20px;
      left: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 12px;
      padding: 15px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      .nav-buttons {
        display: flex;
        justify-content: space-around;
        align-items: center;

        .nav-btn {
          padding: 8px 16px;
          border-radius: 20px;
          min-width: 80px;
          text-align: center;
          font-size: 12px;
          color: #ffffff;
          transition: all 0.2s ease;

          &.start-nav {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
          }

          &.replan {
            background: linear-gradient(135deg, #52c41a, #73d13d);
          }

          &.clear {
            background: linear-gradient(135deg, #ff4d4f, #ff7875);
          }

          &:active {
            transform: scale(0.95);
          }

          .nav-btn-text {
            color: #ffffff;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }
  }

}
</style>
