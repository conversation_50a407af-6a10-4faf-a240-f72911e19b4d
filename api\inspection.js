import { request } from './server.js'

const serverUrl = "http://59.49.48.115:19714/api";

//获取当前用户id
export const getUserId =
	function() {
		return request.get(`/auth/user/id`)
			.then(data => {
				return data.data
			})
	}

//获取当前用户的巡检任务列表
export const getInspectionTaskList =
	function(params) {
		return request.post(`${serverUrl}/inspection/task/page`,params)
			.then(data => {
				return data.data
			})
	}

//查看巡检任务详情
export const getInspectionTaskDetail =
	function(taskId) {
		return request.get(`${serverUrl}/inspection/task/${taskId}`)
			.then(data => {
				return data.data
			})
	}

//巡检点就位
export const inspectionPointArrival =
	function(params) {
		return request.post(`${serverUrl}/inspection/task/inplace`, params)
			.then(data => {
				return data.data
			})
	}
