<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			// 检查用户登录状态
			const token = uni.getStorageSync('token')
			const username = uni.getStorageSync('username')
			
			// 如果已登录，跳转到主页面
			if (token && username) {
				uni.reLaunch({
					url: '/pages/tabs/main'
				})
			}
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	@import "uni_modules/uview-ui/index.scss";

	page {
		display: flex;
		flex-direction: column;
		// height: 100%;
		height: calc(100vh - var(--window-top));
		background-color: $bg-color;
	}

	.btnText {
		width: 95%;
		padding-right: 10rpx;
		text-align: end;
		background-color: transparent;
	}

	.btnImg {
		width: 5%;
		height: 10px;
		align-self: center;
		background-color: #ffffff;
	}

	.wrap {
		display: flex;
		flex-direction: column;
		height: calc(100vh - var(--window-top));
		width: 100%;
	}


	.scrollView {
		height: calc(100vh - var(--window-top));
		width: 100%;
		background-color: $bg-color;
	}

	.block {
		display: flex;
		flex-direction: column;
		margin-top: $info-textsize;
		background-color: white;
		margin-top: 40rpx;
		margin-left: 30rpx;
		margin-right: 30rpx;
		margin-bottom: 24rpx;
		height: auto;
		border-radius: 20rpx;
	}

	.login_parting_line_hori {
		//横向分割线
		background: $divider_color;
		height: 1rpx;
		width: 90%;
		margin-left: 17rpx;
		margin-right: 17rpx;
	}

	.parting_line_hori {
		//横向分割线
		background: $divider_color;
		height: 1rpx;
		width: 95%;
		margin-left: 17rpx;
		margin-right: 17rpx;
	}

	.line {
		//一行信息，坐标标题，右边信息
		display: flex;
		flex-direction: row;
		padding: 13rpx;
		margin-left: 20rpx;
		/* 垂直居中 */
		align-items: center;
	}



	.btns {
		//一列多个按钮
		display: flex;
		flex-direction: column;
		margin: 5rpx;

	}

	.btnInBtns {
		margin: 10rpx;
	}

	.title {
		width: 35%;
		// font-size: $info-textsize;
		color: #333333;
		white-space: pre-wrap;
	}

	.block_title {
		width: 35%;
		// font-size: $info-textsize;
		color: $primary-color;
		white-space: pre-wrap;
	}

	.info {
		width: 65%;
		color: $info-title-text-color;
		border: transparent;
		text-align: right;
		vertical-align:middle;
		background-color: transparent;
		// /* 垂直居中 */
		// align-items: center;
	}

	.info_hint {
		width: 55%;
		color: info-detail-text-hint-color;
		border: transparent;
		text-align: right;
		background-color: transparent;

	}

	.inputLine {
		//一行信息，
		display: flex;
		flex-direction: row;
		padding: 17rpx;
		/* 垂直居中 */
		align-items: center;
	}

	.parting_inputline_hori {
		//横向分割线
		background: #DEDEDE;
		height: 1rpx;
		margin-left: 17rpx;
		margin-right: 17rpx;
	}

	.inputInfo {
		width: 60%;
		// font-size: $info-textsize;
		color: $info-title-text-color;
	}

	.devider {
		background-color: $divider-color;

	}

	.swiper-item {
		width: wrap;
		height: 100%;
		white-space: nowrap;
		// overflow: hidden;
		// text-overflow: ellipsis;
		// font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		// font-weight: 400;
		// text-align: left;
		// color: #010101;
	}

	.list {
		width: 100%;
		height: 100%;
		background: white;
	}

	.listwrap {
		width: 100%;
		height: auto;
		background: white;
	}

	.listitem {
		height: auto;
		margin-top: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
		background: #ffffff;
		border-radius: 10px;
		padding-left: 23rpx;
		padding-right: 23rpx;
	}

	.listitem_box {
		border-radius: 16.67px;
		display: flex;
		height: auto;
		width: 100%;
		flex-direction: row;
		margin-left: 11rpx;
		margin-right: 11rpx;
		padding-top: 22rpx;
		padding-bottom: 12rpx;
		margin-top: 10rpx;
		background-color: white;
	}

	.list_item_content_right {
		position: absolute;
		right: 10px;
		width: wrap;
		height: auto;
		white-space: nowrap;
		padding-left: 10rpx;
		padding-right: 10rpx;
		padding-top: 5rpx;
		padding-bottom: 5rpx;
		margin-left: 24rpx;
		vertical-align: center;
		border-radius: 8.33px;
		// font-size: 10px;
		color: $info-detail-text-color;

	}

	.list_title {
		width: wrap;
		height: auto;
		font-size: 34rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		padding-left: 24rpx;
		color: #010101;
	}

	.list_com_line {
		width: 100%;
		height: auto;
		display: flex;
		flex-direction: row;
		padding-top: 5rpx;
		padding-left: 21rpx;
		padding-right: 21rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		// font-size: 12px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		color: #666666;
	}




	.left {
		display: flex;
		flex-direction: row;
		background-color: transparent;
		width: 93%;
		align-items: center;
	}


	.right {
		//列表右侧状态显示
		position: absolute;
		right: 42rpx;
		width: wrap;
		height: auto;
		white-space: normal;
		word-wrap: break-word;
		word-break: normal;
		padding-left: 16rpx;
		padding-right: 16rpx;
		padding-top: 8rpx;
		padding-bottom: 8rpx;
		margin-left: 24rpx;
		margin-top: 16rpx;
		margin-right: 16rpx;
		vertical-align: center;
		font-size: 24rpx;
		border-radius: 10rpx;
		color: white;
	}

	.bgRecColor {
		//列表右侧状态显示

		width: wrap;
		height: auto;
		white-space: normal;
		word-wrap: break-word;
		word-break: normal;
		padding-left: 16rpx;
		padding-right: 16rpx;
		padding-top: 8rpx;
		padding-bottom: 8rpx;
		margin-left: 24rpx;
		margin-top: 16rpx;
		margin-right: 16rpx;
		vertical-align: center;
		font-size: 24rpx;
		border-radius: 10rpx;
		color: white;
	}

	.rightNoBg {
		position: absolute;
		right: 42rpx;
		width: wrap;
		height: auto;
		white-space: nowrap;
		padding-left: 16rpx;
		padding-right: 16rpx;
		padding-top: 8rpx;
		padding-bottom: 8rpx;
		margin-left: 24rpx;
		margin-top: 16rpx;
		margin-right: 16rpx;
		vertical-align: center;
		font-size: 20rpx;
		vertical-align: center;
		background-color: transparent;
		color: #333333;
	}

	.list_com_half_l {
		width: 50%;
		background-color: transparent;
		color: $info-title-text-color;
		padding-left: 24rpx;
		padding-right: 24rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: flex;
		flex-direction: row;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
	}

	.list_com_half_r {
		background-color: transparent;
		width: 50%;
		padding-left: 24rpx;
		padding-right: 24rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: $info-title-text-color;
		// font-size: 12rpx;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: right;
		align-content: right;

	}

	.icon {
		width: 35rpx;
		height: 35rpx;
		background-color: transparent;
	}

	.name {
		width: wrap;
		height: auto;

		font-size: 30rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: bold;
		text-align: left;
		// padding-left: 24rpx;
		color: #010101;
	}

	// }



	.btnView {
		/* 设置为flex布局 */
		display: flex;
		width: 85%; //包裹内容
		height: fit-content;
		margin-top: 10rpx;
		padding-left: 10rpx;
		padding-right: 10rpx;
		align-items: center;
		margin-left: 35rpx;
		border: 0.6px solid #ddd;
		border-radius: 3.5px;
		background-color: transparent;
	}

	.search_line {

		display: flex;
		flex-direction: row;
		border-radius: 16.33px;
		background-color: #ffffff;
		margin-left: $margin-left-right;
		margin-right: $margin-left-right;
		margin-top: $margin-search-top;
		margin-bottom: $margin-search-bottom;


	}

	.search-button {
		width: 61.33px;
		height: 32.67px;
		background: $primary-color;
		border-radius: 16.33px;
		color: #ffffff;
	}

	.detail {
		width: wrap;
		white-space: nowrap;
		padding-left: 24rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 24rpx;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		color: #333333;
		white-space: normal;
		word-wrap: break-word;
		word-break: normal;
	}

	.form_detail {
		//工单里面的详情
		width: wrap;
		white-space: nowrap;
		padding-left: 24rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 24rpx;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		color: #666666;

		white-space: normal;
		word-wrap: break-word;
		word-break: normal;
	}

	.time {
		width: wrap;
		white-space: nowrap;
		padding-left: 0rpx;
		padding-bottom: 32rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 24rpx;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		color: #999999;
		white-space: normal;
		word-wrap: break-word;
		word-break: normal;
	}

	// .icon {
	// 	width: 15rpx;
	// 	height: 15rpx;
	// 	background-color: transparent;
	// }
	.content {
		background-color: white;
	}
</style>