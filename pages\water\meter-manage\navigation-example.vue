<template>
	<view class="container">
		<view class="header">
			<text class="title">抄表导航示例</text>
		</view>
		
		<view class="content">
			<view class="meter-info">
				<text class="info-title">水表信息</text>
				<view class="info-item">
					<text class="label">水表地址：</text>
					<text class="value">{{ meterData.meterAddress }}</text>
				</view>
				<view class="info-item">
					<text class="label">经度：</text>
					<text class="value">{{ meterData.meterLongitude }}</text>
				</view>
				<view class="info-item">
					<text class="label">纬度：</text>
					<text class="value">{{ meterData.meterLatitude }}</text>
				</view>
			</view>
			
			<button class="nav-button" @click="openNavigation">
				<text class="button-text">水表定位导航</text>
			</button>
		</view>
		
		<!-- 导航组件 -->
		<navigation ref="navigation"></navigation>
	</view>
</template>

<script>
import navigation from './navigation.vue';

export default {
	components: {
		navigation
	},
	data() {
		return {
			// 示例水表数据
			meterData: {
				meterAddress: '山西省太原市小店区某某小区1号楼',
				meterLongitude: '112.5384',
				meterLatitude: '37.8756'
			}
		};
	},
	methods: {
		// 打开导航
		openNavigation() {
			// 调用导航组件的openEvent方法，传入水表坐标
			this.$refs.navigation.openEvent(this.meterData);
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f5;
	
	.header {
		background: #1890ff;
		padding: 20px 16px;
		padding-top: calc(var(--status-bar-height) + 20px);
		
		.title {
			color: #fff;
			font-size: 18px;
			font-weight: 500;
		}
	}
	
	.content {
		padding: 20px;
		
		.meter-info {
			background: #fff;
			border-radius: 8px;
			padding: 16px;
			margin-bottom: 20px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
			
			.info-title {
				font-size: 16px;
				font-weight: 500;
				color: #333;
				margin-bottom: 12px;
			}
			
			.info-item {
				display: flex;
				margin-bottom: 8px;
				
				.label {
					color: #666;
					width: 80px;
				}
				
				.value {
					color: #333;
					flex: 1;
				}
			}
		}
		
		.nav-button {
			width: 100%;
			height: 50px;
			background: linear-gradient(135deg, #1890ff, #40a9ff);
			border-radius: 25px;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
			
			.button-text {
				color: #fff;
				font-size: 16px;
				font-weight: 500;
			}
			
			&:active {
				transform: scale(0.98);
			}
		}
	}
}
</style>
