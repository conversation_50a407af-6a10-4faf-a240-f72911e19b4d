import App from './App'
//vue2
// #ifndef VUE3
import Vue from 'vue'
import uView from '@/uni_modules/uview-ui'
// 添加 uni-icons 组件引入
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'

Vue.use(uView)
// 全局注册 uni-icons 组件
Vue.component('uni-icons', uniIcons)
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
	...App
})
app.$mount()
// #endif
Vue.prototype.$onLaunched = new Promise(resolve => {
    Vue.prototype.$isResolve = resolve
})
// // #ifdef VUE3
// import { createSSRApp } from 'vue'
// export function createApp() {
//   const app = createSSRApp(App)
//   return {
//     app
//   }
// }
// // #endif
import {request} from './api/request.js'

Vue.prototype.$request = {request}