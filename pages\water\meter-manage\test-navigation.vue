<template>
  <view class="container">
    <view class="header">
      <text class="title">导航功能测试</text>
    </view>
    
    <view class="content">
      <view class="test-info">
        <text class="info-title">测试数据</text>
        <view class="info-item">
          <text class="label">水表地址：</text>
          <text class="value">{{ testData.meterAddress }}</text>
        </view>
        <view class="info-item">
          <text class="label">水表经度：</text>
          <text class="value">{{ testData.meterLongitude }}</text>
        </view>
        <view class="info-item">
          <text class="label">水表纬度：</text>
          <text class="value">{{ testData.meterLatitude }}</text>
        </view>
      </view>
      
      <view class="button-group">
        <button class="test-btn primary" @click="testNavigation">
          <text class="btn-text">🗺️ 打开导航弹窗</text>
        </button>

        <button class="test-btn secondary" @click="testWithDifferentLocation">
          <text class="btn-text">📍 测试不同位置</text>
        </button>

        <button class="test-btn info" @click="showDebugInfo">
          <text class="btn-text">🔍 显示调试信息</text>
        </button>

        <button class="test-btn warning" @click="testClosePopup">
          <text class="btn-text">❌ 关闭弹窗</text>
        </button>

        <button class="test-btn purple" @click="testPanelDisplay">
          <text class="btn-text">🎛️ 测试面板显示</text>
        </button>
      </view>
      
      <view class="debug-info" v-if="showDebug">
        <text class="debug-title">调试信息</text>
        <view class="debug-item">
          <text class="debug-label">组件状态：</text>
          <text class="debug-value">{{ componentStatus }}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">标记点数量：</text>
          <text class="debug-value">{{ markerCount }}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">面板显示状态：</text>
          <text class="debug-value">{{ panelVisible ? '显示' : '隐藏' }}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">路线距离：</text>
          <text class="debug-value">{{ routeDistance || '未规划' }}</text>
        </view>
      </view>
    </view>
    
    <!-- 导航组件 -->
    <navigation ref="navigation"></navigation>
  </view>
</template>

<script>
import navigation from './navigation.vue';

export default {
  components: {
    navigation
  },
  data() {
    return {
      showDebug: false,
      componentStatus: '未初始化',
      markerCount: 0,
      panelVisible: false,
      routeDistance: '',
      testData: {
        meterAddress: '山西省太原市小店区测试水表位置',
        meterLongitude: '112.5384',
        meterLatitude: '37.8756'
      },
      alternateData: {
        meterAddress: '山西省太原市迎泽区备用测试位置',
        meterLongitude: '112.5644',
        meterLatitude: '37.8696'
      }
    };
  },
  methods: {
    // 测试导航功能
    testNavigation() {
      console.log('开始测试导航弹窗功能');
      this.componentStatus = '正在打开导航弹窗';

      try {
        this.$refs.navigation.openEvent(this.testData);
        this.componentStatus = '导航弹窗已打开';

        uni.showToast({
          title: '导航弹窗打开成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('导航弹窗打开失败:', error);
        this.componentStatus = '导航弹窗打开失败';

        uni.showToast({
          title: '导航弹窗打开失败',
          icon: 'none'
        });
      }
    },
    
    // 测试不同位置
    testWithDifferentLocation() {
      console.log('测试不同位置');
      this.componentStatus = '测试备用位置';
      
      try {
        this.$refs.navigation.openEvent(this.alternateData);
        this.componentStatus = '备用位置导航已启动';
        
        uni.showToast({
          title: '备用位置测试成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('备用位置测试失败:', error);
        this.componentStatus = '备用位置测试失败';
        
        uni.showToast({
          title: '备用位置测试失败',
          icon: 'none'
        });
      }
    },
    
    // 显示调试信息
    showDebugInfo() {
      this.showDebug = !this.showDebug;

      if (this.showDebug) {
        // 获取导航组件的状态信息
        const navComponent = this.$refs.navigation;
        if (navComponent) {
          this.markerCount = navComponent.markers ? navComponent.markers.length : 0;
          this.componentStatus = navComponent.isNavigating ? '导航中' : '待机';
          this.panelVisible = navComponent.showNavigationPanel;
          this.routeDistance = navComponent.routeDistance;
        }
      }
    },

    // 测试关闭弹窗
    testClosePopup() {
      try {
        this.$refs.navigation.closeNavigation();
        this.componentStatus = '弹窗已关闭';

        uni.showToast({
          title: '弹窗关闭成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('关闭弹窗失败:', error);
        uni.showToast({
          title: '关闭弹窗失败',
          icon: 'none'
        });
      }
    },

    // 测试面板显示
    testPanelDisplay() {
      console.log('测试导航面板显示');

      try {
        // 先打开导航
        this.$refs.navigation.openEvent(this.testData);

        // 延迟检查面板状态
        setTimeout(() => {
          const navComponent = this.$refs.navigation;
          if (navComponent) {
            console.log('面板显示状态:', navComponent.showNavigationPanel);
            console.log('标记点数量:', navComponent.markers.length);
            console.log('路线数量:', navComponent.polyline.length);

            uni.showModal({
              title: '面板状态检查',
              content: `面板显示: ${navComponent.showNavigationPanel ? '是' : '否'}\n标记点: ${navComponent.markers.length}个\n路线: ${navComponent.polyline.length}条`,
              showCancel: false
            });
          }
        }, 1000);

      } catch (error) {
        console.error('测试面板显示失败:', error);
        uni.showToast({
          title: '测试失败',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    color: #ffffff;
    font-size: 24px;
    font-weight: bold;
  }
}

.content {
  .test-info {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    .info-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }
    
    .info-item {
      display: flex;
      margin-bottom: 10px;
      
      .label {
        color: #666;
        width: 100px;
        font-weight: 500;
      }
      
      .value {
        color: #333;
        flex: 1;
      }
    }
  }
  
  .button-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
    
    .test-btn {
      height: 50px;
      border-radius: 25px;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      
      &.primary {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
      }
      
      &.secondary {
        background: linear-gradient(135deg, #52c41a, #73d13d);
      }
      
      &.info {
        background: linear-gradient(135deg, #722ed1, #9254de);
      }

      &.warning {
        background: linear-gradient(135deg, #fa8c16, #ffa940);
      }

      &.purple {
        background: linear-gradient(135deg, #722ed1, #9254de);
      }
      
      &:active {
        transform: scale(0.98);
      }
      
      .btn-text {
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
  
  .debug-info {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    padding: 15px;
    
    .debug-title {
      color: #00ff00;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    .debug-item {
      display: flex;
      margin-bottom: 8px;
      
      .debug-label {
        color: #ffff00;
        width: 120px;
        font-size: 14px;
      }
      
      .debug-value {
        color: #ffffff;
        flex: 1;
        font-size: 14px;
      }
    }
  }
}
</style>
