<template>
  <view class="container">
    <view class="title-bar">
      <text class="title">山西综改区智慧水务</text>
    </view>
    <view class="header-card">
      <view class="user-info">
        <image class="avatar" src="/static/uview/common/avatar.png"></image>
        <view class="user-details">
          <text class="username">{{ username }}</text>
          <text class="department">综合办</text>
        </view>
      </view>
      <view class="sign-in">
        <button class="sign-in-btn" @click="logout">退出</button>
      </view>
    </view>
    <view class="grid">
      <view v-for="(item, index) in menuItems" :key="index" class="grid-item"
            @click="handleMenuItemClick(item.name)">
        <view :style="{ 'background': item.background }" class="icon-container">
          <image :src="item.icon" class="grid-icon"></image>
        </view>
        <text class="grid-text">{{ item.name }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import {login} from '../../api/login.js';
import md5 from '../../js_sdk/js-md5/src/md5.js';
import {setData} from "@/api/meter-manage/http/auth";

export default {
  data() {
    return {
      username: '',
      refreshTimer: null,
      menuItems: [
        {
          name: '数据统计',
          icon: '/static/icons/data-stats.svg',
          background: 'linear-gradient(135deg, rgba(98, 184, 245, 0.7), rgba(68, 129, 235, 0.7))'
        },
        {
          name: '实时监控',
          icon: '/static/icons/realtime-monitor.svg',
          background: 'linear-gradient(135deg, rgba(86, 217, 163, 0.7), rgba(33, 176, 138, 0.7))'
        },
        //{ name: '实时告警', icon: '/static/icons/realtime-alert.svg', background: 'linear-gradient(135deg, rgba(254, 203, 110, 0.7), rgba(249, 169, 62, 0.7))' },
        {
          name: '地图查询',
          icon: '/static/icons/map-search.svg',
          background: 'linear-gradient(135deg, rgba(142, 130, 241, 0.7), rgba(109, 93, 245, 0.7))'
        },
        {
          name: '巡检任务',
          icon: '/static/icons/inspection-task.svg',
          background: 'linear-gradient(135deg, rgba(101, 199, 247, 0.7), rgba(0, 162, 244, 0.7))'
        },
        {
          name: '隐患上报',
          icon: '/static/icons/hazard-report.svg',
          background: 'linear-gradient(135deg, rgba(255, 154, 139, 0.7), rgba(255, 106, 136, 0.7))'
        },
        {
          name: '抄表管理',
          icon: '/static/icons/field-work-order.svg',
          background: 'linear-gradient(135deg, rgba(143, 211, 244, 0.7), rgba(132, 250, 176, 0.7))'
        },
        //{ name: '报装工单', icon: '/static/icons/installation-work-order.svg', background: 'linear-gradient(135deg, rgba(127, 127, 213, 0.7), rgba(145, 234, 228, 0.7))' },
        //{ name: '公告通知', icon: '/static/icons/announcement.svg', background: 'linear-gradient(135deg, rgba(195, 169, 245, 0.7), rgba(167, 126, 242, 0.7))' },
        //{ name: '设置', icon: '/static/icons/settings.svg', background: 'linear-gradient(135deg, rgba(246, 211, 101, 0.7), rgba(253, 160, 133, 0.7))' }
      ]
    };
  },
  created() {
    this.username = uni.getStorageSync('username') || '用户';
    this.startTokenRefresh();
    this.loginZhyx();
  },
  methods: {
    async loginZhyx() {
      let {data} = await login({username: "admin", password: "Sxzgzhsw@123"});
      console.log("获取登录token", data);
      setData("zhyx_token", data.access_token);
    },
    handleMenuItemClick(name) {
      if (name === '实时监控') {
        uni.navigateTo({
          url: '/pages/water/realtime-monitor/index'
        });
      } else if (name === '数据统计') {
        uni.navigateTo({
          url: '/pages/water/data-total/index'
        });
      } else if (name === '地图查询') {
        uni.navigateTo({
          url: '/pages/water/map-query/index'
        });
      } else if (name === '巡检任务') {
        uni.navigateTo({
          url: '/pages/water/inspection-task/index'
        });
      } else if (name === '隐患上报') {
        uni.navigateTo({
          url: '/pages/water/danger-report/index'
        });
      } else if (name === '抄表管理') {
        uni.navigateTo({
          url: '/pages/water/meter-manage/index'
        });
      } else {
        uni.showToast({
          title: '功能暂未开放，请等待上线',
          icon: 'none'
        });
      }
    },
    logout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除本地存储的用户信息
            uni.removeStorageSync('username');
            uni.removeStorageSync('token');
            // 跳转到登录页
            uni.reLaunch({
              url: '/pages/water/login/index'
            });
          }
        }
      });
    },
    // 开始token刷新定时器
    startTokenRefresh() {
      // 设置10分钟（600000毫秒）的定时器
      this.refreshTimer = setInterval(() => {
        this.refreshToken();
      }, 10 * 60 * 1000);
    },
    // 刷新token
    async refreshToken() {
      try {
        const username = uni.getStorageSync('username');
        const password = uni.getStorageSync('pwd'); // 修改为正确的存储键名
        const isRemember = uni.getStorageSync('isRemember');

        // 检查是否记住了密码
        if (username && password && isRemember) {
          // 使用MD5加密密码，与登录时保持一致
          const md5pwd = md5(password);

          const loginParams = {
            account: username,
            password: md5pwd
          };

          const result = await login(loginParams);
          if (result && result.success && result.msg) {
            // 更新token，格式与登录时保持一致
            uni.setStorageSync('token', "Bearer " + result.msg);
            console.log('Token刷新成功');
          } else {
            console.log('Token刷新失败，服务器返回错误');
            this.handleTokenRefreshFailure();
          }
        } else {
          console.log('用户未选择记住密码，无法自动刷新token');
          this.handleTokenRefreshFailure();
        }
      } catch (error) {
        console.error('Token刷新失败:', error);
        this.handleTokenRefreshFailure();
      }
    },
    // 处理token刷新失败的情况
    handleTokenRefreshFailure() {
      // 停止定时器
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }

      // 提示用户重新登录
      uni.showModal({
        title: '提示',
        content: '登录状态已过期，请重新登录',
        showCancel: false,
        success: () => {
          this.logout();
        }
      });
    }
  },
  // 页面销毁时清理定时器
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  },
  // uni-app生命周期
  onUnload() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

};
</script>

<style scoped>
.container {
  background-color: #fff;
  min-height: 100vh;
}

.title-bar {
  background-color: #1890ff;
  color: white;
  padding: 50px 20px 30px 20px;
  text-align: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.header-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  margin: -20px 15px 20px 15px;
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 16px;
  font-weight: bold;
}

.department {
  font-size: 12px;
  color: #999;
}

.sign-in-btn {
  background-color: #fff;
  color: #1890ff;
  border: 1px solid #1890ff;
  border-radius: 20px;
  padding: 6px 20px;
  font-size: 14px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon-container {
  width: 70px;
  height: 70px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.grid-icon {
  width: 35px;
  height: 35px;
}

.grid-text {
  font-size: 14px;
  color: #333;
}
</style>
