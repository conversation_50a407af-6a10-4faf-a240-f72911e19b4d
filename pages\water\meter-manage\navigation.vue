<template>
  <uni-popup ref="container" :mask-click="false" class="navigation-popup" type="center">

    <!-- 弹窗标题栏 -->
    <view class="popup-header">
      <text class="popup-title">抄表导航</text>
      <view class="close-icon" @click="closeNavigation">
        <text class="close-text">×</text>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <!--
        markers 多个标记地点
       -->
      <map id="amapContainers" :enable-3D="true" :enable-overlooking="true" :enable-poi="true"
           :enable-rotate="true" :enable-scroll="true" :enable-zoom="true" :latitude="mapCenter.latitude"
           :longitude="mapCenter.longitude"
           :markers="markers" :polyline="polyline"
           :scale="mapScale" :show-location="true" :zIndex="1" class="map" @markertap="onMarkerTap"
           @regionchange="onRegionChange" @tap="onMapTap">
      </map>

      <!-- 地图控制按钮 -->
      <cover-view class="map-controls">
        <cover-image class="location-btn" src="../../../static/icon/location.png"
                     @click="centerToLocation"></cover-image>
      </cover-view>

      <!-- 导航控制面板 -->
      <cover-view v-if="showNavigationPanel" class="navigation-panel" :class="{ 'panel-show': showNavigationPanel }">
        <!-- 距离信息显示 -->
        <cover-view v-if="routeDistance" class="distance-info">
          <cover-text class="distance-text">距离: {{ routeDistance }}</cover-text>
        </cover-view>

        <!-- 导航按钮组 -->
        <cover-view class="nav-buttons">
          <cover-view v-if="!isNavigating" class="nav-btn start-nav" @click="startNavigation">
            <cover-text class="nav-btn-icon">🧭</cover-text>
            <cover-text class="nav-btn-text">开始导航</cover-text>
          </cover-view>

          <cover-view class="nav-btn replan" @click="replanRoute">
            <cover-text class="nav-btn-icon">🔄</cover-text>
            <cover-text class="nav-btn-text">重新规划</cover-text>
          </cover-view>
          <cover-view class="nav-btn clear" @click="clearRoute">
            <cover-text class="nav-btn-icon">🗑️</cover-text>
            <cover-text class="nav-btn-text">清除路线</cover-text>
          </cover-view>
        </cover-view>
      </cover-view>
    </view>
  </uni-popup>
</template>

<script>
// 导航组件 - 专用于抄表员导航功能

export default {
  data() {
    return {
      // 地图中心点（山西太原坐标）
      mapCenter: {
        longitude: 112.53,
        latitude: 37.87
      },
      // 地图缩放级别
      mapScale: 13,
      // 标记点数组
      markers: [],
      // 地图上下文
      mapContext: null,
      // 用户位置
      userLocation: null,
      // 用户位置标记
      userLocationMarker: null,
      // 水表位置标记
      meterLocationMarker: null,
      // 路线规划相关
      polyline: [],
      // 水表数据
      fjdsj: {},
      // 导航状态
      isNavigating: false,
      // 路线距离信息
      routeDistance: ''
    };
  },

  async onLoad() {
    // 初始化地图上下文
    this.initMapContext();
  },

  onReady() {
    // 页面渲染完成后初始化地图上下文
    this.initMapContext();
  },

  onUnload() {
    // 清理资源
    this.mapContext = null;
  },

  computed: {
    // 控制导航面板显示
    showNavigationPanel() {
      // 当有用户位置和水表位置时显示面板
      const hasUserMarker = this.markers.some(marker => marker.id === 'user-location');
      const hasMeterMarker = this.markers.some(marker => marker.id === 'meter-location');
      const hasRoute = this.polyline.length > 0;

      console.log('面板显示条件检查:', {
        hasUserMarker,
        hasMeterMarker,
        hasRoute,
        isNavigating: this.isNavigating,
        markersCount: this.markers.length
      });

      return (hasUserMarker && hasMeterMarker) || hasRoute || this.isNavigating;
    }
  },

  methods: {
    // 初始化导航地图
    async initNavigationMap() {
      try {
        uni.showLoading({
          title: '正在获取位置...'
        });

        // 获取抄表员当前位置
        await this.getCurrentLocationForNavigation();

        // 创建抄表员和水表位置标记
        this.createNavigationMarkers();

        // 规划路线
        await this.planRoute();

        // 调整地图视野以显示完整路线
        this.adjustMapView();

        // 打开弹窗显示地图
        this.$refs.container.open();

        // 延迟初始化地图上下文，确保弹窗完全打开后再初始化
        setTimeout(() => {
          this.initMapContext();
        }, 300);

        uni.hideLoading();
        uni.showToast({
          title: '导航准备完成',
          icon: 'success'
        });

      } catch (error) {
        console.error('初始化导航失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '导航初始化失败',
          icon: 'none'
        });
      }
    },

    // 获取抄表员当前位置（用于导航）
    getCurrentLocationForNavigation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            this.userLocation = {
              latitude: res.latitude,
              longitude: res.longitude
            };
            console.log('获取到抄表员位置:', this.userLocation);
            resolve(res);
          },
          fail: (err) => {
            console.error('获取位置失败:', err);
            reject(err);
          }
        });
      });
    },

    // 创建导航标记点（抄表员位置和水表位置）
    createNavigationMarkers() {
      const markers = [];

      // 抄表员位置标记
      if (this.userLocation) {
        this.userLocationMarker = {
          id: 'user-location',
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude,
          title: '抄表员位置',
          iconPath: '/static/icons/marker-normal.png',
          width: 32,
          height: 32,
          callout: {
            content: '抄表员位置',
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#1890ff',
            padding: 6,
            display: 'ALWAYS'
          }
        };
        markers.push(this.userLocationMarker);
      }

      // 水表位置标记
      if (this.fjdsj.meterLongitude && this.fjdsj.meterLatitude) {
        this.meterLocationMarker = {
          id: 'meter-location',
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude),
          title: '水表位置',
          iconPath: '/static/icons/marker-error.png',
          width: 32,
          height: 32,
          callout: {
            content: `目标水表\n${this.fjdsj.meterAddress || ''}`,
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#52c41a',
            padding: 6,
            display: 'ALWAYS'
          }
        };
        markers.push(this.meterLocationMarker);
      }

      this.markers = markers;
      console.log('创建导航标记点:', markers);
      console.log('当前标记点数量:', this.markers.length);
      console.log('导航面板显示状态:', this.showNavigationPanel);
    },

    // 规划路线
    async planRoute() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        console.error('缺少位置信息，无法规划路线');
        return;
      }

      try {
        // 创建简单的直线路线（实际项目中可以调用高德地图路线规划API）
        const startPoint = {
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude
        };

        const endPoint = {
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude)
        };

        // 计算距离
        const distance = this.calculateDistance(startPoint, endPoint);
        console.log(`路线距离: ${distance.toFixed(2)}米`);

        // 创建路线polyline
        this.polyline = [{
          points: [startPoint, endPoint],
          color: '#1890ff',
          width: 4,
          dottedLine: false,
          arrowLine: true,
          borderColor: '#ffffff',
          borderWidth: 1
        }];

        console.log('路线规划完成:', this.polyline);

        // 保存距离信息
        this.routeDistance = distance > 1000 ?
          `${(distance / 1000).toFixed(1)}公里` :
          `${distance.toFixed(0)}米`;

        // 显示距离信息
        uni.showToast({
          title: `距离约${this.routeDistance}`,
          icon: 'none',
          duration: 3000
        });

      } catch (error) {
        console.error('路线规划失败:', error);
        // 即使路线规划失败，也创建一个简单的直线
        this.createSimpleRoute();
      }
    },

    // 创建简单路线（备用方案）
    createSimpleRoute() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        return;
      }

      this.polyline = [{
        points: [
          {
            longitude: this.userLocation.longitude,
            latitude: this.userLocation.latitude
          },
          {
            longitude: parseFloat(this.fjdsj.meterLongitude),
            latitude: parseFloat(this.fjdsj.meterLatitude)
          }
        ],
        color: '#1890ff',
        width: 4,
        dottedLine: true
      }];
    },

    // 计算两点间距离（米）
    calculateDistance(point1, point2) {
      const R = 6371000; // 地球半径（米）
      const lat1Rad = point1.latitude * Math.PI / 180;
      const lat2Rad = point2.latitude * Math.PI / 180;
      const deltaLatRad = (point2.latitude - point1.latitude) * Math.PI / 180;
      const deltaLngRad = (point2.longitude - point1.longitude) * Math.PI / 180;

      const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
          Math.cos(lat1Rad) * Math.cos(lat2Rad) *
          Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      return R * c;
    },

    // 调整地图视野以显示完整路线
    adjustMapView() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        return;
      }

      const userLng = this.userLocation.longitude;
      const userLat = this.userLocation.latitude;
      const meterLng = parseFloat(this.fjdsj.meterLongitude);
      const meterLat = parseFloat(this.fjdsj.meterLatitude);

      // 计算中心点
      const centerLng = (userLng + meterLng) / 2;
      const centerLat = (userLat + meterLat) / 2;

      // 计算合适的缩放级别
      const lngDiff = Math.abs(userLng - meterLng);
      const latDiff = Math.abs(userLat - meterLat);
      const maxDiff = Math.max(lngDiff, latDiff);

      let scale = 16;
      if (maxDiff > 0.01) scale = 14;
      if (maxDiff > 0.02) scale = 13;
      if (maxDiff > 0.05) scale = 12;
      if (maxDiff > 0.1) scale = 11;

      // 更新地图中心和缩放级别
      this.mapCenter = {
        longitude: centerLng,
        latitude: centerLat
      };
      this.mapScale = scale;

      console.log('调整地图视野:', this.mapCenter, 'scale:', scale);
    },

    // 开始导航
    startNavigation() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        uni.showToast({
          title: '位置信息不完整',
          icon: 'none'
        });
        return;
      }

      this.isNavigating = true;

      // 可以在这里添加实时位置更新逻辑
      uni.showModal({
        title: '开始导航',
        content: '是否要打开外部地图应用进行导航？',
        success: (res) => {
          if (res.confirm) {
            this.openExternalMap();
          }
        }
      });
    },

    // 打开外部地图应用
    openExternalMap() {
      const meterLng = this.fjdsj.meterLongitude;
      const meterLat = this.fjdsj.meterLatitude;
      const meterName = this.fjdsj.meterAddress || '水表位置';

      // #ifdef APP-PLUS
      // 构建高德地图URL
      const amapUrl = `amaps://route/plan/?sid=BGVIS1&slat=${this.userLocation.latitude}&slon=${this.userLocation.longitude}&sname=当前位置&did=BGVIS2&dlat=${meterLat}&dlon=${meterLng}&dname=${encodeURIComponent(meterName)}&dev=0&t=0`;

      // 构建百度地图URL（备用）
      const baiduUrl = `baidumap://map/direction?origin=${this.userLocation.latitude},${this.userLocation.longitude}&destination=${meterLat},${meterLng}&mode=walking`;

      // 尝试打开高德地图
      plus.runtime.openURL(amapUrl, () => {
        console.log('打开高德地图失败，尝试百度地图');
        // 如果高德地图打开失败，尝试百度地图
        plus.runtime.openURL(baiduUrl, () => {
          console.log('打开百度地图也失败');
          uni.showToast({
            title: '请安装地图应用',
            icon: 'none'
          });
        });
      });
      // #endif

      // #ifdef H5
      // H5环境下打开网页版地图
      const webUrl = `https://uri.amap.com/navigation?from=${this.userLocation.longitude},${this.userLocation.latitude},当前位置&to=${meterLng},${meterLat},${encodeURIComponent(meterName)}&mode=walking`;
      window.open(webUrl, '_blank');
      // #endif
    },

    // 清除路线
    clearRoute() {
      this.polyline = [];
      this.isNavigating = false;
      this.routeDistance = '';
      uni.showToast({
        title: '已清除路线',
        icon: 'success'
      });
    },

    // 重新规划路线
    async replanRoute() {
      uni.showLoading({
        title: '重新规划中...'
      });

      try {
        await this.getCurrentLocationForNavigation();
        this.createNavigationMarkers();
        await this.planRoute();
        this.adjustMapView();

        uni.hideLoading();
        uni.showToast({
          title: '路线已更新',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '重新规划失败',
          icon: 'none'
        });
      }
    },
    openEvent(data) {
      console.log('接收到水表数据:', data);
      this.fjdsj = data;

      // 检查水表坐标是否存在
      if (!data.meterLongitude || !data.meterLatitude) {
        uni.showToast({
          title: '水表位置信息不完整',
          icon: 'none'
        });
        return;
      }

      // 获取抄表员当前位置并显示导航
      this.initNavigationMap();
    },
    // 初始化地图上下文
    initMapContext() {
      // #ifdef APP-PLUS
      this.mapContext = uni.createMapContext('amapContainers', this);
      console.log('高德地图上下文初始化成功');
      // #endif
    },


    // 标记点点击事件
    onMarkerTap(e) {
      console.log('标记点被点击:', e.detail);
      const markerId = e.detail.markerId;
      const marker = this.markers.find(m => m.id === markerId);

      if (marker) {
        uni.showModal({
          title: marker.title || '位置信息',
          content: `经度: ${marker.longitude}\n纬度: ${marker.latitude}`,
          showCancel: false
        });
      }
    },

    // 地图区域变化事件
    onRegionChange(e) {
      if (e.type === 'end') {
        console.log('地图区域变化:', e.detail);
      }
    },

    // 地图点击事件
    onMapTap(e) {
      console.log('地图点击:', e.detail);
    },

    // 定位到当前位置
    // 定位到当前位置
    centerToLocation() {
      console.log("这里是否执行");
      if (this.userLocation) {
        // 更新地图中心点
        this.mapCenter = {
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude
        };
        // 放大地图层级
        this.mapScale = 17;
        // 添加红色定位点标记
        this.addUserLocationMarker();
        console.log('定位到当前位置:', this.mapCenter);
        this.mapContext.moveToLocation();
      } else {
        this.getCurrentLocation();
        uni.showToast({
          title: '正在获取位置...',
          icon: 'loading'
        });
      }
    },
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };

          // 更新地图中心点到当前位置
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          this.mapScale = 16;

          // 添加用户位置标记
          this.addUserLocationMarker();

          uni.showToast({
            title: '定位成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '获取位置失败',
            icon: 'none'
          });
        }
      });
    },
    // 添加用户位置标记
    addUserLocationMarker() {
      if (!this.userLocation) return;

      // 创建红色定位点标记
      this.userLocationMarker = {
        id: 'user-location',
        longitude: this.userLocation.longitude,
        latitude: this.userLocation.latitude,
        title: '我的位置',
        iconPath: '/static/icons/user-location.png', // 使用红色定位图标
        width: 36,
        height: 36,
        callout: {
          content: '我的位置',
          color: '#ffffff',
          fontSize: 12,
          borderRadius: 6,
          bgColor: '#ff4d4f', // 红色背景
          padding: 8,
          borderWidth: 1,
          borderColor: '#ff4d4f',
          display: 'ALWAYS',
          textAlign: 'center'
        },
        anchor: {
          x: 0.5,
          y: 1
        }
      };

      // 将用户位置标记添加到标记数组中
      this.updateMarkersWithUserLocation();
    },

    // 更新标记数组，包含用户位置标记
    updateMarkersWithUserLocation() {
      // 移除之前的用户位置标记
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');

      // 添加新的用户位置标记
      if (this.userLocationMarker) {
        this.markers = [...this.markers, this.userLocationMarker];
      }
    },

    // 移除用户位置标记
    removeUserLocationMarker() {
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');
      this.userLocationMarker = null;
    },


    // 关闭导航弹窗
    closeNavigation() {
      this.$refs.container.close();
      // 清理导航状态
      this.isNavigating = false;
      this.polyline = [];
      this.routeDistance = '';
      this.markers = [];
      this.userLocation = null;
      this.userLocationMarker = null;
      this.meterLocationMarker = null;
    }
  }
};
</script>

<style lang="scss" scoped>
// 弹窗样式
.navigation-popup {
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: #1890ff;
    border-radius: 12px 12px 0 0;

    .popup-title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }

    .close-icon {
      width: 30px;
      height: 30px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .close-text {
        color: #ffffff;
        font-size: 20px;
        font-weight: bold;
        line-height: 1;
      }

      &:active {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .map-container {
    width: 90vw;
    height: 70vh;
    position: relative;
    background-color: #f5f5f5;
    border-radius: 0 0 12px 12px;
    overflow: hidden;

    .map {
      width: 100%;
      height: 100%;
      position: relative;
    }

    // 地图控制按钮
    .map-controls {
      position: absolute;
      right: 15px;
      top: 15px;
      z-index: 10;

      .location-btn {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);

        &:active {
          transform: scale(0.95);
          background: rgba(255, 255, 255, 1);
        }

        .location-icon {
          width: 24px;
          height: 24px;
        }
      }
    }

    // 导航控制面板
    .navigation-panel {
      position: absolute;
      bottom: 15px;
      left: 15px;
      right: 15px;
      background: rgba(255, 255, 255, 0.98);
      border-radius: 20px;
      padding: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      z-index: 10;
      transform: translateY(100%);
      opacity: 0;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &.panel-show {
        transform: translateY(0);
        opacity: 1;
      }

      // 距离信息显示
      .distance-info {
        text-align: center;
        margin-bottom: 12px;
        padding: 8px 16px;
        background: rgba(24, 144, 255, 0.1);
        border-radius: 12px;
        border: 1px solid rgba(24, 144, 255, 0.2);

        .distance-text {
          color: #1890ff;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .nav-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;

        .nav-btn {
          flex: 1;
          padding: 12px 8px;
          border-radius: 16px;
          text-align: center;
          color: #ffffff;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          position: relative;
          overflow: hidden;

          // 添加微光效果
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:active::before {
            left: 100%;
          }

          &.start-nav {
            background: linear-gradient(135deg, #1890ff, #40a9ff, #69c0ff);
          }

          &.replan {
            background: linear-gradient(135deg, #52c41a, #73d13d, #95de64);
          }

          &.clear {
            background: linear-gradient(135deg, #ff4d4f, #ff7875, #ffa39e);
          }

          &:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
          }

          .nav-btn-icon {
            display: block;
            font-size: 16px;
            margin-bottom: 4px;
            line-height: 1;
          }

          .nav-btn-text {
            color: #ffffff;
            font-size: 11px;
            font-weight: 600;
            line-height: 1.2;
            display: block;
          }
        }
      }
    }
  }

}

// 弹窗整体样式
/deep/ .uni-popup {
  .uni-popup__wrapper {
    .uni-popup__wrapper-box {
      background: transparent;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      max-width: 90vw;
      max-height: 80vh;
    }
  }
}
</style>
