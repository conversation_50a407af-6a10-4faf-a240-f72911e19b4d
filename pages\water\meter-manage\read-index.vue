<!-- 抄表管理 -->
<template>
  <scroll-view scroll-y="true">
    <view class="read-box">
      <image class="bg" mode="widthFix" src="/static/img/bg1.jpg"></image>
      <view class="one">
        <view v-if="queryParams.cardSchedulingId" class="switch-box">
          <view>
            <text>跳过已抄</text>
            <u-switch v-model="queryParams.isSkip" inactiveColor="#AEC6E3" size="18" @change="change"></u-switch>
          </view>
          <view @click="submit()">
            <text>下一户</text>
            <image mode="widthFix" src="/static/img/next.png" style="width: 28rpx;"></image>
          </view>
        </view>
        <view class="info">
          <view class="title">{{ form.userName }}</view>

          <view class="info-list">
            <em></em>
            <span>户号：{{ form.userAccount }}</span>
          </view>
          <view class="info-list">
            <em></em>
            <span>电话：{{ form.contactPhone }}</span>
          </view>
          <view class="info-list">
            <em></em>
            <span>用水性质：{{ form.natureName }}</span>
          </view>
        </view>
      </view>

      <view class="two">
        <view class="card">
          <uni-table>
            <!-- 表头行 -->
            <uni-tr>
              <uni-th align="center" width="234rpx">项目</uni-th>
              <uni-th align="center" width="234rpx">上次</uni-th>
              <uni-th align="center" width="234rpx">本次</uni-th>
            </uni-tr>
            <!-- 表格数据行 -->
            <uni-tr>
              <uni-td align="center">抄表时间</uni-td>
              <uni-td align="center">{{ parseTime(form.previousReadingTime, '{y}-{m}-{d}') }}</uni-td>
              <uni-td align="center"> {{ parseTime(form.readingTime || new Date(), '{y}-{m}-{d}') }}</uni-td>
            </uni-tr>
            <uni-tr>
              <uni-td align="center">抄表读数</uni-td>
              <uni-td align="center">{{ form.previousReading || 0 }}</uni-td>
              <uni-td align="center">
                <u--input v-model="form.currentReading" border="none" inputAlign="center" placeholder="请输入读数"
                          type="number" @change="blur"></u--input>
              </uni-td>
            </uni-tr>
            <uni-tr>
              <uni-td align="center">用水量</uni-td>
              <uni-td align="center">{{ form.previousWaterConsumption || 0 }}</uni-td>
              <uni-td align="center">
                <u--input v-model="form.waterConsumption" border="none" inputAlign="center"
                          readonly type="number"></u--input>
              </uni-td>
            </uni-tr>
          </uni-table>
        </view>
        <view class="card">
          <view class="card-info">
            <span>水表表号：{{ form.meterNum }}</span>
            <span>水表地址：{{ form.meterAddress || '暂无' }}</span>
            <span>口径：{{ form.caliberSize || '暂无' }}</span>
            <span>外码：{{ form.imei || '暂无' }}</span>
			<view class="button_zdy" @click="waterMeterLocation">水表定位</view>
          </view>
          <image class="img-a" mode="widthFix" src="/static/img/i1.png"></image>
        </view>
        <u-collapse @change="change" @close="close" @open="open">
          <u-collapse-item label="水表存在异常可上传图片，添加描述 " name="1" title="上传图片">
            <view class="collase-box">
              <view class="title-box">
                <em></em>
                <label for="">添加描述</label>
              </view>
              <u--textarea v-model="form.remark" count maxlength="200" placeholder="请输入内容"></u--textarea>
              <view class="image-box">
                <u-upload :fileList="fileList1" :maxCount="3" multiple name="1"
                          @afterRead="afterRead" @delete="deletePic">
                </u-upload>
              </view>
            </view>
          </u-collapse-item>
          <u-collapse-item label="点击查看往期用水量" name="3" title="历史数据">
            <view class="collase-box" style="min-height: 504rpx;">
              <view class="title-box">
                <em></em>
                <label for="">水量统计</label>
              </view>
              <view v-if="isShow" class="chart">
                <chartHistroy ref="hisRef"></chartHistroy>
              </view>
            </view>
          </u-collapse-item>
        </u-collapse>
      </view>
      <view class="read-btn">
        <view v-if="!isNext" class="btnlist" @click="confirm(1)">
          提交并返回
        </view>
        <block v-if="queryParams.cardSchedulingId">
          <view v-if="isNext" class="btnlist" @click="up()">
            上一户
          </view>
          <view class="btnlist" @click="submit()">
            下一户
          </view>
        </block>
        <!--        <block v-if="!queryParams.cardSchedulingId">-->
        <!--          <view v-if="!isNext" class="btnlist" @click="confirm(1)">-->
        <!--            提交-->
        <!--          </view>-->
        <!--        </block>-->
      </view>

    </view>


    <u-overlay :show="showmask" @click="showmask = false">
      <view class="warp">
        <view class="bg-box">
          <image mode="widthFix" src="/static/img/warn.png" style="width: 602rpx;"></image>
          <label for="">异常报警</label>
          <span>抄表数据存在异常！</span>
        </view>
        <image mode="widthFix" src="/static/img/close.png" style="width: 60rpx;"></image>
      </view>
    </u-overlay>
    <u-modal ref="uModal" :asyncClose="true" :content="content" :show="show" :showCancelButton="true" title="提示" @cancel="cancel" @confirm="confirm"></u-modal>
	<navigation ref="navigation" />
  </scroll-view>
</template>

<script>
// import Navbar from '@/components/navbar/Navbar'
import chartHistroy from './progress-circle/chartHistroy.vue'
import {computeWaterConsumption, getNextData, saveMeterReading} from '@/api/meter-manage/meter.js'
import {config} from "@/api/meter-manage/http/config.js";
import {getData} from "@/api/meter-manage/http/auth.js";
import {parseTime} from "@/pages/water/meter-manage/utils/timeUtils";
import navigation from "./navigation.vue"

export default {
  components: {
    // Navbar,
    chartHistroy,
	navigation
  },
  data() {
    return {
      isNext: false,
      show: false,
      isShow: false,
      value1: '',
      value2: '',
      fileList1: [],
      showmask: false,
      meterVolumesNum: '', //抄表册本编号
      cardSchedulingId: '', //小区编码
      clickType: '', // 点击类型  under:下一条 over:上一条
      info: null,
      form: {
        recordRemark: '',
        currentReading: null,
        waterConsumption: null,
        remark: ''
      },
      imgList: [],
      content: '是否确认提交数据，并跳转下一户',
      dataList: {
        categories: [],
        series: []
      },
      queryParams: {
        detailId: null,
        cardSchedulingId: null,
        meterNum: null,
        volumeSchedulingId: null,
        isSkip: false,
        clickType: 'down'
      },

      detailId: undefined,

    };
  },
  onLoad(options) {
    this.queryParams = {...this.queryParams, ...options};
    this.getInfo()
  },
  methods: {
    parseTime,
    cancel() {
      this.show = false;
    },
    up() {
      this.queryParams.meterNum = undefined;
      this.queryParams.clickType = 'over'
      this.assignQuery()
      this.getInfo()
    },

    assignQuery() {
      this.queryParams.cardSchedulingId = this.form.schedulingId;
      this.queryParams.volumeSchedulingId = this.form.volumeSchedulingId;
      this.queryParams.detailId = this.form.detailId;
    },


    // 提交抄表
    submit() {
      this.show = true;
    },


    // 提交
    async confirm(type) {
      if (!this.form.currentReading) {
        uni.$u.toast('请输入本期用水')
        return false
      }
      this.form.attachment = this.fileList1.map((item) => item.url) ? (this.fileList1.map((item) => item.url))
          .toString() : ''
      const {code} = await saveMeterReading(this.form)
      if (code == 200) {
        if (type == 1) {
          uni.$u.toast('提交成功')
          uni.navigateBack()
        } else {
          this.isNext = true
          this.queryParams.meterNum = undefined;
          this.queryParams.clickType = 'down'
          this.assignQuery();
          this.show = false;
          await this.getInfo()
        }
      }

    },


    blur(e) {
      this.form.waterConsumption = e;
      if (this.form.currentReading) {
        this.computeWater(this.form.currentReading)
      }

    },
    // 重置数据
    reset() {
      this.form = {
        currentReading: null,
        waterConsumption: null,
        remark: '',
        attachment: null
      }
      this.fileList1 = []
    },
    // 计算用水量
    async computeWater(num) {
      const {data} = await computeWaterConsumption({
        currentReading: num,
        previousReading: this.form.previousReading,
        userAccount: this.form.userAccount
      })
      this.form.waterConsumption = data.waterConsumption
      this.$forceUpdate();
    },

    // 当前页面数据
    async getInfo() {

      const {data, code} = await getNextData(this.queryParams)
      if (code == 9999) {
        uni.$u.toast('提交成功,已经是最后一条数据')
        setTimeout(() => {
          uni.navigateBack()
        }, 1000)
        return false
      }
      this.reset()
	  console.log("当前页面数据",data);
	  // meterLongitude 经度
	  // meterLatitude 维度
      this.form = data
      this.dataList.categories = data.historyList.map((item) => parseTime(item.readingTime, '{m}-{d}'))
      this.dataList.series = data.historyList.map((item) => item.consumption)
    },
    change(e) {
      this.queryParams.isSkip = e
    },
    open(e) {
      if (e == 3) {
        this.isShow = true
        this.$nextTick(() => {
          this.$refs.hisRef.getServerData(this.dataList)
        })

      }
    },
    close(e) {
      if (e == 3) {
        this.isShow = false
        this.$nextTick(() => {
          this.$refs.hisRef.close()
        })

      }
    },
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1)
    },
    // 新增图片
    async afterRead(event) {
      console.log(event)
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file)
      let fileListLen = this[`fileList${event.name}`].length
      lists.map((item) => {
        this[`fileList${event.name}`].push({
          ...item,
          status: 'uploading',
          message: '上传中'
        })
      })
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url)

        let item = this[`fileList${event.name}`][fileListLen]
        this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
          status: 'success',
          message: '',
          url: result
        }))
        fileListLen++
      }
      console.log(this.fileList1)
    },
    uploadFilePromise(url) {
      // console.log(url)
      return new Promise((resolve, reject) => {
        let a = uni.uploadFile({
          url: config.BASE_URL + "/file/upload", // 仅为示例，非真实的接口地址
          filePath: url,
          name: 'file',
          header: {
            'Authorization': 'Bearer ' + getData("zhyx_token") // 设置token
          },
          success: (res) => {
            console.log(res)
            console.log(JSON.parse(res.data))
            setTimeout(() => {
              resolve(JSON.parse(res.data).data.url)

            }, 1000)
          }
        });
      })
    },
	
	waterMeterLocation() {
		// meterLongitude 经度
		// meterLatitude 维度
		// this.form = data
		this.$refs.navigation.openEvent(this.form)
	}
  },
};
</script>
<style lang="scss">
.read-box {
  .u-collapse-item__content__text {
    // padding: 12rpx 0 !important;
  }

  ::deep .u-collapse-item .u-cell {
    background-color: #fff !important;
  }
}
</style>
<style lang="scss" scoped>
.warp {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  flex-direction: column;

  .bg-box {
    position: relative;
    width: 602rpx;
    height: 292rpx;
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    padding-bottom: 40rpx;
    box-sizing: border-box;

    image {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
    }

    label {
      display: block;
      position: relative;
      z-index: 2;
      font-size: 38rpx;
      font-weight: 500;
      color: #FB4A27;
      line-height: 28rpx;
      // margin-top: 156rpx;
      // margin-bottom: 20rpx;
    }

    span {
      position: relative;
      z-index: 2;
      font-size: 28rpx;
      font-weight: 400;
      color: #3F3F3F;
      line-height: 28rpx;
      margin-top: 40rpx;
    }
  }
}

.read-box {
  position: relative;
  background-color: #EFF3F9;
  min-height: 100vh;


  .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }

  .one {
    position: relative;
    z-index: 2;
    padding: 34rpx;
    box-sizing: border-box;
    // background-color: pink;

    .switch-box {
      display: flex;

      justify-content: space-between;
      align-items: center;
      font-size: 26rpx;
      // font-weight: 400;
      // color: #1172FD;
      // line-height: 28rpx;
      // background-color: pink;
      text {
        margin-right: 10rpx;
      }

      view {
        display: flex;
        align-items: center;
      }
    }

    .info {
      width: 100%;
      margin-top: 22rpx;

      .title {
        width: 100%;
        text-align: left;
        font-size: 44rpx;
        color: #000000;
      }

      .info-list {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        em {
          display: block;
          width: 14rpx;
          height: 14rpx;
          background: #00B051;
          border-radius: 50%;
          margin-right: 10rpx;
        }

        span {
          font-size: 26rpx;
          font-weight: 400;
          color: #7A8192;
        }
      }

    }
  }

  .two {
    padding: 0 24rpx;

    .card {
      position: relative;
      background: #FFFFFF;
      border-radius: 16rpx;
      margin-bottom: 16rpx;

      .card-info {
        position: relative;
        z-index: 2;
        padding: 30rpx;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;

        span {
          font-size: 28rpx;
          font-weight: 500;
          color: #333333;
          margin-bottom: 15rpx;

        }
      }

      .img-a {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 294rpx;
        z-index: 1;
      }
    }

    .collase-box {
      width: 100%;
      //
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx;
      box-sizing: border-box;

      .title-box {
        margin-bottom: 24rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        em {
          display: inline-block;
          width: 8rpx;
          height: 28rpx;
          background: #1172FD;
          margin-right: 12rpx;
        }

        label {
          font-size: 26rpx;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.9);
        }
      }

      .image-box {
        margin-top: 24rpx;
      }

      .chart {
        height: 350px;
      }
    }

  }

  .read-btn {
    margin-top: 40rpx;
    padding-bottom: 100rpx;
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .btnlist {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300rpx;
      height: 56rpx;
      background: #1172FD;
      border-radius: 12rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #FFFFFF;
    }
  }
}

.button_zdy {
	background-color: #3D6BFC;
	color: #FFFFFF;
	padding:  10rpx 30rpx;
	border-radius: 10rpx;
	font-size: 30rpx;
}
</style>
