	import {
		getDeviceState,
		getSysOrg
	} from "../api/device.js"

	import {
		getOrderState,
		colorBg,
	} from "../utils/orderUtils.js"

	// export function getSysOrgDict() {
	// 	getSysOrg().then(res => {
	// 		uni.setStorageSync('isDevice', false);
	// 		if (res.success) {
	// 			sysOrgStorage = res.data
	// 			uni.setStorageSync('sysOrg', sysOrgStorage);
	// 			console.log("get sysOrg back:" + JSON.stringify(sysOrgStorage));

	// 		} else {
	// 			console.log("get sysOrg 失败信息：" + res.msg);
	// 			this.$u.toast(res.msg, 1000);
	// 			return "";
	// 		}

	// 	});
	// };

	export async function getSysOrgString(orgId) { //将数字转为文字
		if (orgId == null) return ""
		// console.log("getsysOrg begin " + orgId);
		var sysOrgStr = "";
		// var sysOrgStorage = uni.getStorageSync('sysOrg');
		if (sysOrgStorage == null || sysOrgStorage.length == 0) {
			console.log("sysOrgStorage == null ");
			sysOrgStorage = await getSysOrg();
			// console.log("result:" + JSON.stringify(sysOrgStorage));



			if (sysOrgStorage == null || sysOrgStorage.length == 0) {
				console.log("after get sysOrgStorage == null ");
				return ""
			} else {
				sysOrgStorage.forEach(function(element, index, array) {
					if (element.orgId == orgId) {
						sysOrgStr = element.orgName;
						// console.info("get sysOrgStr:" + sysOrgStr); //当前元素的值
						return false; //中断循环
					}
					// console.info("index: " + index); //当前下标
					// 	// console.info(array); //数组本身
				});
				if (sysOrgStr == "") sysOrgStr = orgId;
				// console.log("getsysOrg end" + sysOrgStr);
				return sysOrgStr;
			}


		} else {

			// console.log("sysOrgStorage :" + JSON.stringify(sysOrgStorage));
			sysOrgStorage.forEach(function(element, index, array) {
				if (element.orgId == orgId) {
					sysOrgStr = element.orgName;
					// console.info("get sysOrgStr:" + sysOrgStr); //当前元素的值
					return false; //中断循环
				}
				// console.info("index: " + index); //当前下标
				// 	// console.info(array); //数组本身
			});
			if (sysOrgStr == "") sysOrgStr = orgId;
			// console.log("getsysOrg end" + sysOrgStr);
			return sysOrgStr;

		}


	};
	// 设备状态
	export async function getDeviceStateStr(state) { //将数字转为文字
		var stateStr = "";
		console.log("getDeviceState" + state);
		var deviceStateStorage = uni.getStorageSync('deviceState');
		if (deviceStateStorage == null || deviceStateStorage.length == 0) {
			console.log("deviceStateStorage == null ");
			deviceStateStorage = await getDeviceState();
			console.log("deviceStateStorage :" + JSON.stringify(deviceStateStorage));
			if (deviceStateStorage == null || deviceStateStorage.length == 0) {
				console.log("after get deviceStateStorage == null ");
				return ""
			} else {
				deviceStateStorage.forEach(function(element, index, array) {
					if (element.id == state) {
						stateStr = element.name;
						console.info("stateStr:" + stateStr); //当前元素的值
						return false; //中断循环
					}
					// console.info("index: " + index); //当前下标
					// 	// console.info(array); //数组本身
				});
				console.log("getdeviceState end" + stateStr);
				return stateStr
			}

		} else {
			console.log("deviceStateStorage saved:" + JSON.stringify(deviceStateStorage));

			deviceStateStorage.forEach(function(element, index, array) {
				if (element.id == state) {
					stateStr = element.name;
					console.info("stateStr:" + stateStr); //当前元素的值
					return false; //中断循环
				}
				// console.info("index: " + index); //当前下标
				// 	// console.info(array); //数组本身
			});
			console.log("getdeviceState end" + stateStr);
			return stateStr
		}
	};

	//巡检
	export function getInsColorBg(item) {
		console.log("getInsColorBg")
		if (item.orderState == null) {
			if (isActive(item.orderStartTime)) {
					console.log("#67C23A");
				return "#67C23A";
			} else {
				console.log("#aaaaaa");
				return "#aaaaaa";
			}
		} else
			return colorBg(item);
	};

	export function getInsColorBg2(item) {
		// console.log("getInsColorBg2")
		if (item.orderContentState != null && item.orderContentState == 0)
			return "#e73c11"
		else return "#00000000"
	};
	export function getInsOrderStateStr(item) { //工单流程状态
		// console.log("getInsOrderStateStr：" + item.orderState + " item.orderStartTime：" + item.orderStartTime)
		if (item.orderState == null || item.orderState == "null") {
			if (isActive(item.orderStartTime)) {
				console.log("已生效")
				return "已生效"
			} else {
				console.log("未生效")
				return "未生效"
			}
		} else
			return getOrderState(item);
	};
	export function isActive(time) {
		// console.log("isActive")
		if (time == null) return true
		else {
			let dateStart = new Date(time);
			let dateCurr = new Date();
			return dateStart.getTime() <= dateCurr.getTime()
		}
	};
	export function getInsDeviceStateName(item) { //设备状态
		console.log("getInsDeviceStateName" + item.orderContentState)
		if (item.orderContentState == null) return ""
		else {
			if (item.orderContentState == 0) return "异常"
			else if (item.orderContentState == 1) return "正常"
			else ""

		}

	};
	export function getInsDeviceStateColor(item) { //设备状态颜色
		console.log("getInsDeviceStateColor ：" + item.orderContentState)

		if (item.orderContentState == null) return "#666666";
		else {
			if (item.orderContentState == 0) return "#e73c11";
			else if (item.orderContentState == 1) return "#666666";
			else "#666666";

		}



	};
	export function getSendDeviceStateName(item) { //设备状态
		console.log("getSendDeviceStateName" + item.deviceState)
		if (item.deviceState == null) return ""
		else {
			if (item.deviceState == 0) return "异常"
			else if (item.deviceState == 1) return "正常"
			else item.deviceState

		}

	};
	export function getSendDeviceStateColor(item) { //设备状态颜色
		console.log("getSendDeviceStateColor ：" + item.deviceState)

		if (item.deviceState == null) return "#666666";
		else {
			if (item.deviceState == 0) return "#e73c11";
			else if (item.deviceState == 1) return "#666666";
			else "#666666";

		}



	};