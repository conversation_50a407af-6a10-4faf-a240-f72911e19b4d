<template>
  <uni-popup ref="container" :animation="true" :duration="300" :mask-click="false" class="navigation-popup"
             type="center">

    <!-- 弹窗标题栏 -->
    <view class="popup-header">
      <text class="popup-title">抄表导航</text>
      <view class="close-icon" @click="closeNavigation">
        <text class="close-text">×</text>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <!-- 地图加载状态 -->
      <view v-if="!mapReady" class="map-loading">
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">地图加载中...</text>
        </view>
      </view>

      <!-- 地图组件 -->
      <map v-show="mapReady" id="amapContainers" :enable-3D="true" :enable-overlooking="true" :enable-poi="true"
           :enable-rotate="true" :enable-scroll="true" :enable-zoom="true" :latitude="mapCenter.latitude"
           :longitude="mapCenter.longitude" :markers="markers" :polyline="polyline" :scale="mapScale"
           :show-location="true" :zIndex="1" class="map" @markertap="onMarkerTap" @regionchange="onRegionChange"
           @tap="onMapTap" @updated="onMapUpdated">
      </map>

      <!-- 地图控制按钮 -->
      <cover-view v-show="mapReady" class="map-controls">
        <cover-image class="location-btn" src="../../../static/icon/location.png"
                     @click="centerToLocation"></cover-image>
      </cover-view>


    </view>
  </uni-popup>
</template>

<script>
// 导航组件 - 专用于抄表员导航功能

// 高德地图配置
const amapConfig = {
  webServiceKey: 'da939956dea92fa861877892ce50eb29',
  apiConfig: {
    walkingRoute: {
      baseUrl: 'https://restapi.amap.com/v3/direction/walking',
      timeout: 10000,
      extensions: 'all'
    }
  },
  mapStyle: {
    polyline: {
      walking: {
        color: '#1890ff',
        width: 6,
        borderColor: '#ffffff',
        borderWidth: 2,
        arrowLine: true,
        dottedLine: false
      },
      fallback: {
        color: '#ff4d4f',
        width: 4,
        borderColor: '#ffffff',
        borderWidth: 1,
        arrowLine: true,
        dottedLine: true
      }
    }
  }
};

export default {
  data() {


    return {
      // 地图中心点（山西太原坐标）
      mapCenter: {
        longitude: 112.53,
        latitude: 37.87
      },
      // 地图缩放级别
      mapScale: 13,
      // 标记点数组
      markers: [],
      // 地图上下文
      mapContext: null,
      // 用户位置
      userLocation: null,
      // 用户位置标记
      userLocationMarker: null,
      // 水表位置标记
      meterLocationMarker: null,
      // 路线规划相关
      polyline: [],
      // 水表数据
      fjdsj: {},
      // 导航状态
      isNavigating: false,
      // 路线距离信息
      routeDistance: '',
      // 路线步骤信息
      routeSteps: [],
      // 地图准备状态
      mapReady: false,
      // 弹窗显示状态
      popupVisible: false
    };
  },

  async onLoad(options) {
    // 接收传入的水表数据
    if (options.fjdsj) {
      this.fjdsj = JSON.parse(decodeURIComponent(options.fjdsj));
    }
    // 初始化地图上下文
    this.initMapContext();
  },

  onReady() {
    // 页面渲染完成后初始化地图上下文
    this.initMapContext();
  },

  onUnload() {
    // 清理资源
    this.mapContext = null;
  },


  methods: {
    // 初始化导航地图
    async initNavigationMap() {
      try {
        uni.showLoading({
          title: '正在获取位置...'
        });

        // 获取抄表员当前位置
        await this.getCurrentLocationForNavigation();

        // 创建抄表员和水表位置标记
        this.createNavigationMarkers();

        // 规划路线
        await this.planRoute();

        // 先打开弹窗
        this.popupVisible = true;
        this.$refs.container.open();

        // 等待弹窗动画完成后再显示地图
        setTimeout(async () => {
          try {
            // 第一步：显示地图加载状态
            console.log('开始初始化地图...');

            // 第二步：显示地图组件
            this.mapReady = true;

            // 第三步：等待地图DOM渲染完成
            await this.$nextTick();
            await new Promise(resolve => setTimeout(resolve, 400));

            // 第四步：初始化地图上下文
            const initSuccess = this.initMapContext();
            if (!initSuccess) {
              throw new Error('地图上下文初始化失败');
            }

            // 第五步：等待地图完全加载
            await new Promise(resolve => setTimeout(resolve, 300));

            // 第六步：调整地图视野
            this.adjustMapView();

            console.log('地图初始化完成');
            uni.hideLoading();
            uni.showToast({
              title: '路线规划完成',
              icon: 'success'
            });
          } catch (error) {
            console.error('地图初始化失败:', error);
            this.mapReady = false;
            uni.hideLoading();
            uni.showToast({
              title: '地图加载失败，请重试',
              icon: 'none'
            });
          }
        }, 300);

      } catch (error) {
        console.error('初始化导航失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '导航初始化失败',
          icon: 'none'
        });
      }
    },

    // 获取抄表员当前位置（用于导航）
    getCurrentLocationForNavigation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            this.userLocation = {
              latitude: res.latitude,
              longitude: res.longitude
            };
            console.log('获取到抄表员位置:', this.userLocation);
            resolve(res);
          },
          fail: (err) => {
            console.error('获取位置失败:', err);
            reject(err);
          }
        });
      });
    },

    // 创建导航标记点（抄表员位置和水表位置）
    createNavigationMarkers() {
      const markers = [];


      // 抄表员位置标记
      if (this.userLocation) {
        this.userLocationMarker = {
          id: 'user-location',
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude,
          title: '抄表员位置',
          iconPath: '/static/icons/marker-normal.png',
          width: 32,
          height: 32,
          callout: {
            content: '抄表员位置',
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#1890ff',
            padding: 6,
            display: 'ALWAYS'
          }
        };
        markers.push(this.userLocationMarker);
      }

      // 水表位置标记
      if (this.fjdsj.meterLongitude && this.fjdsj.meterLatitude) {
        this.meterLocationMarker = {
          id: 'meter-location',
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude),
          title: '水表位置',
          iconPath: '/static/icons/marker-error.png',
          width: 32,
          height: 32,
          callout: {
            content: `目标水表\n${this.fjdsj.meterAddress || ''}`,
            color: '#ffffff',
            fontSize: 12,
            borderRadius: 6,
            bgColor: '#52c41a',
            padding: 6,
            display: 'ALWAYS'
          }
        };
        markers.push(this.meterLocationMarker);
      }

      this.markers = markers;
      console.log('创建导航标记点:', markers);
      console.log('当前标记点数量:', this.markers.length);
      console.log('导航面板显示状态:', this.showNavigationPanel);

      // 强制触发视图更新
      this.$nextTick(() => {
        console.log('下一帧导航面板显示状态:', this.showNavigationPanel);
      });
    },

    // 规划路线 - 使用高德地图API
    async planRoute() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        console.error('缺少位置信息，无法规划路线');
        return;
      }

      try {
        const startPoint = {
          longitude: this.userLocation.longitude,
          latitude: this.userLocation.latitude
        };

        const endPoint = {
          longitude: parseFloat(this.fjdsj.meterLongitude),
          latitude: parseFloat(this.fjdsj.meterLatitude)
        };

        console.log('开始规划步行路线:', {
          startPoint,
          endPoint
        });

        // 调用高德地图步行路线规划API
        const routeResult = await this.getWalkingRoute(startPoint, endPoint);
        console.log(routeResult)
        debugger
        if (routeResult && routeResult.success) {
          // 使用高德API返回的路线
          this.polyline = routeResult.polyline;
          this.routeDistance = routeResult.distance;

          // 处理路线步骤信息
          if (routeResult.steps) {
            this.routeSteps = this.processRouteSteps(routeResult.steps);
          }

          console.log('高德路线规划成功:', {
            distance: this.routeDistance,
            duration: routeResult.duration,
            polylineCount: this.polyline.length,
            stepsCount: routeResult.steps ? routeResult.steps.length : 0
          });

          // 显示距离和时间信息
          uni.showToast({
            title: `步行${this.routeDistance}，约${routeResult.duration}`,
            icon: 'none',
            duration: 4000
          });
        } else {
          // 高德API失败时使用直线路线作为备用
          console.warn('高德路线规划失败，使用直线路线');
          this.createSimpleRoute(startPoint, endPoint);
        }

      } catch (error) {
        console.error('路线规划失败:', error);
        // 创建简单的直线路线作为备用
        this.createSimpleRoute();
      }
    },

    // 调用高德地图步行路线规划API
    async getWalkingRoute(startPoint, endPoint) {
      try {
        // 检查API密钥配置
        if (!amapConfig.webServiceKey || amapConfig.webServiceKey === 'YOUR_AMAP_WEB_SERVICE_KEY') {
          console.warn('高德地图API密钥未配置，请在 config/amap.js 中设置正确的密钥');
          return {
            success: false,
            error: 'API密钥未配置'
          };
        }

        // 构建请求URL
        const origin = `${startPoint.longitude},${startPoint.latitude}`;
        const destination = `${endPoint.longitude},${endPoint.latitude}`;
        const config = amapConfig.apiConfig.walkingRoute;

        const url = `${config.baseUrl}?` +
            `origin=${origin}&` +
            `destination=${destination}&` +
            `key=${amapConfig.webServiceKey}&` +
            `output=json&` +
            `extensions=${config.extensions}`;

        console.log('高德API请求URL:', url);

        // 发起请求
        const [a, response] = await uni.request({
          url: url,
          method: 'GET',
          timeout: config.timeout
        });

        console.log('高德API响应:', response);


        if (response.statusCode === 200 && response.data.status === '1') {
          console.log('高德API调用成功');
          const route = response.data.route;
          if (route && route.paths && route.paths.length > 0) {
            const path = route.paths[0];

            // 解析路线坐标点
            const polylinePoints = this.parseAmapPolyline(path.steps);

            // 创建地图polyline对象
            const walkingStyle = amapConfig.mapStyle.polyline.walking;
            const polyline = [{
              points: polylinePoints,
              color: walkingStyle.color,
              width: walkingStyle.width,
              dottedLine: walkingStyle.dottedLine,
              arrowLine: walkingStyle.arrowLine,
              borderColor: walkingStyle.borderColor,
              borderWidth: walkingStyle.borderWidth
            }];

            // 格式化距离和时间
            const distance = parseInt(path.distance);
            const duration = parseInt(path.duration);
            const formattedDistance = distance > 1000 ?
                `${(distance / 1000).toFixed(1)}公里` :
                `${distance}米`;

            const formattedDuration = duration > 3600 ?
                `${Math.floor(duration / 3600)}小时${Math.floor((duration % 3600) / 60)}分钟` :
                `${Math.floor(duration / 60)}分钟`;

            console.log('路线解析成功:', {
              pointsCount: polylinePoints.length,
              distance: formattedDistance,
              duration: formattedDuration,
              stepsCount: path.steps.length,
              origin: route.origin,
              destination: route.destination
            });

            return {
              success: true,
              polyline: polyline,
              distance: formattedDistance,
              duration: formattedDuration,
              steps: path.steps,
              origin: route.origin,
              destination: route.destination,
              rawData: response.data
            };
          }
        }

        return {
          success: false,
          error: '路线数据解析失败'
        };

      } catch (error) {
        console.error('高德API调用失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    },

    // 解析高德地图返回的路线坐标
    parseAmapPolyline(steps) {
      const points = [];
      let totalSteps = 0;
      let validSteps = 0;

      steps.forEach((step, index) => {
        totalSteps++;
        if (step.polyline && step.polyline.trim()) {
          validSteps++;
          try {
            // 高德返回的polyline格式: "lng,lat;lng,lat;..."
            const coords = step.polyline.split(';');
            coords.forEach(coord => {
              const coordTrimmed = coord.trim();
              if (coordTrimmed) {
                const [lng, lat] = coordTrimmed.split(',');
                if (lng && lat) {
                  const longitude = parseFloat(lng);
                  const latitude = parseFloat(lat);
                  // 验证坐标有效性
                  if (!isNaN(longitude) && !isNaN(latitude) &&
                      longitude >= -180 && longitude <= 180 &&
                      latitude >= -90 && latitude <= 90) {
                    points.push({
                      longitude: longitude,
                      latitude: latitude
                    });
                  }
                }
              }
            });
          } catch (error) {
            console.warn(`解析第${index + 1}步路线坐标失败:`, error, step);
          }
        }
      });

      console.log(`路线坐标解析完成: 总步数${totalSteps}, 有效步数${validSteps}, 坐标点数${points.length}`);

      // 如果没有解析到坐标点，返回起点和终点
      if (points.length === 0) {
        console.warn('未解析到有效坐标点，使用起点和终点作为路线');
        if (this.userLocation && this.fjdsj.meterLongitude && this.fjdsj.meterLatitude) {
          points.push(
              {
                longitude: this.userLocation.longitude,
                latitude: this.userLocation.latitude
              },
              {
                longitude: parseFloat(this.fjdsj.meterLongitude),
                latitude: parseFloat(this.fjdsj.meterLatitude)
              }
          );
        }
      }

      return points;
    },

    // 处理路线步骤信息
    processRouteSteps(steps) {
      if (!steps || steps.length === 0) return [];

      const processedSteps = steps.map((step, index) => {
        return {
          stepNumber: index + 1,
          instruction: step.instruction || '继续前行',
          road: step.road || '',
          distance: step.distance ? `${step.distance}米` : '',
          duration: step.duration ? `${Math.ceil(step.duration / 60)}分钟` : '',
          orientation: step.orientation || '',
          action: step.action || ''
        };
      });

      console.log('处理路线步骤:', processedSteps.length, '步');
      return processedSteps;
    },

    // 创建简单路线（备用方案）
    createSimpleRoute(startPoint, endPoint) {
      if (!startPoint && (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude)) {
        return;
      }

      const start = startPoint || {
        longitude: this.userLocation.longitude,
        latitude: this.userLocation.latitude
      };

      const end = endPoint || {
        longitude: parseFloat(this.fjdsj.meterLongitude),
        latitude: parseFloat(this.fjdsj.meterLatitude)
      };

      const fallbackStyle = amapConfig.mapStyle.polyline.fallback;
      this.polyline = [{
        points: [start, end],
        color: fallbackStyle.color,
        width: fallbackStyle.width,
        dottedLine: fallbackStyle.dottedLine,
        arrowLine: fallbackStyle.arrowLine,
        borderColor: fallbackStyle.borderColor,
        borderWidth: fallbackStyle.borderWidth
      }];

      // 计算并显示直线距离
      const distance = this.calculateDistance(start, end);
      this.routeDistance = distance > 1000 ?
          `${(distance / 1000).toFixed(1)}公里` :
          `${distance.toFixed(0)}米`;

      uni.showToast({
        title: `直线距离约${this.routeDistance}`,
        icon: 'none',
        duration: 2000
      });
    },

    // 计算两点间距离（米）
    calculateDistance(point1, point2) {
      const R = 6371000; // 地球半径（米）
      const lat1Rad = point1.latitude * Math.PI / 180;
      const lat2Rad = point2.latitude * Math.PI / 180;
      const deltaLatRad = (point2.latitude - point1.latitude) * Math.PI / 180;
      const deltaLngRad = (point2.longitude - point1.longitude) * Math.PI / 180;

      const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
          Math.cos(lat1Rad) * Math.cos(lat2Rad) *
          Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      return R * c;
    },

    // 调整地图视野以显示完整路线
    adjustMapView() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        console.warn('调整地图视野失败：缺少位置信息');
        return;
      }

      if (!this.mapReady || !this.mapContext) {
        console.warn('调整地图视野失败：地图未准备就绪');
        return;
      }

      const userLng = this.userLocation.longitude;
      const userLat = this.userLocation.latitude;
      const meterLng = parseFloat(this.fjdsj.meterLongitude);
      const meterLat = parseFloat(this.fjdsj.meterLatitude);

      // 计算中心点
      const centerLng = (userLng + meterLng) / 2;
      const centerLat = (userLat + meterLat) / 2;

      // 计算合适的缩放级别
      const lngDiff = Math.abs(userLng - meterLng);
      const latDiff = Math.abs(userLat - meterLat);
      const maxDiff = Math.max(lngDiff, latDiff);

      let scale = 16;
      if (maxDiff > 0.01) scale = 14;
      if (maxDiff > 0.02) scale = 13;
      if (maxDiff > 0.05) scale = 12;
      if (maxDiff > 0.1) scale = 11;

      // 更新地图中心和缩放级别
      this.mapCenter = {
        longitude: centerLng,
        latitude: centerLat
      };
      this.mapScale = scale;

      console.log('调整地图视野:', this.mapCenter, 'scale:', scale);
    },

    // 开始导航
    startNavigation() {
      if (!this.userLocation || !this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        uni.showToast({
          title: '位置信息不完整',
          icon: 'none'
        });
        return;
      }

      this.isNavigating = true;

      // 可以在这里添加实时位置更新逻辑
      uni.showModal({
        title: '开始导航',
        content: '是否要打开外部地图应用进行导航？',
        success: (res) => {
          if (res.confirm) {
            this.openExternalMap();
          }
        }
      });
    },

    // 打开外部地图应用
    openExternalMap() {
      const meterLng = this.fjdsj.meterLongitude;
      const meterLat = this.fjdsj.meterLatitude;
      const meterName = this.fjdsj.meterAddress || '水表位置';

      // #ifdef APP-PLUS
      // 构建高德地图URL
      const amapUrl =
          `amaps://route/plan/?sid=BGVIS1&slat=${this.userLocation.latitude}&slon=${this.userLocation.longitude}&sname=当前位置&did=BGVIS2&dlat=${meterLat}&dlon=${meterLng}&dname=${encodeURIComponent(meterName)}&dev=0&t=0`;

      // 构建百度地图URL（备用）
      const baiduUrl =
          `baidumap://map/direction?origin=${this.userLocation.latitude},${this.userLocation.longitude}&destination=${meterLat},${meterLng}&mode=walking`;

      // 尝试打开高德地图
      plus.runtime.openURL(amapUrl, () => {
        console.log('打开高德地图失败，尝试百度地图');
        // 如果高德地图打开失败，尝试百度地图
        plus.runtime.openURL(baiduUrl, () => {
          console.log('打开百度地图也失败');
          uni.showToast({
            title: '请安装地图应用',
            icon: 'none'
          });
        });
      });
      // #endif

      // #ifdef H5
      // H5环境下打开网页版地图
      const webUrl =
          `https://uri.amap.com/navigation?from=${this.userLocation.longitude},${this.userLocation.latitude},当前位置&to=${meterLng},${meterLat},${encodeURIComponent(meterName)}&mode=walking`;
      window.open(webUrl, '_blank');
      // #endif
    },

    // 直接跳转高德地图APP
    openAmapApp() {
      // 显示加载提示
      uni.showLoading({
        title: '正在打开高德地图...',
        mask: true
      });

      // 检查是否有必要的位置信息
      if (!this.userLocation) {
        uni.hideLoading();
        uni.showToast({
          title: '请先获取当前位置',
          icon: 'none'
        });
        return;
      }

      if (!this.fjdsj.meterLongitude || !this.fjdsj.meterLatitude) {
        uni.hideLoading();
        uni.showToast({
          title: '水表位置信息不完整',
          icon: 'none'
        });
        return;
      }

      const meterLng = this.fjdsj.meterLongitude;
      const meterLat = this.fjdsj.meterLatitude;
      const meterName = this.fjdsj.meterAddress || '水表位置';

      // #ifdef APP-PLUS
      // 构建高德地图步行导航URL
      const amapUrl = `amaps://route/plan/?` +
          `sid=BGVIS1&` +
          `slat=${this.userLocation.latitude}&` +
          `slon=${this.userLocation.longitude}&` +
          `sname=当前位置&` +
          `did=BGVIS2&` +
          `dlat=${meterLat}&` +
          `dlon=${meterLng}&` +
          `dname=${encodeURIComponent(meterName)}&` +
          `dev=0&` +
          `t=0`; // t=0表示步行导航

      console.log('高德地图URL:', amapUrl);

      // 尝试打开高德地图
      plus.runtime.openURL(amapUrl, (error) => {
        uni.hideLoading();
        console.log('打开高德地图失败:', error);
        uni.showModal({
          title: '提示',
          content: '未检测到高德地图APP，是否前往应用商店下载？',
          success: (res) => {
            if (res.confirm) {
              // 跳转到应用商店下载高德地图
              // #ifdef APP-PLUS-ANDROID
              plus.runtime.openURL('market://details?id=com.autonavi.minimap');
              // #endif
              // #ifdef APP-PLUS-IOS
              plus.runtime.openURL('https://apps.apple.com/cn/app/id461703208');
              // #endif
            }
          }
        });
      });

      // 延迟隐藏loading，给用户足够的反馈时间
      setTimeout(() => {
        uni.hideLoading();
      }, 1000);
      // #endif

      // #ifdef H5
      // H5环境下打开高德地图网页版
      const webUrl = `https://uri.amap.com/navigation?` +
          `from=${this.userLocation.longitude},${this.userLocation.latitude},当前位置&` +
          `to=${meterLng},${meterLat},${encodeURIComponent(meterName)}&` +
          `mode=walking`;

      window.open(webUrl, '_blank');
      uni.hideLoading();
      uni.showToast({
        title: '已打开高德地图网页版',
        icon: 'success'
      });
      // #endif

      // #ifdef MP
      // 小程序环境下提示用户
      uni.hideLoading();
      uni.showModal({
        title: '提示',
        content: '小程序环境下无法直接跳转APP，请手动打开高德地图进行导航',
        showCancel: false
      });
      // #endif
    },

    // 清除路线
    clearRoute() {
      this.polyline = [];
      this.isNavigating = false;
      this.routeDistance = '';
      uni.showToast({
        title: '已清除路线',
        icon: 'success'
      });
    },

    // 重新规划路线
    async replanRoute() {
      uni.showLoading({
        title: '重新规划中...'
      });

      try {
        await this.getCurrentLocationForNavigation();
        this.createNavigationMarkers();
        await this.planRoute();
        this.adjustMapView();

        uni.hideLoading();
        uni.showToast({
          title: '路线已更新',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '重新规划失败',
          icon: 'none'
        });
      }
    },
    openEvent(data) {
      console.log('接收到水表数据:', data);
      this.fjdsj = data;

      // 检查水表坐标是否存在
      if (!data.meterLongitude || !data.meterLatitude) {
        uni.showToast({
          title: '水表位置信息不完整',
          icon: 'none'
        });
        return;
      }

      // 获取抄表员当前位置并显示导航
      this.initNavigationMap();
    },
    // 初始化地图上下文
    initMapContext() {
      try {
        // #ifdef APP-PLUS
        this.mapContext = uni.createMapContext('amapContainers', this);
        console.log('高德地图上下文初始化成功');
        // #endif

        // #ifdef H5 || MP
        this.mapContext = uni.createMapContext('amapContainers', this);
        console.log('地图上下文初始化成功');
        // #endif

        return true;
      } catch (error) {
        console.error('地图上下文初始化失败:', error);
        return false;
      }
    },


    // 标记点点击事件
    onMarkerTap(e) {
      console.log('标记点被点击:', e.detail);
      const markerId = e.detail.markerId;
      const marker = this.markers.find(m => m.id === markerId);

      if (marker) {
        uni.showModal({
          title: marker.title || '位置信息',
          content: `经度: ${marker.longitude}\n纬度: ${marker.latitude}`,
          showCancel: false
        });
      }
    },

    // 地图区域变化事件
    onRegionChange(e) {
      if (e.type === 'end') {
        console.log('地图区域变化:', e.detail);
      }
    },

    // 地图点击事件
    onMapTap(e) {
      console.log('地图点击:', e.detail);
    },

    // 地图更新事件
    onMapUpdated(e) {
      console.log('地图更新完成:', e);
      // 地图更新完成后，确保地图已准备就绪
      if (!this.mapReady) {
        this.mapReady = true;
      }
    },

    // 定位到当前位置
    // 定位到当前位置
    centerToLocation() {
      console.log("这里是否执行");
      if (this.userLocation) {
        // 更新地图中心点
        this.mapCenter = {
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude
        };
        // 放大地图层级
        this.mapScale = 17;
        // 添加红色定位点标记
        this.addUserLocationMarker();
        console.log('定位到当前位置:', this.mapCenter);
        this.mapContext.moveToLocation();
      } else {
        this.getCurrentLocation();
        uni.showToast({
          title: '正在获取位置...',
          icon: 'loading'
        });
      }
    },
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };

          // 更新地图中心点到当前位置
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          this.mapScale = 16;

          // 添加用户位置标记
          this.addUserLocationMarker();

          uni.showToast({
            title: '定位成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          uni.showToast({
            title: '获取位置失败',
            icon: 'none'
          });
        }
      });
    },
    // 添加用户位置标记
    addUserLocationMarker() {
      if (!this.userLocation) return;

      // 创建红色定位点标记
      this.userLocationMarker = {
        id: 'user-location',
        longitude: this.userLocation.longitude,
        latitude: this.userLocation.latitude,
        title: '我的位置',
        iconPath: '/static/icons/user-location.png', // 使用红色定位图标
        width: 36,
        height: 36,
        callout: {
          content: '我的位置',
          color: '#ffffff',
          fontSize: 12,
          borderRadius: 6,
          bgColor: '#ff4d4f', // 红色背景
          padding: 8,
          borderWidth: 1,
          borderColor: '#ff4d4f',
          display: 'ALWAYS',
          textAlign: 'center'
        },
        anchor: {
          x: 0.5,
          y: 1
        }
      };

      // 将用户位置标记添加到标记数组中
      this.updateMarkersWithUserLocation();
    },

    // 更新标记数组，包含用户位置标记
    updateMarkersWithUserLocation() {
      // 移除之前的用户位置标记
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');

      // 添加新的用户位置标记
      if (this.userLocationMarker) {
        this.markers = [...this.markers, this.userLocationMarker];
      }
    },

    // 移除用户位置标记
    removeUserLocationMarker() {
      this.markers = this.markers.filter(marker => marker.id !== 'user-location');
      this.userLocationMarker = null;
    },


    // 关闭导航弹窗
    closeNavigation() {
      this.$refs.container.close();
      // 清理导航状态
      this.isNavigating = false;
      this.mapReady = false;
      this.popupVisible = false;
      this.polyline = [];
      this.routeDistance = '';
      this.routeSteps = [];
      this.markers = [];
      this.userLocation = null;
      this.userLocationMarker = null;
      this.meterLocationMarker = null;
    }
  }
};
</script>

<style lang="scss" scoped>
// 弹窗样式
.navigation-popup {
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: #1890ff;
    border-radius: 12px 12px 0 0;

    .popup-title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
    }

    .close-icon {
      width: 30px;
      height: 30px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .close-text {
        color: #ffffff;
        font-size: 20px;
        font-weight: bold;
        line-height: 1;
      }

      &:active {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .map-container {
    width: 90vw;
    height: 70vh;
    position: relative;
    background-color: #f5f5f5;
    border-radius: 0 0 12px 12px;
    overflow: hidden;

    // 地图加载状态
    .map-loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 5;

      .loading-content {
        text-align: center;
        color: #666;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #e0e0e0;
          border-top: 3px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        .loading-text {
          font-size: 14px;
          color: #666;
        }
      }
    }

    .map {
      width: 100%;
      height: 100%;
      position: relative;
    }

    // 地图控制按钮
    .map-controls {
      position: absolute;
      right: 15px;
      bottom: 100px;
      z-index: 1000000;
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: flex-end;

      .location-btn {
        width: 44px;
        height: 44px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 50%;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(0, 0, 0, 0.08);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
        }

        &:active {
          transform: scale(0.95) translateY(0);
          background: rgba(255, 255, 255, 1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }

      .amap-btn {
        padding: 10px 14px;
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        border-radius: 22px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 15px rgba(24, 144, 255, 0.4);
        border: 1px solid rgba(24, 144, 255, 0.3);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
        min-width: 70px;
        gap: 3px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(24, 144, 255, 0.5);
        }

        &:active {
          transform: scale(0.95) translateY(0);
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.5);
        }

        .amap-icon {
          width: 16px;
          height: 16px;
          filter: brightness(0) invert(1); // 将图标变为白色
        }

        .amap-text {
          color: #ffffff;
          font-size: 10px;
          font-weight: 600;
          text-align: center;
          line-height: 1;
          white-space: nowrap;
        }
      }
    }


  }

}

// 弹窗整体样式
/deep/ .uni-popup {
  .uni-popup__wrapper {
    .uni-popup__wrapper-box {
      background: transparent;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      max-width: 90vw;
      max-height: 80vh;
    }
  }
}

// 动画定义
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
