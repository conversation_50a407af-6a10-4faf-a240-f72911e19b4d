<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="nav-bar">
        <uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
        <text class="title">我的巡检任务</text>
        <!-- <uni-icons type="reload" size="20" color="#fff" @click="refreshTasks"></uni-icons> -->
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <!-- 高德地图 -->
      <map id="inspectionMap" class="map" :longitude="mapCenter.longitude" :latitude="mapCenter.latitude"
        :scale="mapScale" :markers="markers" :show-location="true" :enable-3D="false" :enable-overlooking="false"
        :enable-zoom="true" :enable-scroll="true" :enable-rotate="false" @markertap="onMarkerTap"
        @regionchange="onRegionChange" @tap="onMapTap">
        <!-- 地图控件 -->
        <cover-view class="map-controls">
          <cover-image class="location-btn" src="../../../static/icon/location.png"
            @click="centerToLocation"></cover-image>
        </cover-view>

      </map>

      <!-- 地图加载错误提示 -->
      <view v-if="mapError" class="map-error">
        <uni-icons type="info" size="24" color="#999"></uni-icons>
        <text class="error-text">地图加载失败，请检查网络连接</text>
        <button class="retry-btn" @click="retryLoadMap">重试</button>
      </view>
    </view>

    <!-- 巡检点信息面板 -->
    <view v-if="selectedMarker" class="info-panel">
      <view class="panel-header">
        <text class="panel-title">{{ selectedMarker.title }}</text>
        <uni-icons type="closeempty" size="20" color="#666" @click="closeInfoPanel"></uni-icons>
      </view>
      <view class="panel-content">
        <view class="info-item">
          <text class="info-label">状态：</text>
          <text class="info-value" :class="getStatusClass(selectedMarker.status)">{{
            getStatusText(selectedMarker.status) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">位置：</text>
          <text class="info-value">{{ selectedMarker.address }}</text>
        </view>
      </view>
      <view class="panel-actions">
        <button class="action-btn primary" @click="startInspection">开始巡检</button>
        <button class="action-btn secondary" @click="viewHistory">查看历史</button>
      </view>
    </view>

    <!-- 任务信息卡片 -->
    <view class="task-info-card">
      <view class="card-header">
        <text class="task-name">{{ taskInfo.name }}</text>
        <text class="task-status" :class="getTaskStatusClass(taskInfo.status)">{{ getTaskStatusText(taskInfo.status)
        }}</text>
      </view>
      <view class="card-content">
        <view class="info-row">
          <text class="label">巡检进度：</text>
          <text class="value">{{ taskInfo.completed }}/{{ taskInfo.total }}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: taskInfo.progressPercent + '%' }"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getInspectionTaskDetail, inspectionPointArrival } from '@/api/inspection.js';
import { wgs84ToGcj02 } from '@/utils/coordinateUtils.js';

export default {
  name: 'InspectionTaskMain',
  data() {
    return {
      taskId: '',
      mapError: false,
      mapScale: 16,
      mapCenter: {
        longitude: 112.549248,
        latitude: 37.857014
      },
      mapContext: null,
      userLocation: null,
      selectedMarker: null,
      // 添加定位相关变量
      locationTimer: null,
      isLocationActive: false,
      userLocationMarker: null,
      checkedPoints: new Set(), // 记录已检测过的点位
      taskInfo: {
        id: 1,
        name: '日巡-片区关键设施巡检',
        status: 'in_progress',
        completed: 3,
        total: 8,
        progressPercent: 37.5
      },
      markers: [
        // 这些静态数据需要转换
        {
          id: 1,
          // 原始WGS84坐标
          originalLongitude: 112.549248,
          originalLatitude: 37.857014,
          longitude: 0, // 转换后的坐标
          latitude: 0,
          title: '巡检点1',
          iconPath: '/static/icons/inspection-point.png',
          width: 30,
          height: 30
        }
      ],
    }
  },
  onLoad(options) {
    this.convertStaticCoordinates();
    if (options.taskId) {
      this.taskId = options.taskId
      this.loadTaskData()
    }
    this.getCurrentLocation()
    this.checkLocationStatus()
  },
  onReady() {
    this.mapContext = uni.createMapContext('inspectionMap', this)
  },
  onUnload() {
    // 页面销毁时清理定时器
    this.stopLocationTracking()
  },
  methods: {
    // 转换静态坐标数据
    convertStaticCoordinates() {
      this.markers = this.markers.map(marker => {
        if (marker.originalLongitude && marker.originalLatitude) {
          const converted = wgs84ToGcj02(marker.originalLongitude, marker.originalLatitude);
          return {
            ...marker,
            longitude: converted.lng,
            latitude: converted.lat
          };
        }
        return marker;
      });
    },
    // 获取当前位置
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          }
          // 将地图中心点设置为当前定位点
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          }
          // 放大地图层级以便更好地显示
          this.mapScale = 17;
          console.log('地图中心点已移动到定位点:', this.mapCenter)
        },
        fail: (err) => {
          console.error('获取位置失败:', err)
          uni.showToast({
            title: '获取位置失败',
            icon: 'none'
          })
        }
      })
    },

    // 定位到当前位置
    centerToLocation() {
      if (this.userLocation) {
        this.mapCenter = {
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude
        }
        // 放大地图层级
        this.mapScale = 17;

        // 添加用户位置标记
        this.updateUserLocationMarker();

        this.mapContext.moveToLocation()
      } else {
        // 如果没有用户位置，先获取位置
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            this.userLocation = {
              latitude: res.latitude,
              longitude: res.longitude
            }
            this.mapCenter = {
              latitude: res.latitude,
              longitude: res.longitude
            }
            this.mapScale = 17;

            // 添加用户位置标记
            this.updateUserLocationMarker();

            console.log('定位成功并添加标记:', this.userLocation)
            uni.showToast({
              title: '定位成功',
              icon: 'success'
            })
          },
          fail: (err) => {
            console.error('获取位置失败:', err)
            uni.showToast({
              title: '获取位置失败',
              icon: 'none'
            })
          }
        })
      }
    },

    // 放大地图
    zoomIn() {
      if (this.mapScale < 20) {
        this.mapScale += 1
      }
    },

    // 缩小地图
    zoomOut() {
      if (this.mapScale > 5) {
        this.mapScale -= 1
      }
    },

    // 标记点击事件
    onMarkerTap(e) {
      const markerId = e.detail.markerId
      const marker = this.markers.find(m => m.id === markerId)
      if (marker) {
        this.selectedMarker = marker
      }
    },

    // 地图区域变化事件
    onRegionChange(e) {
      if (e.detail.type === 'end') {
        this.mapCenter = {
          latitude: e.detail.centerLocation.latitude,
          longitude: e.detail.centerLocation.longitude
        }
      }
    },

    // 地图点击事件
    onMapTap() {
      this.selectedMarker = null
    },

    // 关闭信息面板
    closeInfoPanel() {
      this.selectedMarker = null
    },
    // 检查定位状态
    // 在 map.vue 的 onLoad 或 onShow 方法中
    checkLocationStatus() {
      // 检查定位是否开启
      const isLocationActive = uni.getStorageSync('inspectionLocationActive')
      if (isLocationActive) {
        // 获取当前位置信息
        const currentLocation = uni.getStorageSync('currentInspectionLocation')
        if (currentLocation) {
          this.updateUserLocation(currentLocation)
        }

        // 监听位置更新（而不是启动新的定位）
        this.startLocationMonitoring()
      }
    },
    // 监听位置更新的方法
    startLocationMonitoring() {
      // 每隔一段时间检查本地存储中的位置是否更新
      this.locationMonitorTimer = setInterval(() => {
        const currentLocation = uni.getStorageSync('currentInspectionLocation')
        if (currentLocation && currentLocation.timestamp !== this.lastLocationTimestamp) {
          this.updateUserLocation(currentLocation)
          this.lastLocationTimestamp = currentLocation.timestamp
        }
      }, 5000) // 5秒检查一次本地存储
    },

    // 开始定位追踪
    startLocationTracking() {
      this.isLocationActive = true

      // 立即获取一次位置
      this.getLocationAndUpdate()

      // 设置定时器，每20秒获取一次位置
      this.locationTimer = setInterval(() => {
        this.getLocationAndUpdate()
      }, 20000)
    },

    // 停止定位追踪
    stopLocationTracking() {
      this.isLocationActive = false
      if (this.locationTimer) {
        clearInterval(this.locationTimer)
        this.locationTimer = null
      }
      // 移除用户位置标记
      this.removeUserLocationMarker()
    },

    // 更新用户位置
    updateUserLocation(locationData) {
      if (!locationData) return

      // 更新用户位置数据
      this.userLocation = {
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        timestamp: locationData.timestamp
      }

      // 更新地图中心点为当前位置
      this.mapCenter = {
        latitude: locationData.latitude,
        longitude: locationData.longitude
      }

      // 更新用户位置标记
      this.updateUserLocationMarker()

      // 检测与巡检点的距离
      this.checkDistanceToInspectionPoints()

      console.log('更新用户位置:', this.userLocation)
    },

    // 更新用户位置标记
    updateUserLocationMarker() {
      if (!this.userLocation) return

      // 创建或更新用户位置标记
      this.userLocationMarker = {
        id: 'user_location',
        latitude: this.userLocation.latitude,
        longitude: this.userLocation.longitude,
        title: '我的位置',
        iconPath: '/static/icons/user-location.png', // 红色定位图标
        width: 32,
        height: 32,
        zIndex: 999
      }

      // 更新markers数组
      this.updateMarkersWithUserLocation()
    },

    // 移除用户位置标记
    removeUserLocationMarker() {
      this.userLocationMarker = null
      this.updateMarkersWithUserLocation()
    },

    // 更新标记点数组（包含用户位置）
    updateMarkersWithUserLocation() {
      // 过滤掉之前的用户位置标记
      const inspectionMarkers = this.markers.filter(marker => marker.id !== 'user_location')

      // 如果有用户位置标记，添加到数组中
      if (this.userLocationMarker) {
        this.markers = [...inspectionMarkers, this.userLocationMarker]
      } else {
        this.markers = inspectionMarkers
      }
    },

    // 检测与巡检点的距离
    checkDistanceToInspectionPoints() {
      if (!this.userLocation) return

      this.markers.forEach(marker => {
        // 跳过用户位置标记
        if (marker.id === 'user_location') return

        // 跳过已检测过的点位
        if (this.checkedPoints.has(marker.id)) return

        const distance = this.calculateDistance(
          this.userLocation.latitude,
          this.userLocation.longitude,
          marker.latitude,
          marker.longitude
        )

        console.log(`距离巡检点 ${marker.title}: ${distance.toFixed(2)}米`)

        // 如果距离小于等于100米，调用接口
        if (distance <= 100) {
          this.handlePointArrival(marker)
        }
      })
    },

    // 计算两点间距离（米）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371000 // 地球半径（米）
      const dLat = this.toRadians(lat2 - lat1)
      const dLon = this.toRadians(lon2 - lon1)
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
      return R * c
    },

    // 角度转弧度
    toRadians(degrees) {
      return degrees * (Math.PI / 180)
    },

    // 处理巡检点就位
    async handlePointArrival(marker) {
      try {
        // 标记该点位已检测过，避免重复调用
        this.checkedPoints.add(marker.id)

        const params = {
          taskId: this.taskId,
          taskPointId: marker.id,
          latitude: this.userLocation.latitude,
          longitude: this.userLocation.longitude,
          arrivalTime: new Date().toISOString()
        }

        console.log('调用巡检点就位接口:', params)

        const result = await inspectionPointArrival(params)

        uni.showToast({
          title: `已到达${marker.title}`,
          icon: 'success',
          duration: 3000
        })

        // 更新标记点状态（可选）
        marker.status = 'arrived'
        marker.iconPath = '/static/icons/marker-arrived.png'

        console.log('巡检点就位成功:', result)

      } catch (error) {
        console.error('巡检点就位失败:', error)
        uni.showToast({
          title: '巡检点就位失败',
          icon: 'none'
        })
      }
    },

    // 开始巡检
    startInspection() {
      if (this.selectedMarker) {
        uni.navigateTo({
          url: `/pages/water/inspection-task/checkin?markerId=${this.selectedMarker.id}&taskId=${this.taskId}`
        })
      }
    },

    // 查看历史
    viewHistory() {
      if (this.selectedMarker) {
        uni.navigateTo({
          url: `/pages/water/inspection-task/history?markerId=${this.selectedMarker.id}`
        })
      }
    },

    // 获取状态样式类
    getStatusClass(status) {
      return {
        'status-normal': status === 'normal',
        'status-warning': status === 'warning',
        'status-error': status === 'error'
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'normal': '正常',
        'warning': '警告',
        'error': '异常'
      }
      return statusMap[status] || '未知'
    },

    // 获取任务状态样式类
    getTaskStatusClass(status) {
      return {
        'task-status-pending': status === 'pending',
        'task-status-in-progress': status === 'in_progress',
        'task-status-completed': status === 'completed'
      }
    },

    // 获取任务状态文本
    getTaskStatusText(status) {
      const statusMap = {
        'pending': '待开始',
        'in_progress': '进行中',
        'completed': '已完成'
      }
      return statusMap[status] || '未知'
    },

    // 修复：加载任务数据时转换坐标
    async loadTaskData() {
      try {
        // 使用正确的API方法：getInspectionTaskDetail
        const result = await getInspectionTaskDetail(this.taskId);

        if (result.code === 200 && result.data.taskPoints) {
          // 转换服务器返回的坐标数据
          this.markers = result.data.taskPoints.map(point => {
            let longitude = point.pointLon;
            let latitude = point.pointLat;

            // 如果服务器返回的是WGS84坐标，进行转换
            if (point.coordinateSystem === 'wgs84' || this.isWgs84Coordinate(longitude, latitude)) {
              const converted = wgs84ToGcj02(longitude, latitude);
              longitude = converted.lng;
              latitude = converted.lat;
            }
            console.log('转换后的坐标:', longitude, latitude);
            return {
              id: point.id,
              longitude: longitude,
              latitude: latitude,
              title: point.name,
              iconPath: '/static/icons/inspection-point.png',
              width: 30,
              height: 30,
              callout: {
                content: point.name,
                color: '#333',
                fontSize: 12,
                borderRadius: 4,
                bgColor: '#fff',
                padding: 5,
                display: 'BYCLICK'
              }
            };
          });

          // 更新任务信息
          if (result.data.taskInfo) {
            this.taskInfo = {
              id: result.data.taskInfo.id,
              name: result.data.taskInfo.name,
              status: result.data.taskInfo.status,
              completed: result.data.taskInfo.completed || 0,
              total: result.data.taskInfo.total || this.markers.length,
              progressPercent: result.data.taskInfo.progressPercent || 0
            };
          }

          this.updateMarkersWithUserLocation();
        }
      } catch (error) {
        console.error('加载任务数据失败:', error);
        uni.showToast({
          title: '加载任务数据失败',
          icon: 'none'
        });
      }
    },
    // 简单判断是否为WGS84坐标（可根据实际情况调整）
    isWgs84Coordinate(lng, lat) {
      // 这里可以根据坐标范围或其他标识来判断
      // 例如：如果坐标精度很高（小数点后6位以上），可能是GPS原始坐标
      return false; // 根据实际情况实现
    },

    // 获取位置并更新地图
    getLocationAndUpdate() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            timestamp: Date.now()
          }

          // 实时更新地图中心点为当前定位坐标
          this.mapCenter = {
            latitude: res.latitude,
            longitude: res.longitude
          }

          // 更新用户位置标记
          this.updateUserLocationMarker()

          // 检测与巡检点的距离
          this.checkDistanceToInspectionPoints()

          console.log('更新用户位置和地图中心:', this.userLocation)
        },
        fail: (err) => {
          console.error('获取位置失败:', err)
        }
      })
    },

    // 重试加载地图
    retryLoadMap() {
      this.mapError = false
      // 重新初始化地图
    },

    // 显示菜单
    showMenu() {
      uni.showActionSheet({
        itemList: ['任务详情', '导出报告', '分享任务'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.showTaskDetail()
              break
            case 1:
              this.exportReport()
              break
            case 2:
              this.shareTask()
              break
          }
        }
      })
    },

    // 显示任务详情
    showTaskDetail() {
      // TODO: 显示任务详情弹窗
    },

    // 导出报告
    exportReport() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    },

    // 分享任务
    shareTask() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      })
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

  .header {
    background: #1890ff;
    padding-top: var(--status-bar-height);

    .nav-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;

      .title {
        width: 100%;
        color: #fff;
        font-size: 18px;
        font-weight: 500;
        text-align: center;
      }
    }
  }

  .map-container {
    width: 100%;
    height: calc(100% - 48px);
    position: relative;

    .map {
      width: 100%;
      height: 100%;
      position: relative;
    }

    // 地图控制按钮
    .map-controls {
      position: absolute;
      right: 15px;
      bottom: 80px;
      background: #fff;
      border-radius: 30%;

      .location-btn {
        width: 29px;
        height: 29px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;

        &:active {
          transform: scale(0.95);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }

        .location-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

.info-panel {
  position: fixed;
  bottom: 120px;
  left: 16px;
  right: 16px;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .panel-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .panel-content {
    margin-bottom: 16px;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .info-label {
        font-size: 14px;
        color: #666;
        width: 80px;
      }

      .info-value {
        font-size: 14px;
        color: #333;
        flex: 1;

        &.status-normal {
          color: #52c41a;
        }

        &.status-warning {
          color: #faad14;
        }

        &.status-error {
          color: #f5222d;
        }
      }
    }
  }

  .panel-actions {
    display: flex;
    gap: 12px;

    .action-btn {
      flex: 1;
      padding: 10px;
      border-radius: 6px;
      font-size: 14px;
      border: none;

      &.primary {
        background-color: #FF8C42;
        color: #fff;
      }

      &.secondary {
        background-color: #f5f5f5;
        color: #333;
      }
    }
  }
}

.task-info-card {
  position: fixed;
  bottom: 16px;
  left: 16px;
  right: 16px;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .task-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      flex: 1;
    }

    .task-status {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 12px;

      &.task-status-pending {
        background-color: #f0f0f0;
        color: #666;
      }

      &.task-status-in-progress {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      &.task-status-completed {
        background-color: #f6ffed;
        color: #52c41a;
      }
    }
  }

  .card-content {
    .info-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .label {
        font-size: 14px;
        color: #666;
      }

      .value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }

    .progress-bar {
      height: 4px;
      background-color: #f0f0f0;
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FF8C42 0%, #FF6B1A 100%);
        border-radius: 2px;
        transition: width 0.3s;
      }
    }
  }
}
</style>
