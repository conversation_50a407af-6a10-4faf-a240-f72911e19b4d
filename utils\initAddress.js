import {
	city
} from "static/common/js/city.js"
import {
	cityCode
} from "static/common/js/cityCode.js"
export function initAddrUser(columns, columnData, userProvinceId, userCityId, userProvince, userCity) {
	// console.log("initAddress run")
	var data = city[86]
	console.log("userProvinceId:" + userProvinceId)
	for (var v in data) {
		if (v == userProvinceId) {
			userProvince = city["86"][v]
			console.log("pro:" + userProvince)

		}

		columns[0].push({
			id: v,
			label: city["86"][v]
		})
	}
	// console.log("columns:" +JSON.stringify(columns));
	// console.log("初始化市")
	var index = 0
	var provinceList = columns[0]

	provinceList.filter((v, i) => {
		var provId = v.id
		// console.log("provId" + provId)
		var citys = city[provId]
		// console.log("citys: " + JSON.stringify(citys))
		columnData[index] = []
		var y = 0
		for (var v2 in citys) { //城市数据
			if (provId == userProvinceId) {
				if (v2 == userCityId) {
					userCity = city[provId][v2]
					console.log("userCity:" + userCity)
				}

			}
			// console.log("index:" + index + "provId:" + provId + "cityId:" +
			// 	v2 + ",cityName:" + city[provId][v2]);
			columnData[index].push({
				id: v2,
				label: city[provId][v2]
			})
		}
		// console.log("columnData:" + JSON.stringify(this.columnData[index]));
		y = 0
		index++
	})
	console.log("init:" + userProvince + "-" + userCity)
	return userProvince + "-" + userCity

}
//省市
export function initAddr(columns, columnData, chosenProvince, chosenCity) {
	// console.log("initAddress run")
	var data = city[86]
	for (var v in data) {

		columns[0].push({
			id: v,
			label: city["86"][v]
		})
	}
	// console.log("columns:" +JSON.stringify(columns));
	// console.log("初始化市")
	var index = 0
	var provinceList = columns[0]

	provinceList.filter((v, i) => {
		var provId = v.id
		// console.log("provId" + provId)
		var citys = city[provId]
		// console.log("citys: " + JSON.stringify(citys))
		columnData[index] = []
		var y = 0
		for (var v2 in citys) { //城市数据

			// console.log("index:" + index + "provId:" + provId + "cityId:" +
			// 	v2 + ",cityName:" + city[provId][v2]);
			columnData[index].push({
				id: v2,
				label: city[provId][v2]
			})
		}
		// console.log("columnData:" + JSON.stringify(this.columnData[index]));
		y = 0
		index++
	})

	// return chosenProvince + "-" + chosenCity

}
//省市区
export function initAddrPCD(columns, columnData, columnData3) {
	console.log("initAddrPCD run")
	var data = cityCode
	//初始化省份
	// dealArray(columns,data);
	// console.log("columns:" + JSON.stringify(columns));

	data.filter((v, i) => {
		//初始化省份
		// console.log("v:" + JSON.stringify(v));
		// console.log("i:" + i);
		columns[0].push({
			id: v.value,
			label: v.label
		})

		//初始化市
		columnData[i] = [];
		columnData3[i] = [];
		var city = v.children;
		// console.log("初始化市");
		city.filter((v1, i1) => {
			// console.log("v1:" + JSON.stringify(v1));
			// console.log("i1:" + i1);
			columnData[i].push({
				id: v1.value,
				label: v1.label
			})
			columnData3[i][i1] = [];
			var dist = v1.children;
			// console.log("初始化区");
			 // console.log("dist:" + JSON.stringify(dist));
			dist.filter((v2, i2) => {
				// console.log("v2:" + JSON.stringify(v2));
				// console.log("i2:" + i2);
				columnData3[i][i1].push({
					id: v2.value,
					label: v2.label
				})
			})

		})
	})
	console.log("columns:" + columns.length);	
	// console.log("columns:" + JSON.stringify(columns));
	// console.log("columnData:" + JSON.stringify(columnData));
	// console.log("columnData3:" + JSON.stringify(columnData3));

	// return chosenProvince + "-" + chosenCity + "-" + chosenDis

}

function dealArray(desArray, oriArray) {
	oriArray.filter((v, i) => {
		desArray.push({
			id: v.value,
			label: v.label
		})
	})
}
