# 地图配置说明

## 问题描述
当前项目使用uni-app原生map组件，但缺少必要的地图服务API密钥配置，导致出现"Map key not configured"错误。

## 解决方案

### 1. 获取高德地图API密钥

1. 访问高德地图开放平台：https://lbs.amap.com/
2. 注册并登录账号
3. 进入控制台，创建应用
4. 分别创建iOS和Android平台的应用，获取对应的AppKey

### 2. 配置API密钥

在 `manifest.json` 文件中，已经添加了地图配置模板：

```json
"sdkConfigs" : {
    "ad" : {},
    "maps" : {
        "amap" : {
            "appkey_ios" : "请在高德地图开放平台申请iOS应用的AppKey",
            "appkey_android" : "请在高德地图开放平台申请Android应用的AppKey"
        }
    }
}
```

请将获取到的实际AppKey替换上述占位符文本。

### 3. 配置步骤详解

#### iOS平台配置
1. 在高德开放平台创建iOS应用
2. Bundle ID设置为：`com.potevio.shanxi.water`
3. 获取iOS AppKey
4. 替换manifest.json中的`appkey_ios`值

#### Android平台配置
1. 在高德开放平台创建Android应用
2. 包名设置为：`com.potevio.shanxi.water`
3. 获取SHA1安全码（从开发证书中提取）
4. 获取Android AppKey
5. 替换manifest.json中的`appkey_android`值

### 4. 其他地图服务商选择

如果需要使用其他地图服务商，可以配置：

#### 腾讯地图
```json
"maps" : {
    "qqmap" : {
        "key" : "腾讯地图AppKey"
    }
}
```

#### 百度地图
```json
"maps" : {
    "baidumap" : {
        "appkey_ios" : "百度地图iOS AppKey",
        "appkey_android" : "百度地图Android AppKey"
    }
}
```

### 5. 注意事项

1. **重新编译**：修改manifest.json后需要重新编译项目
2. **平台限制**：不同平台需要不同的AppKey
3. **域名白名单**：H5平台需要在地图服务商后台配置域名白名单
4. **配额限制**：注意API调用次数限制，生产环境建议购买商业版

### 6. H5平台特殊配置

如果需要在H5平台使用地图，需要在页面中引入地图JS SDK：

```html
<!-- 高德地图 -->
<script src="https://webapi.amap.com/maps?v=1.4.15&key=您的高德地图WebKey"></script>

<!-- 腾讯地图 -->
<script src="https://map.qq.com/api/gljs?v=1.0.0&key=您的腾讯地图WebKey"></script>
```

### 7. 测试验证

配置完成后，重新编译并运行项目，地图应该能够正常显示。如果仍有问题，请检查：

1. AppKey是否正确
2. 包名/Bundle ID是否匹配
3. 网络连接是否正常
4. 控制台是否有其他错误信息

## 当前项目状态

- ✅ 已添加地图配置模板到manifest.json
- ❌ 需要申请并配置实际的API密钥
- ✅ 地图组件代码已实现完成

请按照上述步骤获取并配置API密钥，即可解决"Map key not configured"问题。