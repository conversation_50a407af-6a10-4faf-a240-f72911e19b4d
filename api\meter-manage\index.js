// import request from './request.js';

// // export const login = (params) => request.post('/wx/getWxVolumesList', params, { custom: { auth: false } })

// // 抄表册本列表
// export const getWxVolumesList = (params) => request.get('/charge/wx/getWxVolumesList', params)

// // 抄表册本下载
// export const downloadVolumes = (params) => request.get('/charge/wx/downloadVolumes/'+ params)

// // 抄表册本提交
// export const commitVolumes = (params) => request.get('/charge/wx/commitVolumes/'+ params)


// // 抄表册本状态统计
// export const getVolumesStat = (params) => request.get('/charge/wx/getVolumesStat', params)


import {request} from "./http/request.js"

// 登录
export function login(data) {
    return request({
        url: `/auth/login`,
        method: 'post',
        data
    })
}

// // 退出登录
// export function logout() {
//   return request({
//     url: '/logout',
//     method: 'post'
//   })
// }

// // 获取用户详细信息
// export function getInfo() {
//   return request({
//     url: '/getInfo'
//   })
// }

// 抄表册本状态统计
export function getVolumesStat(data) {
    return request({
        url: `/revenue/app/getVolumesStat`,
        method: 'get',
        data
    })
}

// 抄表册本列表
export function getWxVolumesList(data) {
    return request({
        url: `/revenue/app/getWxVolumesList`,
        method: 'get',
        data
    })
}

// 抄表册本下载
export function downloadVolumes(data) {
    return request({
        url: `/revenue/app/downloadVolumes/${data}`,
        method: 'get'
    })
}

// 抄表册本提交
export function commitVolumes(data) {
    return request({
        url: `/revenue/app/commitVolumes/${data}`,
        method: 'get'
    })
}


export function getVolumesReadingStat(volumeSchedulingId) {
    return request({
        url: `/revenue/app/getVolumesReadingStat/${volumeSchedulingId}`,
        method: 'get'
    })
}


export function getWxVolumesReadingList(data) {
    return request({
        url: `/revenue/app/getWxVolumesReadingList`,
        method: 'get',
        data
    })
}


export function getVolumeCardList(data) {
    return request({
        url: `/revenue/app/getVolumeCardList/${data}`,
        method: 'get'
    })
}

export function getVolumesHistoryList(data) {
    return request({
        url: `/revenue/app/getVolumesHistoryList`,
        method: 'get',
        data
    })
}


export function getMeterReadingList(data) {
    return request({
        url: `/revenue/app/getMeterReadingList`,
        method: 'get',
        data
    })
}



