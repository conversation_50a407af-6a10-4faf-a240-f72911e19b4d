<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成导航图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 40px;
            margin: 20px 0;
            justify-content: center;
        }
        .icon-item {
            text-align: center;
        }
        .icon-item h3 {
            margin-bottom: 10px;
            color: #333;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .download-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #40a9ff;
        }
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>导航图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <p>1. 点击下方的"下载PNG"按钮下载图标</p>
            <p>2. 将下载的图标文件重命名并放置到对应路径：</p>
            <ul>
                <li><strong>user-location.png</strong> → static/icons/user-location.png</li>
                <li><strong>meter-location.png</strong> → static/icons/meter-location.png</li>
            </ul>
        </div>

        <div class="icon-preview">
            <div class="icon-item">
                <h3>抄表员位置图标</h3>
                <canvas id="userIcon" width="36" height="36"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon('userIcon', 'user-location.png')">下载PNG</button>
            </div>
            
            <div class="icon-item">
                <h3>水表位置图标</h3>
                <canvas id="meterIcon" width="36" height="36"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon('meterIcon', 'meter-location.png')">下载PNG</button>
            </div>
        </div>
    </div>

    <script>
        // 绘制抄表员位置图标
        function drawUserIcon() {
            const canvas = document.getElementById('userIcon');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 36, 36);
            
            // 外圈阴影
            ctx.beginPath();
            ctx.arc(18, 18, 17, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(24, 144, 255, 0.15)';
            ctx.fill();
            
            // 主体圆圈
            ctx.beginPath();
            ctx.arc(18, 18, 14, 0, 2 * Math.PI);
            ctx.fillStyle = '#1890ff';
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 用户图标 - 头部
            ctx.beginPath();
            ctx.arc(18, 14, 3.5, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            
            // 用户图标 - 身体
            ctx.beginPath();
            ctx.moveTo(13, 21);
            ctx.quadraticCurveTo(13, 17, 18, 15.5);
            ctx.quadraticCurveTo(23, 17, 23, 21);
            ctx.lineTo(23, 25);
            ctx.lineTo(13, 25);
            ctx.closePath();
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            
            // 内部光晕
            ctx.beginPath();
            ctx.arc(18, 18, 10, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.fill();
            
            // 中心点
            ctx.beginPath();
            ctx.arc(18, 18, 2, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
        }
        
        // 绘制水表位置图标
        function drawMeterIcon() {
            const canvas = document.getElementById('meterIcon');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 36, 36);
            
            // 外圈阴影
            ctx.beginPath();
            ctx.arc(18, 18, 17, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(82, 196, 26, 0.15)';
            ctx.fill();
            
            // 主体圆圈
            ctx.beginPath();
            ctx.arc(18, 18, 14, 0, 2 * Math.PI);
            ctx.fillStyle = '#52c41a';
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 水表外框
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(12, 12, 12, 12);
            ctx.strokeStyle = '#52c41a';
            ctx.lineWidth = 1;
            ctx.strokeRect(12, 12, 12, 12);
            
            // 水表表盘
            ctx.beginPath();
            ctx.arc(18, 18, 4, 0, 2 * Math.PI);
            ctx.fillStyle = '#52c41a';
            ctx.fill();
            
            // 表盘刻度
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 0.8;
            const angles = [0, 45, 90, 135, 180, 225, 270, 315];
            angles.forEach(angle => {
                const rad = (angle * Math.PI) / 180;
                const x1 = 18 + Math.cos(rad) * 3;
                const y1 = 18 + Math.sin(rad) * 3;
                const x2 = 18 + Math.cos(rad) * 2.5;
                const y2 = 18 + Math.sin(rad) * 2.5;
                
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            });
            
            // 指针
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(18, 18);
            ctx.lineTo(18, 16);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(18, 18);
            ctx.lineTo(19.5, 19);
            ctx.stroke();
            
            // 指针中心
            ctx.beginPath();
            ctx.arc(18, 18, 0.5, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            
            // 水滴装饰
            ctx.beginPath();
            ctx.moveTo(14, 14);
            ctx.quadraticCurveTo(14, 13, 15.5, 13);
            ctx.quadraticCurveTo(16, 13.5, 16, 15);
            ctx.quadraticCurveTo(15.5, 15.5, 14, 14);
            ctx.fillStyle = '#1890ff';
            ctx.globalAlpha = 0.8;
            ctx.fill();
            ctx.globalAlpha = 1;
            
            // 内部光晕
            ctx.beginPath();
            ctx.arc(18, 18, 10, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.fill();
        }
        
        // 下载图标
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 初始化绘制图标
        window.onload = function() {
            drawUserIcon();
            drawMeterIcon();
        };
    </script>
</body>
</html>
