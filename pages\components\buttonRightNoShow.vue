<template>
	<view>
		<u-picker :show="show" ref="uPicker" :columns="choices" @confirm="confirmChoice" @change="changeHandlerMeasure"
			@cancel="close"></u-picker>

		<u-cell :isLink="true" @click="show=true" :value="valueHint">
			<view slot="title" class="u-slot-title">
				<text class="u-cell-text">{{title}}</text>
			</view>
		</u-cell>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => "请选择"
			},
			valueHint: {
				type: String,
				default: () => "请选择"
			},
			show: {
				type: Boolean,
				default () {
					false
				}
			},

			choices: {
				type: Array,
				default () {
					return []
				}
			}
		},


		methods: {
			confirmChoice(e) {
				console.log("confirmChoice" + e.value[0] + ",index:" + e.indexs[0]);
				this.value = e.value[0];
				this.$emit('update:index', e.indexs[0])
				this.$emit('update:msg', e.value[0])

				this.show = false;
			},
			changeHandlerMeasure(e) {
				console.log("changeHandlerMeasure");
				const {
					columnIndex,
					value,
					values,
					index,
					picker = this.$refs.uPicker
				} = e
				if (columnIndex === 0) {
					picker.setColumnValues(1, this.columnData[index]);

				}
			},
			close() {
				this.show = false
			},
		}
	}
</script>

<style lang="scss" scoped>


</style>