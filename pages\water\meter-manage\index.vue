<!-- 抄表管理 -->
<template>
  <view class="index-content">
    <!-- 		<Navbar contentColor="#fff" :hideBtn="true" title="首页" bgColor="#4582FD" :h5Show="false" :fixed="false">
        </Navbar> -->
    <view class="top-bg">
      <image class="bg-img" mode="widthFix" src="/static/img/top-bg.png"></image>
      <!-- <view class="tips-box" @click="getMore()">
        <u-notice-bar mode="link" :text="noticeList"></u-notice-bar>
      </view> -->
      <div class="ceben-box">
        <image class="cb-img" src="/static/img/cb-bg.png"></image>
        <view class="tongji">
          <span>册本总数：{{ info.volumesCount ? info.volumesCount : 0 }}</span>
          <span>已抄：{{ info.havingReadingCount ? info.havingReadingCount : 0 }}
						未抄：{{ info.noReadingCount ? info.noReadingCount : 0 }}</span>
        </view>
        <view class="swiper-box">
          <!-- :autoplay="true" -->
          <view class="swiper-container">
            <swiper v-if="swiperList.length > 0" :current="currentIndex" circular class="swiper" interval="9000"
                    @change="handleSwiperChange">
              <block v-for="(item, index) in swiperList" :key="index">
                <swiper-item v-if="item">
                  <!-- 轮播项的内容 -->
                  <!-- @click="item.schedulingStatus!=0?navTo('/packageA/pages/meter-reading/area-list?item=',item):''" -->
                  <view class="class-item" @click="goNav(item)">

                    <view v-if="item.schedulingStatus == 1" class="score-view">
                      <label for="">{{ item.volumeNameTime }}</label>
                      <image class="img-b" mode="widthFix" src="/static/img/nodata.png" style="width: 392rpx;"></image>
                      <text style="height: 220rpx; display: flex;align-items: flex-end;">暂无数据，请先下载</text>
                      <view class="down" @click="down(item.schedulingId)">
                        <image mode="widthFix" src="/static/img/down.png" style="width: 42rpx;"></image>
                        下载
                      </view>
                    </view>
                    <!--                    <view v-if="item.schedulingStatus==4" class="score-view">-->
                    <!--                      <span style="color: #ff4d4f;font-size: 32rpx;">去复查</span>-->
                    <!--                    </view>-->
                    <view v-if="item.schedulingStatus!=1" class="score-view">
                      <label :style="item.schedulingStatus==4?'color:#ff4d4f':''" for="">{{ item.volumeName }}</label>
                      <span>{{ item.havingReadingCount + item.noReadingCount }}</span>
                      <text>已抄: {{ item.havingReadingCount }} 未抄:{{ item.noReadingCount }}</text>
                      <text style="margin-top: 20rpx;">
                        {{ item.schedulingStatus == '2' ? '待提交' : '已提交' }}
                      </text>
                    </view>
                    <!-- <ProgressCircle @down="getList()" :progress="item.havingReadingCount/(item.havingReadingCount+item.noReadingCount)"  :item="item" :key="index"></ProgressCircle> -->
                  </view>
                </swiper-item>
              </block>

            </swiper>
            <view v-if="swiperList.length === 0" class="empty">
              <u-empty icon="http://cdn.uviewui.com/uview/empty/list.png" mode="list" text="暂无数据">
              </u-empty>
            </view>


            <view v-if="swiperList.length" class="arrow arrow-left" @tap="prev">
              <image mode="widthFix" src="/static/img/left.png"></image>
            </view>
            <view v-if="swiperList.length" class="arrow arrow-right" @tap="next">
              <image mode="widthFix" src="/static/img/right.png"></image>
            </view>
            <view v-if="swiperList.length" class="num">
              {{ (currentIndex + 1) + "/" + swiperList.length }}
            </view>
          </view>
        </view>
      </div>
    </view>
    <view class="home-btn-box">
      <view class="home-btn-list" @click="navTo('/pages/water/meter-manage/detail?item=',swiperList[currentIndex])">
        <image mode="widthFix" src="/static/img/c1.png"></image>
        <span>册本详情</span>
      </view>
      <view class="home-btn-list"
            @click="navTo('/pages/water/meter-manage/history-list?item=',swiperList[currentIndex])">
        <image mode="widthFix" src="/static/img/c2.png"></image>
        <span>历史记录</span>
      </view>
      <!-- <view class="home-btn-list home-btn-list1" @click="navTo('/pages/water/meter-manage/event-list?item=',swiperList[currentIndex])">
        <image src="/static/img/c3.png" mode="widthFix"></image>
        <span>事件记录</span>
      </view>
      <view class="home-btn-list home-btn-list1" @click="navTo('/pages/water/meter-manage/abnormal-statistics?item=',swiperList[currentIndex])">
        <image src="/static/img/c4.png" mode="widthFix"></image>
        <span>异常统计</span>
      </view> -->
    </view>
    <view>

    </view>
    <view
        v-if="swiperList.length > 0"
        :class="swiperList[currentIndex].schedulingStatus==2?'btn-box':'btn-box btn-box-disable'"
        @click="swiperList[currentIndex].schedulingStatus==2?commitData():''">
      {{ swiperList[currentIndex].schedulingStatus == 2 ? '数据上传' : '数据已上传' }}
    </view>
    <!--
      mode 选择器类型
     -->
    <!--
      @change="change"
      type 弹框形式
     -->
    <uni-popup ref="popup1" background-color="#FFFFFF" type="bottom">
      <view>
        <view class="operation">
          <view @click="$refs.popup1.close()">取消</view>
          <view @click="confirm">确定</view>
        </view>
        <!-- v-if="visible" -->
        <picker-view :value="format" class="picker-view" indicator-style="height: 50px;" @change="bindChange">
          <picker-view-column>
            <view v-for="(item,index) in years" :key="index" class="item">{{ item }}年</view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="(item,index) in months" :key="index" class="item">{{ item }}月</view>
          </picker-view-column>
          <!-- <picker-view-column>
            <view class="item" v-for="(item,index) in days" :key="index">{{item}}日</view>
          </picker-view-column> -->
        </picker-view>
      </view>

    </uni-popup>
  </view>
</template>

<script>
// import Navbar from './navbar/Navbar.vue'
import ProgressCircle from './progress-circle/ProgressCircle.vue'
import {getData, setData} from "@/api/meter-manage/http/auth.js";
import {commitVolumes, downloadVolumes, getVolumesStat, getWxVolumesList, login} from '@/api/meter-manage/index.js'
// import * as IndexApi from '@/api/meter-manage/index.js'
// import * as NoticeApi from '@/api/meter-manage/notice.js'
export default {
  components: {
    // Navbar,
    ProgressCircle
  },
  data() {
    const date = new Date()
    const years = []
    const year = date.getFullYear()
    for (let i = 1990; i <= date.getFullYear(); i++) {
      years.push(i)
    }

    const months = []
    const month = date.getMonth() + 1
    for (let i = 1; i <= 12; i++) {
      months.push(i)
    }
    return {
      info: {
        volumesCount: 0,
        havingReadingCount: 0,
        noReadingCount: 0,
        // abnormalCount: 0,
        // eventCount: 0,
        // noticeTitle: ''
      },
      currentIndex: 0,
      swiperList: [],
      currentProgress: '80%',
      noticeList: '', //通知


      queryParams: {
        // periodNum: undefined
      },

      timeYue: "",
      // visible: false,
      format: [9999, month - 1],
      years, //年
      year, //当前年
      months, //月
      month, //当前月
    }
  },
  onLoad() {
    this.getList()
    getVolumesStat().then(res => {
      console.log("抄表册本状态统计", res)
      this.info = res.data
      // if(res.data){
      // 	this.noticeList=res.data[0].noticeTitle
      // }else{
      // 	this.noticeList='暂无通知'
      // }

    })
    // 没有token才请求
    if (!getData("zhyx_token")) {
      this.queryLoginInfo()
    }
  },
  onShow() {
    this.getList()
    // this.getNoticeList()
    /*
      {
        dutyUser: this.$storage.get('user').userId
      }
    */
    getVolumesStat().then(res => {
      console.log("抄表册本状态统计", res)
      this.info = res.data
    })
  },
  onNavigationBarButtonTap() {
    // console.log(111);
    // console.log(this.$refs.times);
    // this.$refs.times.addEventListener("click",() => {

    // });
    this.$refs.popup1.open()
    // this.visible = true
  },
  methods: {
    // 获取登录token
    async queryLoginInfo() {
      let {data} = await login({username: "admin", password: "Sxzgzhsw@123"});
      console.log("获取登录token", data);
      setData("zhyx_token", data.access_token);
    },
    down(num) {
      console.log(num)
      const app = this
      downloadVolumes(num).then(res => {
        console.log(res)
        // this.$emit("down")
        this.getList()
      })
    },
    // 获取公告
    getNoticeList() {
      const app = this
      NoticeApi.noticeList().then(res => {
        if (res.rows) {
          this.noticeList = res.rows[0].noticeTitle
        } else {
          this.noticeList = '暂无通知'
        }
      })
    },
    // 跳转公告列表
    // getMore() {
    // 	console.log(11111111)
    // 	uni.switchTab({
    // 		url: '/pages/work/notice/list'
    // 	})
    // },
    handleSwiperChange(event) {
      const current = event.detail.current;
      this.currentIndex = current;
      console.log("当前轮播到第", current, "个索引");
    },
    prev() {
      this.currentIndex = (this.currentIndex - 1 + this.swiperList.length) % this.swiperList.length;
    },
    next() {
      this.currentIndex = (this.currentIndex + 1) % this.swiperList.length;
    },
    navTo(url, item) {
      console.log(11)
      const queryString = encodeURIComponent(JSON.stringify(item));
      uni.navigateTo({
        url: url + queryString
      })
    },
    goNav(item) {
      // debugger
      console.log(item);
      const queryString = encodeURIComponent(JSON.stringify(item));

      // if (item.schedulingStatus == 1) {
      // 	uni.navigateTo({
      // 		url: '/packageA/pages/meter-reading/area-list?item=' + queryString
      // 	})
      // }
      if (item.schedulingStatus == 2) {
        uni.navigateTo({
          url: '/pages/water/meter-manage/level-list?item=' + queryString
        })
      }

      // if (item.schedulingStatus == 4) {
      // 	uni.navigateTo({
      // 		url: '/packageA/pages/meter-reading/review?item=' + queryString
      // 	})
      // }


    },
    // 获取册本列表
    getList() {
      // const app = this
      /*
        {
          dutyUser: app.$storage.get('user').userId
        }
      */
      getWxVolumesList(this.queryParams).then(res => {
        console.log("抄表册本列表", res)
        if (res.rows.length > 0) {
          this.swiperList = res.rows
        } else {
          this.swiperList = []
        }

      })
    },
    // 数据提交
    commitData() {
      const app = this
      let num = this.swiperList[this.currentIndex].schedulingId
      commitVolumes(num).then(res => {
        console.log(res)
        if (res.code == 500) {
          uni.$u.toast('请输入本期用水')
        }
        // app.swiperList = res.rows
        this.getList()
      })
    },

    // 日期弹框，每次选择都会触发，用于实时触发
    bindChange(e) {
      const val = e.detail.value
      this.year = this.years[val[0]]
      this.month = this.months[val[1]]
      // console.log(this.year,"-",this.month);
    },
    // 日期弹框，确认
    confirm() {
      this.$refs.popup1.close()
      // console.log(`${this.year}-${this.month}`);
      this.queryParams.periodNum = `${this.year}-${this.replenish(this.month)}`
      this.getList();
    },
    replenish(num) {
      return num > 9 ? num : `0${num}`
    }
  }
}
</script>

<style lang="scss">
.tips-box {
  position: relative;
  z-index: 2;

  .u-notice-bar {
    background-color: rgba(255, 255, 255, .25) !important;
    border-radius: 12rpx;

    .u-notice__content__text {
      color: #fff !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.index-content {
  background-color: RGBA(246, 245, 243, 1);
  min-height: 100vh;

  .top-bg {
    position: relative;
    z-index: 3;
    width: 100%;
    height: 512rpx;

    padding: 26rpx 38rpx 0;
    box-sizing: border-box;

    .bg-img {
      position: absolute;
      z-index: 1;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }

    .ceben-box {
      position: absolute;
      z-index: 2;
      left: 38rpx;
      // top: 126rpx;
      top: 20rpx;
      width: 674rpx;
      height: 610rpx;
      padding-top: 76rpx;

      .cb-img {
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
      }

      .tongji {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40rpx;
        font-size: 30rpx;
        font-weight: 400;
        color: #949494;
      }

      .swiper-box {
        position: relative;
        z-index: 2;
        height: 520rpx;
        margin: 0 40rpx;

        .swiper {
          height: 500rpx !important;

          .class-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            position: relative;
            height: 100%;
            width: 90%;
            margin: 0 auto;

            // background-color: #0C71FF;
            .score-view {
              font-weight: 500;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;

              label {
                position: relative;
                z-index: 2;
                // font-size: 48rpx;
                font-size: 38rpx;
                font-weight: 500;
                color: #000000;
                line-height: 1.2;
              }

              span {
                margin-top: 28rpx;
                font-size: 60rpx;
                font-weight: 500;
                color: #0C71FF;
                line-height: 1.2;
                margin-bottom: 20rpx;
              }

              text {
                position: relative;
                z-index: 2;
                font-size: 32rpx;
                font-weight: 500;
                color: #A0A1A3;

              }

              .img-b {
                position: absolute;
                z-index: 1;
              }

              .down {
                position: relative;
                z-index: 2;
                font-size: 32rpx;
                font-weight: 500;
                color: #0A90FE;
                margin-top: 20rpx;
              }

            }


          }
        }

        .empty {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          margin: auto;
          padding-top: 80rpx;

        }

        .arrow {
          position: absolute;
          top: 50%;
          transform: translateY(-48%);

          image {
            width: 48rpx;
          }

        }

        .arrow-left {
          left: -20rpx;
        }

        .arrow-right {
          right: -20rpx;
        }

        .num {
          position: absolute;
          z-index: 2;

          left: 50%;
          transform: translateX(-50%);
          font-size: 32rpx;
          font-weight: 500;
          color: #949494;

        }

      }

    }

  }

  .home-btn-box {
    // margin-top: 300rpx;
    margin-top: 200rpx;
    padding: 40rpx;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    box-sizing: border-box;

    .home-btn-list {
      width: 316rpx;
      height: 100rpx;
      background: #0C71FF;
      border-radius: 20rpx;
      // margin-bottom: 30rpx;
      display: flex;
      align-items: center;
      padding-left: 30rpx;
      box-sizing: border-box;

      image {
        width: 56rpx;
        margin-right: 30rpx;
      }

      span {
        font-size: 36rpx;
        font-weight: 400;
        color: #FFFFFF;
      }
    }

    .home-btn-list1 {
      background: #DEE3E7;

      span {
        color: #000000;
      }
    }
  }
}

@media only screen and (max-height: 768px) {
  .index-content .top-bg .ceben-box .swiper-box .swiper {
    height: 520rpx !important;
  }
}

.btn-box {
  margin: 0 auto;
  width: 672rpx;
  height: 100rpx;
  background: #0C71FF;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: 400;
  color: #FFFFFF;
}

.btn-box-disable {
  background: #efefef;
  color: #666;
}

.operation {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  border-bottom: 1px solid #F5F4F4;
  font-size: 25rpx;

  > view:first-child {
    color: #F91510;
  }

  > view:last-child {
    color: #3C69FE;
  }
}

.picker-view {
  width: 750rpx;
  height: 600rpx;
  margin-top: 20rpx;
}

.item {
  line-height: 100rpx;
  text-align: center;
  font-size: 25rpx;
}
</style>
