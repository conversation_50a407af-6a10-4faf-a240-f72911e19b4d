const isDev = process.env.NODE_ENV === 'development'; // 判断当前开发环境


// 开发环境可以用devserver进行proxy代理，线上环境用真实接口地址
// 注意：proxy devserver跨域只适用于h5,其他环境需完整路径才能正常发起请求。
// export const proxy = isDev ? 'http://in.dajtech.net' : 'http://in.dajtech.net'
//{"username":"xj","password":"6cd5bf03fbbe968f409f6bc9a36a34a7"}
// xj  gci@000000
// tzx  gci@000000
//repair_r_t_a gci@000000
// export const proxy = isDev ? 'http://10.1.74.53:8070' : 'http://10.1.74.53:8070'


// 测试环境 新的
export const proxy = isDev ? 'http://59.49.48.115:19713' :'http://59.49.48.115:19713'
export const proxySys = isDev ?  'http://59.49.48.115:19713' :'http://59.49.48.115:19713'
export const proxyDevice = isDev ?  'http://59.49.48.115:19713' :'http://59.49.48.115:19713'
export const proxyJbpm = isDev ? 'http://59.49.48.115:19713' :'http://59.49.48.115:19713'


// export const proxy = isDev ? 'http://10.1.74.53:18888' : 'http://10.1.74.53:18888'
// export const proxySys = isDev ? 'http://10.1.74.53:18888' : 'http://10.1.74.53:18888'
// export const proxyDevice = isDev ? 'http://10.1.74.53:18888' : 'http://10.1.74.53:18888'
// export const proxyJbpm = isDev ? 'http://10.1.74.53:18888' : 'http://10.1.74.53:18888'

// export const proxySys = isDev ? 'http://10.1.74.53:18888' : 'http://10.1.74.53:18888'
// export const proxyDevice = isDev ? 'http://10.1.74.106:8010' : 'http://10.1.74.106:8010'

// 环境：生产
// export const proxy = isDev ? 'http://210.10.2.65:18888' : 'http://210.10.2.65:18888'
// export const proxySys = isDev ? 'http://210.10.2.65:18888' : 'http://210.10.2.65:18888'
// export const proxyDevice = isDev ? 'http://210.10.2.65:18888' : 'http://210.10.2.65:18888'
// export const proxyJbpm = isDev ? 'http://210.10.2.65:18888' : 'http://210.10.2.65:18888'


// http://10.1.74.53:18888/auth/login
// 登录可选用：
// 10.1.74.53:18888/auth/login
// 登出可选用
// 10.1.74.53:18888/auth/logout
// 设备&字典&用户
// 10.1.74.53:18888/potevio-device/对应接口名
// 维修&巡检&保养
// 10.1.74.53:18888/potevio-mro/对应接口名'