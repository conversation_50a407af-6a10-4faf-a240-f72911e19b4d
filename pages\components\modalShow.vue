<template>
	<view>
		<u-modal :title="title" :show="show" @confirm="() =>{
				confirm()
				}" :show-cancel-button="true" @cancel="cancelHandler">
			<view class="slot-content" v-if="showInput">
				<view class="inputLine">
					<view class="title">{{remarkTitle}}
						<font color="#ff0000">*</font>
					</view>
					<u--input clearable class="inputInfo" v-model="value" type="number" placeholder="请输入"></u--input>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			remarkTitle: {     
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => ""
			},
			show: {
				type: Boolean,
				default () {
					false
				}
			},
			showInput: {
				type: Boolean,
				default () {
					true
				}
			}
		},


		methods: {
			cancelHandler() {
				console.log("cancel ");
				this.$emit('cancel')
			},
			confirm() {
				console.log("confirm modalshow");
				this.$emit('update:msg', this.value + "")

			},


		}
	}
</script>

<style lang="scss" scoped>


</style>