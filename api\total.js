import { request } from './server.js'

const serverUrl = "/extension";

//获取统计值
export const getStatisticaValue =
	function(params) {
		return request.post(`${serverUrl}/analysis/getStatisticaValue`, params)
			.then(data => {
				return data.data
			})
	}

//获取曲线数据集
export const getChartList = function(params) {
	return request.post(`${serverUrl}/analysis/getChartList`, params)
		.then(data => {
			return data.data
		})
}

//获取实时水质
export const getRealList = function(params) {
	return request.post(`${serverUrl}/analysis/getRealList`, params)
		.then(data => {
			return data.data
		})
}

//获取设备运行情况
export const getHisList = function(params) {
	return request.post(`${serverUrl}/analysis/getHisList`, params)
		.then(data => {
			return data.data
		})
}