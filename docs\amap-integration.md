# 高德地图API集成说明

## 概述

本项目已集成高德地图Web服务API，用于实现步行路线规划功能。当用户在抄表导航中规划路线时，系统会调用高德地图API获取详细的步行路线，提供更准确的导航体验。

## 配置步骤

### 1. 申请高德地图API密钥

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册并登录账号
3. 进入控制台，创建新应用
4. 申请 **Web服务API** 密钥
5. 确保开通以下服务：
   - 路径规划
   - 步行路径规划

### 2. 配置API密钥

编辑 `config/amap.js` 文件，将申请到的密钥填入：

```javascript
export default {
  // 替换为你的高德地图Web服务API密钥
  webServiceKey: 'YOUR_ACTUAL_AMAP_WEB_SERVICE_KEY',
  // ... 其他配置
}
```

### 3. 功能特性

#### 步行路线规划
- 自动调用高德地图步行路线规划API
- 返回详细的路线坐标点
- 显示准确的步行距离和预计时间
- 在地图上绘制完整的步行路线

#### 备用方案
- 当高德API调用失败时，自动降级为直线路线
- 确保功能的可用性和稳定性

#### 样式配置
- 可配置的路线样式（颜色、宽度、边框等）
- 区分不同类型路线的视觉效果

## API使用说明

### 主要方法

#### `getWalkingRoute(startPoint, endPoint)`
调用高德地图步行路线规划API

**参数：**
- `startPoint`: 起点坐标 `{longitude: number, latitude: number}`
- `endPoint`: 终点坐标 `{longitude: number, latitude: number}`

**返回值：**
```javascript
{
  success: boolean,
  polyline: Array,     // 地图路线数据
  distance: string,    // 格式化的距离字符串
  duration: string,    // 预计时间
  rawData: Object      // 高德API原始返回数据
}
```

#### `parseAmapPolyline(steps)`
解析高德地图返回的路线坐标数据

**参数：**
- `steps`: 高德API返回的路线步骤数组

**返回值：**
- 坐标点数组，格式：`[{longitude: number, latitude: number}, ...]`

### 配置选项

#### API配置
```javascript
apiConfig: {
  walkingRoute: {
    baseUrl: 'https://restapi.amap.com/v3/direction/walking',
    timeout: 10000,
    extensions: 'all'
  }
}
```

#### 样式配置
```javascript
mapStyle: {
  polyline: {
    walking: {
      color: '#1890ff',      // 路线颜色
      width: 6,              // 路线宽度
      borderColor: '#ffffff', // 边框颜色
      borderWidth: 2,        // 边框宽度
      arrowLine: true,       // 显示箭头
      dottedLine: false      // 是否虚线
    }
  }
}
```

## 错误处理

系统包含完善的错误处理机制：

1. **API密钥检查**：启动时检查密钥是否配置
2. **网络超时**：设置合理的请求超时时间
3. **数据验证**：验证API返回数据的完整性
4. **降级方案**：API失败时自动使用直线路线

## 注意事项

1. **配额限制**：高德地图API有日调用量限制，请根据实际需求选择合适的套餐
2. **网络依赖**：路线规划功能需要网络连接，离线状态下会使用直线路线
3. **坐标系统**：高德地图使用GCJ-02坐标系，与GPS坐标略有差异
4. **性能优化**：建议缓存常用路线，减少API调用次数

## 扩展功能

可以根据需要扩展以下功能：

1. **驾车路线规划**：适用于车辆导航
2. **公交路线规划**：提供公共交通方案
3. **路线缓存**：本地存储常用路线
4. **实时路况**：集成交通状况信息
5. **语音导航**：添加语音播报功能

## 技术支持

如遇到问题，可以：

1. 查看浏览器控制台的错误信息
2. 检查高德开放平台的API文档
3. 验证API密钥的有效性和权限
4. 确认网络连接状态
