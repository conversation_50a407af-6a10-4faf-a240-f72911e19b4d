import {getData, removeData} from "./auth.js";
import {config} from "./config.js";

export const request = (option = {}) => {
    // 请求拦截
    // console.log("请求拦截");
    return new Promise((resolve, reject) => {
        uni.request({
            url: config.BASE_URL + option.url,
            data: option.data || {},
            method: option.method || "get",
            header: {
                "Authorization": 'Bearer ' + getData("zhyx_token")
            },
            timeout: config.TIMEOUT,
            success: (response) => { //成功的回调函数
                if (response.data.code !== 200 && option.verificationReturn !== false) {
                    uni.showToast({
                        title: response.data.msg,
                        // title: "接口错误\n"+response.data.msg,
                        icon: "none",
                        duration: 2000
                    })
                }
                if (response.data.code === 401) {
                    uni.showModal({
                        title: '提示',
                        content: '登录已过期，请重返回抄表首页',
                        success: function (res) {
                            if (res.confirm) {
                                removeData("zhyx_token")
                                uni.reLaunch({
                                    url: "/pages/water/meter-manage/index"
                                })
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    });
                }
                if (response.data.code === 200 || option.verificationReturn === false) {
                    resolve(response.data)
                }
                // console.log(response);
            },
            fail: (error) => { //失败的回调函数
                reject(error)
            },
            complete: (info) => { //调用结束的回调函数（调用成功、失败都会执行）
                // console.log(info);
            }
        })
    })
}
