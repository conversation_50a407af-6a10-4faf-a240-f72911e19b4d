<template>
    <view class="container">
        <!-- 顶部导航栏 -->
        <view class="header">
            <view class="nav-bar">
                <uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
                <text class="title">隐患上报</text>
            </view>
        </view>

        <!-- 表单内容 -->
        <view class="form-container">
            <!-- 隐患地点 -->
            <!-- 隐患地点 -->
            <view class="form-item required">
                <view class="form-label">
                    <text class="label-text">隐患地点</text>
                    <text class="required-star">*</text>
                </view>
                <view class="location-input" @click="selectLocation">
                    <image class="location-icon" src="/static/icon/location.png" mode="aspectFit"></image>
                    <text class="location-text" :class="{ placeholder: !latitude }">{{ locationText }}</text>
                    <text class="location-icon-right">📍</text>
                </view>
            </view>

            <!-- 所属分区 -->
            <view class="form-item required">
                <view class="form-label">
                    <text class="label-text">所属分区</text>
                    <text class="required-star">*</text>
                </view>
                <view class="select-input" @click="selectArea">
                    <text class="select-text" :class="{ placeholder: !selectedArea }">{{ selectedArea || '请选择' }}</text>
                    <text class="arrow-icon">›</text>
                </view>
            </view>

            <!-- 隐患类型 -->
            <view class="form-item required">
                <view class="form-label">
                    <text class="label-text">隐患类型</text>
                    <text class="required-star">*</text>
                </view>
                <view class="select-input" @click="selectType">
                    <text class="select-text" :class="{ placeholder: !selectedType }">{{ selectedType || '请选择' }}</text>
                    <text class="arrow-icon">›</text>
                </view>
            </view>

            <!-- 隐患名称 -->
            <view class="form-item">
                <view class="form-label">
                    <text class="label-text">隐患名称</text>
                </view>
                <view class="input-wrapper">
                    <input class="text-input" v-model="dangerName" placeholder="请输入文字"
                        placeholder-class="input-placeholder" />
                </view>
            </view>

            <!-- 上传图片 -->
            <view class="form-item">
                <view class="form-label">
                    <text class="label-text">上传图片</text>
                </view>
                <view class="image-upload">
                    <view class="image-list">
                        <view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
                            <image class="uploaded-image" :src="image" mode="aspectFill"></image>
                            <view class="delete-btn" @click="deleteImage(index)">
                                <text class="delete-icon">×</text>
                            </view>
                        </view>
                        <view class="add-image" @click="addImage" v-if="uploadedImages.length < 9">
                            <text class="add-icon">+</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-container">
            <text class="submit-btn" @click="submitReport">提交</text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            locationText: '请选择隐患地点',
            selectedArea: '',
            selectedType: '',
            dangerName: '',
            uploadedImages: [],
            areaOptions: ['城南区', '城北区', '城东区', '城西区'],
            typeOptions: ['设备故障', '环境污染', '安全隐患', '其他'],
            // 新增地理位置相关数据
            latitude: '',
            longitude: ''
        }
    },
    methods: {
        goBack() {
            uni.navigateBack()
        },
        selectLocation() {
            // Add error handling and platform detection
            try {
                // Check if chooseLocation is available
                if (typeof uni.chooseLocation !== 'function') {
                    console.warn('chooseLocation API not available, using fallback')
                    this.showLocationInputDialog()
                    return
                }

                // Use chooseLocation with proper error handling
                uni.chooseLocation({
                    success: (res) => {
                        console.log('Location selected successfully:', res)
                        if (res && res.latitude && res.longitude) {
                            this.locationText = res.address || res.name || '已选择位置'
                            this.latitude = res.latitude
                            this.longitude = res.longitude
                            
                            uni.showToast({
                                title: '位置选择成功',
                                icon: 'success',
                                duration: 1500
                            })
                        } else {
                            throw new Error('Invalid location data received')
                        }
                    },
                    fail: (err) => {
                        console.error('chooseLocation failed:', err)
                        // Fallback to manual input or current location
                        this.handleLocationError(err)
                    }
                })
            } catch (error) {
                console.error('selectLocation error:', error)
                this.handleLocationError(error)
            }
        },

        // Add error handling method
        handleLocationError(error) {
            console.log('Handling location error:', error)
            
            uni.showModal({
                title: '位置选择',
                content: '地图选点功能暂时不可用，是否使用当前位置或手动输入？',
                confirmText: '当前位置',
                cancelText: '手动输入',
                success: (res) => {
                    if (res.confirm) {
                        this.getCurrentLocation()
                    } else {
                        this.showLocationInputDialog()
                    }
                }
            })
        },

        // Add current location method
        getCurrentLocation() {
            uni.showLoading({
                title: '获取位置中...'
            })
            
            uni.getLocation({
                type: 'gcj02',
                success: (res) => {
                    console.log('Current location:', res)
                    this.latitude = res.latitude
                    this.longitude = res.longitude
                    this.locationText = `位置：${res.latitude.toFixed(6)}, ${res.longitude.toFixed(6)}`
                    
                    uni.hideLoading()
                    uni.showToast({
                        title: '位置获取成功',
                        icon: 'success',
                        duration: 1500
                    })
                },
                fail: (err) => {
                    console.error('getLocation failed:', err)
                    uni.hideLoading()
                    this.showLocationInputDialog()
                }
            })
        },

        // Add manual input dialog
        showLocationInputDialog() {
            uni.showModal({
                title: '手动输入位置',
                editable: true,
                placeholderText: '请输入详细地址',
                success: (res) => {
                    if (res.confirm && res.content) {
                        this.locationText = res.content
                        // Set default coordinates (you can geocode the address if needed)
                        this.latitude = 0
                        this.longitude = 0
                        
                        uni.showToast({
                            title: '地址已设置',
                            icon: 'success',
                            duration: 1500
                        })
                    }
                }
            })
        },
        selectArea() {
            uni.showActionSheet({
                itemList: this.areaOptions,
                success: (res) => {
                    this.selectedArea = this.areaOptions[res.tapIndex]
                }
            })
        },
        selectType() {
            uni.showActionSheet({
                itemList: this.typeOptions,
                success: (res) => {
                    this.selectedType = this.typeOptions[res.tapIndex]
                }
            })
        },
        addImage() {
            uni.chooseImage({
                count: 9 - this.uploadedImages.length,
                sizeType: ['original', 'compressed'],
                sourceType: ['album', 'camera'],
                success: (res) => {
                    this.uploadedImages = this.uploadedImages.concat(res.tempFilePaths)
                }
            })
        },
        deleteImage(index) {
            this.uploadedImages.splice(index, 1)
        },
        submitReport() {
            // Update validation to be more flexible
            if (!this.locationText || this.locationText === '请选择隐患地点') {
                uni.showToast({
                    title: '请选择或输入隐患地点',
                    icon: 'none'
                })
                return
            }
            
            // 验证位置是否已选择
            if (!this.latitude || !this.longitude) {
                uni.showToast({
                    title: '请选择隐患地点',
                    icon: 'none'
                })
                return
            }
            if (!this.selectedArea) {
                uni.showToast({
                    title: '请选择所属分区',
                    icon: 'none'
                })
                return
            }
            if (!this.selectedType) {
                uni.showToast({
                    title: '请选择隐患类型',
                    icon: 'none'
                })
                return
            }

            // 提交逻辑
            const reportData = {
                location: {
                    address: this.locationText,
                    latitude: this.latitude || 0,
                    longitude: this.longitude || 0
                },
                area: this.selectedArea,
                type: this.selectedType,
                name: this.dangerName,
                images: this.uploadedImages
            }
            
            console.log('提交数据：', reportData)
            
            uni.showLoading({
                title: '提交中...'
            })

            setTimeout(() => {
                uni.hideLoading()
                uni.showToast({
                    title: '提交成功',
                    icon: 'success'
                })
                setTimeout(() => {
                    uni.navigateBack()
                }, 1500)
            }, 2000)
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .header {
        background: #1890ff;
        padding-top: var(--status-bar-height);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;

        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;

            .title {
                width: 100%;
                color: #fff;
                font-size: 18px;
                font-weight: 500;
                text-align: center;
            }
        }
    }
}

/* 表单容器 */
.form-container {
    flex: 1;
    margin-top: 150rpx;
    padding: 32rpx 32rpx 120rpx 32rpx; /* 底部增加padding为固定按钮预留空间 */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */

    .form-item {
        margin-bottom: 40rpx;
    }

    .form-label {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
    }

    .label-text {
        font-size: 32rpx;
        color: #333333;
        font-weight: 500;
    }

    .required-star {
        color: #ff4757;
        margin-left: 8rpx;
        font-size: 32rpx;
    }

    /* 位置输入 */
    .location-input {
        height: 88rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        padding: 0 24rpx;
        border: 2rpx solid #e8e8e8;
    }

    .location-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
    }

    .location-text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        
        &.placeholder {
            color: #999999;
        }
    }

    .location-icon-right {
        font-size: 28rpx;
        color: #FF6B35;
    }

    /* 选择输入 */
    .select-input {
        height: 88rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        padding: 0 24rpx;
        border: 2rpx solid #e8e8e8;
        justify-content: space-between;
    }

    .select-text {
        font-size: 28rpx;
        color: #333333;
    }

    .select-text.placeholder {
        color: #999999;
    }

    .arrow-icon {
        font-size: 32rpx;
        color: #cccccc;
    }

    /* 文本输入 */
    .input-wrapper {
        height: 88rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        border: 2rpx solid #e8e8e8;
        padding: 0 24rpx;
        display: flex;
        align-items: center;
    }

    .text-input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
        color: #333333;
    }

    .input-placeholder {
        color: #999999;
    }

    /* 图片上传 */
    .image-upload {
        background-color: #ffffff;
        border-radius: 12rpx;
        padding: 24rpx;
        border: 2rpx solid #e8e8e8;
    }

    .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;
    }

    .image-item {
        position: relative;
        width: 160rpx;
        height: 160rpx;
        border-radius: 12rpx;
        overflow: hidden;
    }

    .uploaded-image {
        width: 100%;
        height: 100%;
    }

    .delete-btn {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: #ff4757;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .delete-icon {
        color: #ffffff;
        font-size: 24rpx;
        font-weight: bold;
    }

    .add-image {
        width: 160rpx;
        height: 160rpx;
        border: 2rpx dashed #cccccc;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fafafa;
    }

    .add-icon {
        font-size: 48rpx;
        color: #cccccc;
    }
}

/* 提交按钮 */
.submit-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0;
    padding: 16px;
    background: #1890ff;
    border-radius: 0;
    text-align: center;
    transition: background-color 0.3s;
    z-index: 999;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

    .submit-btn {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
    }
}
</style>