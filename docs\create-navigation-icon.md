# 创建导航图标说明

## 需要创建的图标

为了让高德导航按钮显示正确的图标，需要创建一个 `navigation.png` 文件。

## 图标规格

- **文件名**: `navigation.png`
- **位置**: `static/icon/navigation.png`
- **尺寸**: 48x48 像素（推荐）
- **格式**: PNG（支持透明背景）
- **颜色**: 白色或浅色（因为按钮背景是蓝色）

## 图标设计建议

### 方案1：导航箭头
```
    ▲
   ▲▲▲
  ▲▲ ▲▲
 ▲▲   ▲▲
▲▲     ▲▲
```

### 方案2：指南针样式
```
    N
    ↑
W ← ● → E
    ↓
    S
```

### 方案3：简单箭头
```
  ↗
```

## 创建方法

### 方法1：使用在线图标生成器
1. 访问 https://www.flaticon.com/ 或 https://icons8.com/
2. 搜索 "navigation" 或"导航"
3. 选择合适的图标
4. 下载为48x48的PNG格式
5. 重命名为 `navigation.png`
6. 放置到 `static/icon/` 目录

### 方法2：使用提供的HTML工具
1. 打开 `static/icon/create-navigation-icon.html`
2. 在浏览器中查看生成的图标
3. 点击"下载导航图标"按钮
4. 将下载的文件重命名为 `navigation.png`
5. 放置到 `static/icon/` 目录

### 方法3：使用图像编辑软件
1. 打开Photoshop、GIMP或其他图像编辑软件
2. 创建48x48像素的新文档
3. 背景设为透明
4. 绘制白色的导航箭头或指南针图标
5. 保存为PNG格式

## 临时解决方案

如果暂时无法创建专门的导航图标，当前代码使用了 `location.png` 作为替代，并通过CSS滤镜将其变为白色：

```css
.amap-icon {
  filter: brightness(0) invert(1); // 将图标变为白色
}
```

## 验证图标

创建图标后，可以通过以下方式验证：

1. 确保文件路径正确：`static/icon/navigation.png`
2. 在浏览器开发者工具中检查图标是否加载成功
3. 确保图标在蓝色背景上清晰可见

## 注意事项

- 图标应该简洁明了，在小尺寸下也能清晰识别
- 使用白色或浅色，确保在蓝色背景上有足够的对比度
- PNG格式支持透明背景，效果更好
- 如果图标太复杂，在16x16的显示尺寸下可能不清晰
