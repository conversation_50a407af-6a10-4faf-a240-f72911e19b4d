// import request from '@/config/request.js';

// // export const login = (params) => request.post('/wx/getWxVolumesList', params, { custom: { auth: false } })

// // 抄表册本小区统计列表
// export const getMeterReadingDistrictList = (params) => request.get('/charge/wx/getMeterReadingDistrictList', params)

// // 抄表册本小区统计列表
// export const getDistrictReadingList = (params) => request.get('/charge/wx/getDistrictReadingList', params)

// // 历史记录
// export const getVolumesHistoryList = (params) => request.get('/charge/wx/getVolumesHistoryList', params)

// // 抄表管理/抄表记录 ——抄表数据列表
// export const getMeterReadingList = (params) => request.get('/charge/wx/getMeterReadingList', params)

// // 开始抄表——获取下一次抄表数据
// export const getNextData = (params) => request.get('/charge/wx/getNextData', params)

// // 开始抄表——计算用水量
// export const computeWaterConsumption = (params) => request.post('/charge/wx/computeWaterConsumption', params)

// // 开始抄表——抄表数据保存
// export const saveMeterReading = (params) => request.post('/charge/wx/saveMeterReading', params)

// // 复查——获取复查数据
// export const getReviewInfo = (params) => request.get('/charge/wx/getReviewInfo/'+params)


// // 复查——获取复查详情 reviewId
// export const getReviewMore = (params) => request.get('/charge/wx/getReviewMore/'+params)

// // 复查——复查数据保存
// export const saveReviewData = (params) => request.post('/charge/wx/saveReviewData',params)






import { request } from "./http/request.js"




// 抄表册本状态统计
export function getDistrictReadingList(data) {
	return request({
		url: `/revenue/app/getDistrictReadingList/${data}`,
		method: 'get'
	})
}

// 开始抄表——抄表数据保存
export function saveMeterReading(data) {
	return request({
		url: `/revenue/app/saveMeterReading`,
		method: 'post',
		data
	})
}

// 开始抄表——计算用水量
export function computeWaterConsumption(data) {
	return request({
		url: `/revenue/app/computeWaterConsumption`,
		method: 'post',
		data
	})
}

// 开始抄表——获取下一次抄表数据
export function getNextData(data) {
	return request({
		url: `/revenue/app/getNextData`,
		method: 'get',
		data
	})
}