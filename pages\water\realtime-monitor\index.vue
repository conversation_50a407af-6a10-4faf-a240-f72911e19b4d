<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="nav-bar">
				<uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
				<text class="title">实时监控</text>
				<uni-icons type="reload" size="20" color="#fff" @click="refreshTasks"></uni-icons>
			</view>
		</view>

		<!-- 显示模式切换 -->
		<view class="mode-switch">
			<view class="switch-item" :class="{ active: currentMode === 'tree' }" @click="switchMode('tree')">
				设备选择
			</view>
			<view class="switch-item" :class="{ active: currentMode === 'result' }" @click="switchMode('result')">
				监控数据
			</view>
		</view>

		<!-- 设备树选择页面 -->
		<view v-show="currentMode === 'tree'" class="tree-page">
			<view class="search-header">
				<view class="search-container">
					<input type="text" placeholder="请输入关键字" v-model="keyword" class="search-input"
						:disabled="treeLoading" />
				</view>
			</view>
			<scroll-view scroll-y class="tree-scroll">
				<view class="tree-container">
					<!-- 优化的加载状态显示 -->
					<view v-if="treeLoading" class="loading-container">
						<view class="loading-content">
							<uni-icons type="spinner-cycle" size="24" color="#1890ff" class="loading-icon"></uni-icons>
							<text class="loading-text">正在加载设备树...</text>
						</view>
					</view>

					<!-- 空状态显示 -->
					<view v-else-if="!treeData.length" class="empty-container">
						<uni-icons type="info-filled" size="48" color="#ccc"></uni-icons>
						<text class="empty-text">暂无设备数据</text>
						<view class="retry-button" @click="fetchDeviceTree">
							<uni-icons type="reload" size="16" color="#1890ff"></uni-icons>
							<text>重新加载</text>
						</view>
					</view>

					<!-- 树节点列表 -->
					<tree-node v-else v-for="node in filteredTreeData" :key="node.id" :node="node" @toggle="toggleNode"
						@select="selectNode"></tree-node>
				</view>
			</scroll-view>

			<view class="query-button-container">
				<u-button :loading="loading" @click="queryMonitorData" class="query-button" :disabled="treeLoading">
					{{ treeLoading ? '加载中...' : '查询监控数据' }}
				</u-button>
			</view>
		</view>

		<!-- 监控数据结果页面 -->
		<view v-show="currentMode === 'result'" class="result-page">
			<view v-if="loading" class="loading-container">
				<u-loadmore status="loading"></u-loadmore>
			</view>
			<view v-else-if="!monitorData.length" class="empty-container">
				<u-empty mode="data" text="暂无数据，请先选择设备并查询"></u-empty>
			</view>
			<view v-else class="content">
				<view class="monitor-card" v-for="(item, index) in monitorData" :key="index">
					<view class="card-title">{{ item.name }}</view>
					<view class="report-time">上报时间：{{ item.time }}</view>
					<view class="data-list">
						<view class="data-item" v-for="(childrenItem, dataIndex) in item.realList" :key="dataIndex">
							<text class="label">{{ childrenItem.name }}</text>
							<text class="value" @click="goToHistory(item, childrenItem)">{{ childrenItem.value }}</text>
							<text class="unit">{{ childrenItem.unit }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import treeNode from './treeNode.vue';
import { getDeviceTree } from "../../../api/tree.js";
import { getRealtimeData } from "../../../api/realtime.js";
import { formatTimestamp } from '../../../utils/timeUtils.js';

export default {
	components: {
		treeNode
	},
	data() {
		return {
			currentMode: 'tree', // 当前显示模式：tree-设备选择，result-监控数据
			keyword: '',
			treeData: [],
			loadedNodes: new Set(), // 记录已加载的节点
			nodeCache: new Map(), // 节点数据缓存
			monitorData: [],
			timer: null,
			loading: false,
			treeLoading: false, // 树加载状态
			lastFilterTime: 0 // 最后过滤时间，用于防抖
		};
	},
	computed: {
		filteredTreeData() {
			if (!this.keyword.trim()) {
				return this.treeData;
			}
			// 简单的防抖检查
			const now = Date.now();
			if (now - this.lastFilterTime < 300) {
				return this.treeData; // 返回原数据，避免频繁过滤
			}
			this.lastFilterTime = now;

			const keyword = this.keyword.toLowerCase().trim();

			const filterNodes = (nodes) => {
				const results = [];
				for (const node of nodes) {
					const children = node.children ? filterNodes(node.children) : [];
					if (node.name.toLowerCase().includes(keyword) || children.length > 0) {
						// 使用Vue.set或者this.$set来确保响应式
						const filteredNode = {
							...node,
							children: children,
							open: true, // 搜索时展开节点
							// 保持原有的响应式属性
							selected: node.selected,
							loading: node.loading
						};
						results.push(filteredNode);
					}
				}
				return results;
			};

			return filterNodes(this.treeData);
		}
	},
	created() {
		this.fetchDeviceTree();
		// 移除自动查询和定时器，改为手动查询
	},
	beforeDestroy() {
		// 清理定时器
		if (this.timer) {
			clearInterval(this.timer);
			this.timer = null;
		}
		// 清理缓存
		this.nodeCache.clear();
		this.loadedNodes.clear();
	},
	onUnload() {
		// uni-app页面卸载时清理资源
		if (this.timer) {
			clearInterval(this.timer);
			this.timer = null;
		}
		this.nodeCache.clear();
		this.loadedNodes.clear();
	},
	methods: {
		// 切换显示模式
		switchMode(mode) {
			this.currentMode = mode;
		},
		// 查询监控数据
		async queryMonitorData() {
		try {
			// 先切换到结果模式
			this.currentMode = 'result';
			// 显示加载状态
			this.loading = true;
			await this.confirmSelection();
			// 如果没有数据，显示提示
			if (this.monitorData.length === 0) {
				uni.showToast({
					title: '请先选择设备',
					icon: 'none'
				});
			}
		} catch (error) {
			console.error('查询监控数据失败:', error);
			uni.showToast({
				title: '查询失败，请重试',
				icon: 'none'
			});
		} finally {
			this.loading = false;
		}
		},
		async fetchDeviceTree() {
			this.treeLoading = true;
			try {
				// 只加载根节点
				const res = await getDeviceTree({ "categoryIds": [], "level": 1 });
				if (res) {
					this.treeData = this.addStateToTree([res.data], true, true); // 第三个参数表示是否为根节点
				}
			} catch (error) {
				console.error('加载设备树失败:', error);
			} finally {
				this.treeLoading = false;
			}
		},
		// 新增：懒加载子节点方法（带缓存优化）
		async loadChildNodes(node) {
			if (this.loadedNodes.has(node.id)) {
				return;
			}

			// 检查缓存
			if (this.nodeCache.has(node.id)) {
				node.children = this.nodeCache.get(node.id);
				this.loadedNodes.add(node.id);
				return;
			}

			if (!node.hasChildren && (!node.children || node.children.length === 0)) {
				return;
			}

			try {
				node.loading = true;
				const res = await getDeviceTree({
					"categoryIds": [],
					"parentId": node.id
				});

				if (res && res.data && res.data.children) {
					const children = this.addStateToTree(res.data.children, false, false);
					node.children = children;
					// 缓存子节点数据
					this.nodeCache.set(node.id, children);
					this.loadedNodes.add(node.id);
				}
			} catch (error) {
				console.error('加载子节点失败:', error);
			} finally {
				node.loading = false;
			}
		},
		addStateToTree(nodes, isTopLevel = true, isRoot = false) {
			let firstRtuFound = false;
			const processNodes = (nodes, topLevel) => {
				return nodes.map(node => {
					const newNode = {
						...node,
						open: isRoot, // 只有根节点默认展开
						selected: false,
						loading: false
					};

					if (topLevel && node.weight === 'rtu' && !firstRtuFound) {
						newNode.selected = true;
						firstRtuFound = true;
					}

					// 懒加载：只保留子节点的占位信息
					if (node.children && node.children.length > 0 && !isRoot) {
						newNode.hasChildren = true;
						newNode.children = []; // 清空子节点，等待懒加载
					} else if (node.children && node.children.length > 0) {
						newNode.children = processNodes(node.children, false);
					}

					return newNode;
				});
			};
			return processNodes(nodes, isTopLevel);
		},
		goBack() {
			uni.navigateBack();
		},
		async toggleNode(node) {
			if (node.hasChildren && node.children.length === 0 && !node.loading) {
				await this.loadChildNodes(node);
			}
			node.open = !node.open;
		},
		selectNode(node) {
			// 找到原始节点并更新其状态
			const findAndUpdateOriginalNode = (nodes, targetNode) => {
				for (const originalNode of nodes) {
					if (originalNode.id === targetNode.id) {
						originalNode.selected = !originalNode.selected;
						// 如果有子节点，则所有子节点跟随父节点选中/取消选中
						if (originalNode.children && originalNode.children.length) {
							const setChildrenState = (children, selected) => {
								children.forEach(child => {
									child.selected = selected;
									if (child.children && child.children.length) {
										setChildrenState(child.children, selected);
									}
								});
							};
							setChildrenState(originalNode.children, originalNode.selected);
						}
						return true;
					}
					if (originalNode.children && originalNode.children.length) {
						if (findAndUpdateOriginalNode(originalNode.children, targetNode)) {
							return true;
						}
					}
				}
				return false;
			};

			findAndUpdateOriginalNode(this.treeData, node);
			
			// 使用 Vue.set 或 this.$set 确保响应式更新
			this.$set(this.treeData, 'length', this.treeData.length);
		},
		async confirmSelection() {
			this.loading = true;
			const selectedIds = this.getSelectedIds(this.treeData);
			if (selectedIds.length === 0) {
				this.monitorData = [];
				this.loading = false;
				return;
			}
			const res = await getRealtimeData({
				pageNum: 1,
				pageSize: 9999,
				state: '',
				deviceId: selectedIds,
				paramTypeId: [1, 2, 3, 11, 4, 5, 6, 7, 8, 9, 10]
			});
			if (res && res.data) {
				this.monitorData = res.data.map(item => {
					item.status = 0;
					item.time = "";
					if (item.realList && item.realList.length > 0) {
						item.status = item.state;
						if (item.realList[0].ts) {
							console.log("Original timestamp:", item.realList[0].ts);
							item.time = formatTimestamp(item.realList[0].ts);
						} else {
							item.time = "";
						}
						item.realList.map(real => {
							real.value = real.value === "true" ? "是" : (real.value === "false" ? "否" : real.value);
						});
					}
					return item;
				});
			}
			this.loading = false;
		},
		getSelectedIds(nodes) {
			let ids = [];
			nodes.forEach(node => {
				// 只选择设备节点
				if (node.selected && node.weight === 'rtu') {
					ids.push(node.id);
				}
				if (node.children && node.children.length) {
					ids = ids.concat(this.getSelectedIds(node.children));
				}
			});
			return ids;
		},
		goToHistory(data, childrenData) {
			const deviceId = data.id;
			const deviceName = data.name;
			const paramId = childrenData.tsModelRelation.id;
			const paramName = childrenData.name;
			uni.navigateTo({
				url: `/pages/water/realtime-monitor/historyData?deviceId=${deviceId}&deviceName=${deviceName}&paramId=${paramId}&paramName=${paramName}`
			});
		},
		// 刷新任务（导航栏按钮）
		refreshTasks() {
			// 清理缓存和已加载节点记录
			this.nodeCache.clear();
			this.loadedNodes.clear();

			// 重新加载设备树
			this.fetchDeviceTree();

			// 显示刷新成功提示
			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1500
			});
		},
	},

	// 移除watch，不再需要监听弹窗状态
};
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	background: #1890ff;
	padding-top: var(--status-bar-height);
	// 添加阴影效果
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;

		.title {
			width: 100%;
			color: #fff;
			font-size: 18px;
			font-weight: 500;
			text-align: center;
		}
	}
}

/* 优化模式切换样式 */
.mode-switch {
	display: flex;
	background-color: #fff;
	border-bottom: 1px solid #f0f0f0;
	position: sticky;
	top: 0;
	z-index: 99;
	// 添加阴影
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

	.switch-item {
		flex: 1;
		text-align: center;
		padding: 16px 0;
		font-size: 16px;
		color: #666;
		border-bottom: 3px solid transparent;
		transition: all 0.3s ease;
		cursor: pointer;
		user-select: none;
		position: relative;

		&:hover {
			background-color: rgba(24, 144, 255, 0.02);
		}

		&.active {
			color: #1890ff;
			border-bottom-color: #1890ff;
			font-weight: 600;
			background-color: rgba(24, 144, 255, 0.05);

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				background: linear-gradient(90deg, #1890ff, #40a9ff);
			}
		}
	}
}

/* 优化树页面样式 */
.tree-page {
	height: calc(100vh - 140px);
	display: flex;
	flex-direction: column;
	background-color: #fff;

	.search-header {
		padding: 16px;
		border-bottom: 1px solid #f0f0f0;
		background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
	}

	.tree-scroll {
		flex: 1;
		overflow-y: auto;

		// 优化滚动条样式
		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background: linear-gradient(135deg, #ccc, #999);
			border-radius: 10px;

			&:hover {
				background: linear-gradient(135deg, #999, #666);
			}
		}

		&::-webkit-scrollbar-track {
			background: #f8f9fa;
			border-radius: 10px;
		}
	}

	.tree-container {
		padding: 12px;

		/* 优化加载状态样式 */
		.loading-container {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 60px 20px;
			min-height: 200px;

			.loading-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 16px;

				.loading-icon {
					animation: spin 1s linear infinite;
				}

				.loading-text {
					font-size: 14px;
					color: #666;
					text-align: center;
				}
			}
		}

		/* 空状态样式 */
		.empty-container {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			padding: 80px 20px;
			gap: 16px;

			.empty-text {
				font-size: 16px;
				color: #999;
				margin-top: 8px;
			}

			.retry-button {
				display: flex;
				align-items: center;
				gap: 8px;
				padding: 8px 16px;
				background-color: rgba(24, 144, 255, 0.1);
				border-radius: 20px;
				margin-top: 16px;
				cursor: pointer;
				transition: all 0.3s ease;

				&:hover {
					background-color: rgba(24, 144, 255, 0.2);
				}

				text {
					font-size: 14px;
					color: #1890ff;
				}
			}
		}
	}

	.query-button-container {
		margin: 16px;
		padding: 7px;
		background: #1890ff;
		border-radius: 8px;
		text-align: center;

		.query-button {
			background: #1890ff;
			border-color: #1890ff;
			color: #fff;
			font-size: 16px;
			font-weight: 500;
		}
	}
}

/* 优化结果页面样式 */
.result-page {
	height: calc(100vh - 140px);
	overflow-y: auto;
	background: linear-gradient(135deg, #f5f5f5 0%, #e8f4fd 100%);

	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40px 20px;
		min-height: 200px;
	}

	.empty-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60px 20px;
	}

	.content {
		padding: 12px;
	}

	.monitor-card {
		background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
		border-radius: 16px;
		padding: 20px;
		margin-bottom: 16px;
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
		border: 1px solid #f0f0f0;
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
		}
	}

	.card-title {
		font-size: 18px;
		font-weight: 700;
		margin-bottom: 8px;
		color: #1a1a1a;
		// 添加渐变文字效果
		background: linear-gradient(135deg, #1890ff, #722ed1);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.report-time {
		font-size: 13px;
		color: #8c8c8c;
		margin-bottom: 16px;
		padding: 4px 8px;
		background-color: rgba(24, 144, 255, 0.05);
		border-radius: 6px;
		display: inline-block;
	}

	.data-list {
		border-top: 2px solid #f0f0f0;
		padding-top: 12px;
	}

	.data-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px 0;
		border-bottom: 1px solid #f8f9fa;
		transition: background-color 0.2s;
		border-radius: 8px;
		margin-bottom: 4px;

		&:hover {
			background-color: rgba(24, 144, 255, 0.02);
		}

		&:last-child {
			border-bottom: none;
		}
	}

	.label {
		font-size: 15px;
		color: #595959;
		width: 45%;
		font-weight: 500;
	}

	.value {
		font-size: 15px;
		color: #1890ff;
		width: 40%;
		text-align: center;
		font-weight: 600;
		cursor: pointer;
		padding: 4px 8px;
		border-radius: 6px;
		transition: all 0.2s;

		&:hover {
			background-color: rgba(24, 144, 255, 0.1);
			transform: scale(1.05);
		}

		&:active {
			transform: scale(0.98);
		}
	}

	.unit {
		font-size: 14px;
		color: #8c8c8c;
		width: 15%;
		text-align: right;
		font-weight: 500;
	}
}

// 添加加载动画
.loading-container {
	.uni-load-more {
		animation: pulse 2s infinite;
	}
}

@keyframes pulse {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0.5;
	}

	100% {
		opacity: 1;
	}
}

// 响应式设计优化
@media screen and (max-width: 750rpx) {
	.data-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 8px;

		.label,
		.value,
		.unit {
			width: 100%;
			text-align: left;
		}

		.value {
			text-align: center;
			margin: 4px 0;
		}
	}
}

.search-container {
	position: relative;
}

.search-input {
	width: 100%;
	height: 40px;
	padding: 0 16px;
	border: 1px solid #d9d9d9;
	border-radius: 20px;
	font-size: 14px;
	background-color: #fafafa;
	box-sizing: border-box;

	&:focus {
		border-color: #1890ff;
		outline: none;
		background-color: #fff;
	}
}
</style>
