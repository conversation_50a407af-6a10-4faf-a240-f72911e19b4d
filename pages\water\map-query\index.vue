<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="nav-bar">
				<uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
				<text class="title">地图查询</text>
				<uni-icons type="reload" size="20" color="#fff" @click="refreshTasks"></uni-icons>
			</view>
		</view>

		<!-- 地图容器 -->
		<view class="map-container">
			<!-- #ifdef APP-PLUS -->
			<map id="amapContainer" class="map" :longitude="mapCenter.longitude" :latitude="mapCenter.latitude"
				:scale="mapScale" :markers="markers" :show-location="true" :enable-3D="true" :enable-overlooking="true"
				:enable-zoom="true" :enable-scroll="true" :enable-rotate="true" :zIndex="1" @markertap="onMarkerTap"
				@regionchange="onRegionChange" @tap="onMapTap">
			</map>
			<!-- #endif -->

			<!-- 地图控制按钮 -->
			<cover-view class="map-controls">
				<cover-image class="location-btn" src="../../../static/icon/location.png"
					@click="centerToLocation"></cover-image>
			</cover-view>
		</view>
	</view>
</template>

<script>
import { getRealtimeData } from '@/api/realtime.js';
import { formatDateTime } from '../../../utils/timeUtils.js';
import { wgs84ToGcj02, convertMarkersCoordinates } from '@/utils/coordinateUtils.js';

export default {
	data() {
		return {
			// 地图中心点（山西太原坐标）
			mapCenter: {
				longitude: 112.53,
				latitude: 37.87
			},
			// 地图缩放级别
			mapScale: 13,
			// 标记点数组
			markers: [],
			// 实时数据
			realtimeData: [],
			// 地图上下文
			mapContext: null,
			// 用户位置
			userLocation: null,
			// 用户位置标记
			userLocationMarker: null
		};
	},

	async onLoad() {
		// 初始化地图上下文
		this.initMapContext();
		// 获取实时数据并添加标记点
		await this.loadRealtimeData();
		this.getCurrentLocation();
	},

	onReady() {
		// 页面渲染完成后初始化地图上下文
		this.initMapContext();
	},

	onUnload() {
		// 清理资源
		this.mapContext = null;
	},

	methods: {
		// 初始化地图上下文
		initMapContext() {
			// #ifdef APP-PLUS
			this.mapContext = uni.createMapContext('amapContainer', this);
			console.log('高德地图上下文初始化成功');
			// #endif
		},

		// 获取实时数据
		async loadRealtimeData() {
			try {
				const res = await getRealtimeData({ pageNum: 1, pageSize: 500 });
				// 确保 res 是数组
				const dataArray = Array.isArray(res) ? res : (res && Array.isArray(res.data) ? res.data : []);

				this.realtimeData = dataArray.filter(item => item.deviceExternalInfo.deviceLocation && item.deviceExternalInfo.deviceLocation !== "null").map(item => {
					const deviceLocation = JSON.parse(item.deviceExternalInfo.deviceLocation);
					return {
						id: item.id,
						name: item.name,
						longitude: deviceLocation.longitude,
						latitude: deviceLocation.latitude,
						realList: item.realList,
						status: item.state,
						type: item.type || 'normal',
						time: item.realList.length ? formatDateTime(new Date(item.realList[0].ts)) : ''
					}
				});

				if (dataArray.length > 0) {
					this.createMarkersFromData(this.realtimeData);
				} else {
					// 如果没有实时数据，添加示例标记点
					this.createSampleMarkers();
				}
			} catch (error) {
				console.error('获取实时数据失败:', error);
				// 添加示例标记点
				this.createSampleMarkers();
			}
		},

		// 根据实时数据创建标记点
		createMarkersFromData(dataArray) {
			const newMarkers = dataArray.map((item, index) => {
				// 如果数据来源是WGS84坐标，需要转换
				const longitude = item.lng || item.longitude || (112.53 + (Math.random() - 0.5) * 0.1);
				const latitude = item.lat || item.latitude || (37.87 + (Math.random() - 0.5) * 0.1);

				// 检查是否需要坐标转换（根据数据来源判断）
				if (this.needCoordinateConversion(item)) {
					const converted = wgs84ToGcj02(longitude, latitude);
					longitude = converted.lng;
					latitude = converted.lat;
				}

				return {
					id: `marker_${index}`,
					longitude: longitude,
					latitude: latitude,
					title: item.name || `设备${index + 1}`,
					iconPath: this.getMarkerIcon(item.type || item.status),
					width: 32,
					height: 32,
					callout: {
						content: item.name || `设备${index + 1}`,
						color: '#333',
						fontSize: 14,
						borderRadius: 8,
						bgColor: '#ffffff',
						padding: 8,
						display: 'BYCLICK'
					},
					data: item // 添加 data 属性，包含完整的设备信息
				};
			});

			this.markers = newMarkers;
		},

		// 判断是否需要坐标转换
		needCoordinateConversion(item) {
			// 根据数据来源或标识判断是否需要转换
			// 例如：如果数据来源标识为'gps'或'wgs84'，则需要转换
			return item.coordinateSystem === 'wgs84' || item.source === 'gps';
		},

		createSampleMarkers() {
			const sampleData = [
				{
					name: '南方风机',
					longitude: 112.5264,
					latitude: 37.8736,
					pressure: '408.930 kPa',
					status: 'normal'
				},
				{
					name: '生态空调',
					longitude: 112.5384,
					latitude: 37.8756,
					pressure: '408.930 kPa',
					status: 'normal'
				},
				{
					name: '信华金属',
					longitude: 112.5444,
					latitude: 37.8696,
					pressure: '408.930 kPa',
					status: 'warning'
				},
				{
					name: '东云天下',
					longitude: 112.5504,
					latitude: 37.8616,
					pressure: '408.930 kPa',
					status: 'error'
				}
			];

			const newMarkers = sampleData.map((item, index) => {
				// 根据状态选择不同的图标
				let iconPath = '/static/icons/marker-normal.png';
				let bgColor = '#1890ff';

				switch (item.status) {
					case 'warning':
						iconPath = '/static/icons/marker-warning.png';
						bgColor = '#fa8c16';
						break;
					case 'error':
						iconPath = '/static/icons/marker-error.png';
						bgColor = '#ff4d4f';
						break;
					default:
						iconPath = '/static/icons/marker-normal.png';
						bgColor = '#52c41a';
				}

				return {
					id: index,
					longitude: item.longitude,
					latitude: item.latitude,
					title: item.name,
					iconPath: iconPath,
					width: 32,
					height: 32,
					callout: {
						content: `${item.name}\n压力: ${item.pressure}`,
						color: '#ffffff',
						fontSize: 12,
						borderRadius: 6,
						bgColor: bgColor,
						padding: 10,
						display: 'BYCLICK'
					},
					data: item
				};
			});

			this.markers = newMarkers;
		},

		// 标记点点击事件
		// 标记点点击事件
		onMarkerTap(e) {
		    console.log('标记点被点击:', e.detail); // 添加调试日志
		    const markerId = e.detail.markerId;
		    const marker = this.markers.find(m => m.id === markerId);

		    console.log('找到的标记点:', marker); // 添加调试日志

		    if (marker) {
		        if (marker.data) {
		            // 显示详细信息弹窗
		            this.showMarkerDetail(marker.data);
		        } else {
		            console.log('标记点没有 data 属性');
		            // 可以显示基本信息
		            uni.showModal({
		                title: marker.title || '设备信息',
		                content: '暂无详细数据',
		                showCancel: false
		            });
		        }
		    } else {
		        console.log('未找到对应的标记点');
		    }
		},

		// 显示标记点详细信息（简化版）
		showMarkerDetail(data) {
			let content = `\n`;

			if (data.realList && Array.isArray(data.realList) && data.realList.length > 0) {
				//content += `\n实时数据:\n`;
				const latestData = formatDateTime(new Date(data.realList[0].ts));
				content += `数据更新时间: ${latestData}\n\n`;

				data.realList.forEach(param => {
					// 使用特殊字符来标识需要高亮的值
					content += `${param.name}: 【${param.value}】 ${param.unit || ''}\n`;
				});
			} else {
				content += `\n暂无实时数据`;
			}

			if (data.status) {
				content += `\n状态: ${this.getStatusText(data.status)}`;
			}

			uni.showModal({
				title: `${data.name || '未知设备'}`,
				content: content,
				showCancel: true,
				cancelText: '关闭'
			});
		},

		// 获取状态文本
		getStatusText(status) {
			switch (status) {
				case 'normal': return '正常';
				case 'warning': return '警告';
				case 'error': return '故障';
				default: return '未知';
			}
		},

		// 获取标记图标
		getMarkerIcon(type) {
			// 根据设备类型或状态返回对应的图标
			switch (type) {
				case 'normal':
					return '/static/icons/marker-normal.png';
				case 'warning':
					return '/static/icons/marker-warning.png';
				case 'error':
					return '/static/icons/marker-error.png';
				case 'pressure': // 压力设备
					return '/static/icons/marker-normal.png';
				case 'flow': // 流量设备
					return '/static/icons/marker-normal.png';
				case 'level': // 液位设备
					return '/static/icons/marker-normal.png';
				default:
					return '/static/icons/marker-normal.png';
			}
		},

		// 地图区域变化事件
		onRegionChange(e) {
			if (e.type === 'end') {
				console.log('地图区域变化:', e.detail);
			}
		},

		// 地图点击事件
		onMapTap(e) {
			console.log('地图点击:', e.detail);
		},

		// 定位到当前位置
		// 定位到当前位置
		centerToLocation() {
			if (this.userLocation) {
				// 更新地图中心点
				this.mapCenter = {
					latitude: this.userLocation.latitude,
					longitude: this.userLocation.longitude
				};
				// 放大地图层级
				this.mapScale = 17;
				// 添加红色定位点标记
				this.addUserLocationMarker();
				console.log('定位到当前位置:', this.mapCenter);
				this.mapContext.moveToLocation();
			} else {
				this.getCurrentLocation();
				uni.showToast({
					title: '正在获取位置...',
					icon: 'loading'
				});
			}
		},
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.userLocation = {
						latitude: res.latitude,
						longitude: res.longitude
					};

					// 自动定位到当前位置并添加标记
					this.mapCenter = {
						latitude: res.latitude,
						longitude: res.longitude
					};

					// 放大地图层级
					this.mapScale = 17;

					// 添加红色定位点标记
					this.addUserLocationMarker();

					uni.showToast({
						title: '定位成功',
						icon: 'success'
					});
				},
				fail: (err) => {
					console.error('获取位置失败:', err);
					uni.showToast({
						title: '获取位置失败',
						icon: 'none'
					});
				}
			});
		},
		// 添加用户位置标记
		addUserLocationMarker() {
			if (!this.userLocation) return;

			// 创建红色定位点标记
			this.userLocationMarker = {
				id: 'user-location',
				longitude: this.userLocation.longitude,
				latitude: this.userLocation.latitude,
				title: '我的位置',
				iconPath: '/static/icons/user-location.png', // 使用红色定位图标
				width: 36,
				height: 36,
				callout: {
					content: '我的位置',
					color: '#ffffff',
					fontSize: 12,
					borderRadius: 6,
					bgColor: '#ff4d4f', // 红色背景
					padding: 8,
					borderWidth: 1,
					borderColor: '#ff4d4f',
					display: 'ALWAYS',
					textAlign: 'center'
				},
				anchor: {
					x: 0.5,
					y: 1
				}
			};

			// 将用户位置标记添加到标记数组中
			this.updateMarkersWithUserLocation();
		},

		// 更新标记数组，包含用户位置标记
		updateMarkersWithUserLocation() {
			// 移除之前的用户位置标记
			this.markers = this.markers.filter(marker => marker.id !== 'user-location');

			// 添加新的用户位置标记
			if (this.userLocationMarker) {
				this.markers = [...this.markers, this.userLocationMarker];
			}
		},

		// 移除用户位置标记
		removeUserLocationMarker() {
			this.markers = this.markers.filter(marker => marker.id !== 'user-location');
			this.userLocationMarker = null;
		},

		// 刷新标记点
		async refreshMarkers() {
			uni.showLoading({
				title: '刷新中...'
			});

			try {
				await this.loadRealtimeData();
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				});
			} catch (error) {
				uni.showToast({
					title: '刷新失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 刷新任务（导航栏按钮）
		refreshTasks() {
			this.refreshMarkers();
		},

		// 跳转到历史数据页面
		goToHistory(data) {
			if (data.id) {
				uni.navigateTo({
					url: `/pages/water/realtime-monitor/historyData?deviceId=${deviceId}&deviceName=${paramId}`
				});
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack();
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f5;

	.header {
		background: #1890ff;
		padding-top: var(--status-bar-height);

		.nav-bar {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 12px 16px;

			.title {
				width: 100%;
				color: #fff;
				font-size: 18px;
				font-weight: 500;
				text-align: center;
			}
		}
	}

	.map-container {
		width: 100%;
		height: calc(100vh - 75px);
		position: relative;

		.map {
			width: 100%;
			height: 100%;
			position: relative;
		}

		// 地图控制按钮
		.map-controls {
			position: absolute;
			right: 15px;
			bottom: 80px;
			background: #fff;
			border-radius: 30%;

			.location-btn {
				width: 29px;
				height: 29px;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 4px rgba(0, 0, 0, 0.1);
				border: 1px solid rgba(0, 0, 0, 0.05);
				transition: all 0.2s ease;

				&:active {
					transform: scale(0.95);
					box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
				}

				.location-icon {
					width: 20px;
					height: 20px;
				}
			}
		}
	}

}
</style>
