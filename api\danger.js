import { request } from './server.js'

const serverUrl = "http://59.49.48.115:19714/api";

//获取隐患任务列表
export const getDangerTaskList =
    function(params) {
        return request.post(`${serverUrl}/inspection/danger/page`, params)
            .then(data => {
                return data.data
            })
    }

//我的上报列表
export const getMyDangerList =
    function(params) {
        return request.post(`${serverUrl}/inspection/danger/page`, params)
            .then(data => {
                return data.data
            })
    }

//已关闭的隐患列表
export const getClosedDangerList =
    function(params) {
        return request.post(`${serverUrl}/inspection/danger/page`, params)
            .then(data => {
                return data.data
            })
    }


//获取隐患任务详情
export const getDangerTaskDetail =
    function(dangerId) {
        return request.post(`${serverUrl}/inspection/danger/${dangerId}`)
            .then(data => {
                return data.data
            })
    }

//隐患处理
export const handleDanger =
    function(params) {
        return request.post(`${serverUrl}/inspection/danger/deal`, params)
            .then(data => {
                return data.data
            })
    }