<template>
	<view>
		<!-- 维修开始时间 -->
		<u-calendar :show="show" @confirm="confirm" @close="close"></u-calendar>
		<u-cell style="background-color: transparent;" :value="value" :border="false" v-model="value" :isLink="true"
			@click="show=true">
			<view slot="title" class="u-slot-title">
				<text class="title">{{title}}</text>
			</view>
		</u-cell>

	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => "请选择"
			},
			show: {
				type: <PERSON><PERSON><PERSON>,
				default () {
					false
				}
			},


		},


		methods: {
			confirm(e) {
				// console.log("confirmChoiceDate" + e[0]);
				this.value = e[0];
				this.$emit('update:msg', e[0])
				this.show = false;
			},

			close() {
				this.show = false
			},
		}
	}
</script>

<style lang="scss" scoped>


</style>