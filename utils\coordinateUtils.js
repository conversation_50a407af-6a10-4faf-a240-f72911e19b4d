/**
 * 坐标转换工具
 * WGS84坐标系转换为高德地图坐标系(GCJ02)
 */

// WGS84坐标系参数
const a = 6378245.0; // 长半轴
const ee = 0.00669342162296594323; // 偏心率平方

/**
 * 判断是否在中国大陆范围内
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {boolean}
 */
function outOfChina(lng, lat) {
  return (lng < 72.004 || lng > 137.8347) || (lat < 0.8293 || lat > 55.8271);
}

/**
 * 转换纬度
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number}
 */
function transformLat(lng, lat) {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
  ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
  return ret;
}

/**
 * 转换经度
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number}
 */
function transformLng(lng, lat) {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0;
  return ret;
}

/**
 * WGS84坐标转换为GCJ02坐标(高德地图坐标系)
 * @param {number} lng WGS84经度
 * @param {number} lat WGS84纬度
 * @returns {object} {lng: GCJ02经度, lat: GCJ02纬度}
 */
export function wgs84ToGcj02(lng, lat) {
  if (outOfChina(lng, lat)) {
    return { lng, lat };
  }
  
  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  
  const radlat = lat / 180.0 * Math.PI;
  let magic = Math.sin(radlat);
  magic = 1 - ee * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  
  dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * Math.PI);
  dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * Math.PI);
  
  const mglat = lat + dlat;
  const mglng = lng + dlng;
  
  return {
    lng: mglng,
    lat: mglat
  };
}

/**
 * 批量转换坐标点
 * @param {Array} points 坐标点数组 [{lng, lat}, ...]
 * @returns {Array} 转换后的坐标点数组
 */
export function batchWgs84ToGcj02(points) {
  return points.map(point => wgs84ToGcj02(point.lng, point.lat));
}

/**
 * 转换地图标记点坐标
 * @param {Array} markers 标记点数组
 * @returns {Array} 转换后的标记点数组
 */
export function convertMarkersCoordinates(markers) {
  return markers.map(marker => {
    if (marker.longitude && marker.latitude) {
      const converted = wgs84ToGcj02(marker.longitude, marker.latitude);
      return {
        ...marker,
        longitude: converted.lng,
        latitude: converted.lat
      };
    }
    return marker;
  });
}

/**
 * 转换地图中心点坐标
 * @param {object} center 中心点对象 {longitude, latitude}
 * @returns {object} 转换后的中心点对象
 */
export function convertCenterCoordinates(center) {
  if (center.longitude && center.latitude) {
    const converted = wgs84ToGcj02(center.longitude, center.latitude);
    return {
      ...center,
      longitude: converted.lng,
      latitude: converted.lat
    };
  }
  return center;
}