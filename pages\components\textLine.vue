<template>
	<view>

		<view class="line"  style="margin-right: 25rpx;">
			<text class="title">{{title}}</text>
			<text class="info"  v-model="value">{{value}}</text>
			<!-- -->

		</view>
		
	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => ""
			},
			
		},
		
		methods: {


		}
	}
</script>

<style lang="scss" scoped>

</style>