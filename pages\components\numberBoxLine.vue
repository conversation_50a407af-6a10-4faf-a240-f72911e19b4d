<template>

	<!-- style="margin-left: 30rpx;margin-right: 30rpx;" -->
	<view class="line">
		<text class="title">{{title}}</text>
		<u-number-box class="info" input-align="right" :step="step" :value.sync="value"  :v-model="value"
			@change="valChange" :min="min" :max="max"></u-number-box>
		<!-- -->

	</view>


</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: Number,
				default: () => ""
			},
			step: {
				type: Number,
				default: () => 1
			},
			max: {
				type: Number,
				default: () => 0
			},
			min: {
				type: Number,
				default: () => 0
			},

		},
	
		methods: {

			valChange(e) {
				console.log('当前值为: ' + e.value)
				this.$emit('update:value', e.value);
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>