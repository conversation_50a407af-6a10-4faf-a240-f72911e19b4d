<!DOCTYPE html>
<html lang="zh-cn" contentType="text/html; charset=gb2312" language="java" import="java.sql.*" errorPage="" %>
	<!-- uni 的 SDK，必须引用。 -->
	<meta charset="utf-8"><!-- 	解决中文乱码 -->
	<body>
		<!-- uni 的 SDK -->
		<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.1.js">
		</script>
		<script type="text/javascript" src="js/h5uploader.js"></script>
		<script type="text/javascript">
			// 当前环境
		uni.getEnv(function(res) {
					console.log('当前环境：' + JSON.stringify(res));
				});

				function changeAgentContent() {
					console.log('changeAgentContent：')
					document.getElementById("myfile").value = document.getElementById("myfile").value;
					console.log('changeAgentContent2：' + document.getElementById("myfile").value);

					sendMessage()

				}
				// var uploader = document.getElementById('uploader');
				// 发送文件数据
				function sendMessage() {
					console.log('sendMessage：')
					var path = document.getElementById("myfile").value
					console.log("send:" + path)
					// uni.postMessage({
					// 	data: {
					// 		action: data
					// 	}
					// });

					window.parent.postMessage({
						path,
					}, '*');



					// 关闭所有页面，打开到应用内的某个页面,url传递文件数据,表单页面通过reload接收数据,并在提交时传递
					// uni.reLaunch({
					// 	url: './h5Upload?fileData=' + data,
					// })
					// 关闭当前页面，跳转到应用内的某个页面
					// uni.redirectTo({
					// 	url: './h5Upload?fileData=' + data,
					// })
					// 返回上一级页面,通过@message接收数据,并放到store中,以便提交时获取文件数据
					// uni.navigateBack({
					//     delta: 1
					// });
				}
				// uploader.addEventListener("click", function(e) {
				// 	// 上传完成,发送文件数据到应用中
				// 	sendMessage()
				// H5Uploader.upload({
				// 	// 上传url
				// 	action: 'upload',
				// 	id: 'myfile',
				// 	size: {
				// 		max: 50000, // 5000kb 
				// 		valide: function(e) {
				// 			if (e) {
				// 				alert("The size of " + e.name + " is exceed max value!");
				// 			}
				// 		}
				// 	}, // MB
				// 	type: {
				// 		name: 'csv;png;jpg;jpeg',
				// 		valide: function(e) {
				// 			if (e) {
				// 				alert("The type of " + e.name + " is not supported!");
				// 			}
				// 		}
				// 	},
				// 	progress: function() {
				// 		var p = document.createElement('p');
				// 		p.innerHTML = "uploading";
				// 		p.id = "loading";
				// 		document.body.appendChild(p);
				// 	},
				// 	success: function(data) {
				// 		alert(data);
				// 		if (data && data == 200) {
				// 			document.getElementById('loading').innerHTML = "The file upload successfully!";
				// 			alert("The file upload successfully.");
				// 		}
				// 	},
				// 	fail: function(data) {}
				// });
				// }, false);
			
		</script>

		<!-- 		<h5 style="wrap">H5附件上传</h5> -->
		<!-- 		<input type="button" onclick="document.getElementById('myfile').click()" value="选择文件" /> -->
		<button for="myfile" onclick="document.getElementById('myfile').click()" class="myfile"
			style="width: 95%;height: 50px;background-color: #3C9CFF;color: #fff;border: 0ch;border-radius: 5px;">选择文件</button>
		<input type="file" id="myfile" name="myfile" onchange="changeAgentContent()" />

		<!-- hidden -->


	</body>

</html>