<template>
    <view class="container">
        <!-- 顶部导航栏 -->
        <view class="header">
            <view class="nav-bar">
                <uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
                <text class="title">隐患上报处理</text>
                <uni-icons :type="displayMap ? 'list' : 'map'" size="20" color="#fff"
                    @click="displayMap = !displayMap"></uni-icons>
            </view>
        </view>
        <view class="search-bar"></view>
        <!-- Tab栏 -->
        <view class="tab-container">
            <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }"
                @click="switchTab(index)">
                <text class="tab-text">{{ tab.name }}</text>
            </view>
        </view>

        <!-- 地图模式 -->
        <view v-if="displayMap" class="map-container">
            <map v-if="mapReady" id="dangerMap" class="map" :longitude="mapCenter.longitude"
                :latitude="mapCenter.latitude" :scale="mapScale" :markers="mapMarkers" @markertap="onMarkerTap"
                @regionchange="onRegionChange" show-location enable-3D enable-overlooking enable-zoom enable-scroll
                enable-rotate>
            </map>

            <view v-else class="map-loading">
                <text>地图加载中...</text>
            </view>

            <!-- 地图上的信息面板 -->
            <view v-if="selectedMarker" class="map-info-panel">
                <view class="info-header">
                    <text class="info-title">{{ selectedMarker.title }}</text>
                    <uni-icons type="close" size="16" color="#666" @click="closeInfoPanel"></uni-icons>
                </view>
                <view class="info-content">
                    <view class="info-item">
                        <text class="info-label">类型：</text>
                        <text class="info-value">{{ selectedMarker.type }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">状态：</text>
                        <text class="info-value" :class="getStatusClass(selectedMarker.status)">{{ selectedMarker.status
                        }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">位置：</text>
                        <text class="info-value">{{ selectedMarker.location }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">处理人：</text>
                        <text class="info-value">{{ selectedMarker.handler }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">时间：</text>
                        <text class="info-value">{{ selectedMarker.time }}</text>
                    </view>
                </view>
                <view class="info-actions">
                    <button class="action-btn" @click="viewDetail(selectedMarker)">查看详情</button>
                </view>
            </view>
        </view>

        <!-- 列表模式 -->
        <scroll-view v-else class="content" scroll-y="true">
            <view class="danger-list">
                <view v-for="(item, index) in currentList" :key="index" class="danger-item" @click="viewDetail(item)">
                    <view class="danger-header">
                        <view class="danger-type">
                            <text class="type-label">【{{ item.type }}】</text>
                            <text class="danger-title">{{ item.title }}</text>
                        </view>
                    </view>
                    <view class="danger-status">
                        <view class="status-dot" :class="getStatusClass(item.status)"></view>
                        <text class="status-text">{{ item.status }}</text>
                    </view>
                    <view class="danger-location">
                        <text class="location-icon">📍</text>
                        <text class="location-text">{{ item.location }}</text>
                    </view>
                    <view class="danger-footer">
                        <text class="handler">{{ item.handler }}</text>
                        <text class="time">{{ item.time }}</text>
                    </view>
                </view>
            </view>

            <!-- 无数据提示 -->
            <view v-if="currentList.length === 0" class="no-data">
                <text class="no-data-text">暂无数据</text>
            </view>
        </scroll-view>

        <!-- 底部上报按钮 -->
        <view class="bottom-button">
            <text class="report-btn" @click="reportDanger">上报隐患</text>
        </view>
    </view>
</template>

<script>
import { wgs84ToGcj02, convertMarkersCoordinates, convertCenterCoordinates } from '@/utils/coordinateUtils.js';

export default {
    data() {
        return {
            displayMap: false,
            mapReady: false, // 添加地图准备状态
            activeTab: 0,
            tabs: [
                { name: '待处理', key: 'pending' },
                { name: '我的上报', key: 'myReport' },
                { name: '已关闭', key: 'closed' }
            ],
            // 地图相关数据
            mapCenter: {
                longitude: 112.516,
                latitude: 37.6132
            },
            mapScale: 13,
            selectedMarker: null,
            // 模拟数据（添加经纬度坐标）
            pendingList: [
                {
                    id: 1,
                    type: '管线',
                    title: '电缆基座与管线间距不足',
                    status: '待处理',
                    location: '凤飞路与大井路口',
                    handler: '李一一',
                    time: '2025-05-06 13:03',
                    longitude: 112.516,
                    latitude: 37.6132
                },
                {
                    id: 2,
                    type: '检测',
                    title: '污水检测',
                    status: '待处理',
                    location: '福飞路超越时代门口',
                    handler: '李一一',
                    time: '2025-05-06 13:03',
                    longitude: 112.53,
                    latitude: 37.6011
                },
                {
                    id: 3,
                    type: '检测',
                    title: '数字峰会期间周边检查',
                    status: '待处理',
                    location: '香格里拉酒店',
                    handler: '李一一',
                    time: '2025-05-06 13:03',
                    longitude: 112.548,
                    latitude: 37.601
                },
                {
                    id: 4,
                    type: '检测',
                    title: '数字峰会期间周边检查',
                    status: '处理中',
                    location: '香格里拉酒店',
                    handler: '李一一',
                    time: '2025-05-06 13:03',
                    longitude: 112.558,
                    latitude: 37.6103
                },
                {
                    id: 5,
                    type: '检测',
                    title: '数字峰会期间周边检查',
                    status: '已完成',
                    location: '香格里拉酒店',
                    handler: '李一一',
                    time: '2025-05-06 13:03',
                    longitude: 112.534,
                    latitude: 37.6308
                }
            ],
            myReportList: [],
            closedList: []
        };
    },
    computed: {
        currentList() {
            switch (this.activeTab) {
                case 0:
                    return this.pendingList;
                case 1:
                    return this.myReportList;
                case 2:
                    return this.closedList;
                default:
                    return [];
            }
        },
        // 地图标记点
        mapMarkers() {
            return this.currentList.map(item => ({
                id: item.id,
                longitude: item.longitude,
                latitude: item.latitude,
                title: item.title,
                iconPath: this.getMarkerIcon(item.status),
                width: 30,
                height: 30,
                callout: {
                    content: item.title,
                    color: '#333',
                    fontSize: 12,
                    borderRadius: 4,
                    bgColor: '#fff',
                    padding: 8,
                    display: 'BYCLICK'
                },
                // 保存完整的数据信息
                ...item
            }));
        }
    },
    onLoad() {
        // 页面加载时获取数据
        this.loadData();
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 切换Tab
        switchTab(index) {
            this.activeTab = index;
            this.selectedMarker = null; // 切换Tab时清除选中的标记
            this.loadData();

            // 如果切换到地图模式，延迟初始化地图
            if (this.displayMap) {
                this.$nextTick(() => {
                    this.initMap();
                });
            }
        },

        // 添加地图初始化方法
        initMap() {
            // 确保地图容器已渲染
            setTimeout(() => {
                this.mapReady = true;
            }, 100);
        },

        // 获取状态样式类
        getStatusClass(status) {
            switch (status) {
                case '待处理':
                    return 'status-pending';
                case '处理中':
                    return 'status-processing';
                case '已完成':
                    return 'status-completed';
                default:
                    return 'status-pending';
            }
        },

        // 获取标记图标
        getMarkerIcon(status) {
            switch (status) {
                case '待处理':
                    return '/static/icons/marker-error.png';
                case '处理中':
                    return '/static/icons/marker-warning.png';
                case '已完成':
                    return '/static/icons/marker-normal.png';
                default:
                    return '/static/icons/marker-error.png';
            }
        },

        // 地图标记点击事件
        onMarkerTap(e) {
            const markerId = e.detail.markerId;
            const marker = this.currentList.find(item => item.id === markerId);
            if (marker) {
                this.selectedMarker = marker;
            }
        },

        // 地图区域变化事件
        onRegionChange(e) {
            if (e.type === 'end') {
                this.mapCenter = {
                    longitude: e.detail.centerLocation.longitude,
                    latitude: e.detail.centerLocation.latitude
                };
            }
        },

        // 关闭信息面板
        closeInfoPanel() {
            this.selectedMarker = null;
        },

        // 查看详情
        viewDetail(item) {
            // 将隐患数据传递给详情页面
            const data = encodeURIComponent(JSON.stringify(item));
            uni.navigateTo({
                url: `/pages/water/danger-report/detail?id=${item.id}&data=${data}`
            });
        },

        // 上报隐患
        reportDanger() {
            uni.navigateTo({
                url: '/pages/water/danger-report/report'
            });
        },

        // 加载数据
        loadData() {
            // 这里可以根据activeTab加载不同的数据
            console.log('加载数据，当前Tab:', this.tabs[this.activeTab].key);
        }
    },

    // 添加监听器
    watch: {
        displayMap(newVal) {
            if (newVal) {
                this.$nextTick(() => {
                    this.initMap();
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;

    .header {
        background: #1890ff;
        padding-top: var(--status-bar-height);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;

        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;

            .title {
                width: 100%;
                color: #fff;
                font-size: 18px;
                font-weight: 500;
                text-align: center;
            }
        }
    }
}

/* Tab栏固定 */
.tab-container {
    background-color: #fff;
    display: flex;
    height: 50px;
    border-bottom: 1px solid #e8e8e8;
    position: fixed;
    top: calc(var(--status-bar-height) + 44px);
    left: 0;
    right: 0;
    z-index: 999;
}

.tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.tab-item.active {
    color: #1890ff;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: #1890ff;
    border-radius: 2px;
}

.tab-text {
    font-size: 16px;
    color: #666;
}

.tab-item.active .tab-text {
    color: #1890ff;
    font-weight: bold;
}

/* 地图容器 */
.map-container {
    position: absolute;
    top: calc(var(--status-bar-height) + 44px + 50px);
    left: 0;
    right: 0;
    bottom: 53px;
    flex: 1;
    position: relative;
    margin-top: calc(var(--status-bar-height) + 44px + 50px);
    margin-bottom: 53px;

    .map {
        width: 100%;
        height: 100%;
        min-height: 300px;
        /* 添加最小高度 */
    }
}

/* 地图信息面板 */
.map-info-panel {
    position: absolute;
    bottom: 20px;
    left: 16px;
    right: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 100;

    .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        .info-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
    }

    .info-content {
        padding: 16px;

        .info-item {
            display: flex;
            margin-bottom: 8px;

            .info-label {
                font-size: 14px;
                color: #666;
                width: 60px;
                flex-shrink: 0;
            }

            .info-value {
                font-size: 14px;
                color: #333;
                flex: 1;

                &.status-pending {
                    color: #1890ff;
                }

                &.status-processing {
                    color: #faad14;
                }

                &.status-completed {
                    color: #52c41a;
                }
            }
        }
    }

    .info-actions {
        padding: 16px;
        border-top: 1px solid #f0f0f0;

        .action-btn {
            width: 100%;
            height: 36px;
            background: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 14px;
        }
    }
}

/* 地图加载提示 */
.map-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: #f5f5f5;

    text {
        color: #666;
        font-size: 16px;
    }
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 0 15px;
    overflow-y: auto;
    box-sizing: border-box;
    margin-top: calc(var(--status-bar-height) + 44px + 50px);
    margin-bottom: 80px;
}

.danger-list {
    padding: 15px 0;
}

.danger-item {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.danger-header {
    margin-bottom: 10px;
}

.danger-type {
    display: flex;
    align-items: center;
}

.type-label {
    font-size: 14px;
    color: #1890ff;
    font-weight: bold;
    margin-right: 5px;
}

.danger-title {
    font-size: 14px;
    color: #333;
}

.danger-status {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-pending {
    background-color: #1890ff;
}

.status-processing {
    background-color: #faad14;
}

.status-completed {
    background-color: #52c41a;
}

.status-text {
    font-size: 14px;
    color: #1890ff;
}

.danger-location {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.location-icon {
    font-size: 14px;
    margin-right: 5px;
}

.location-text {
    font-size: 14px;
    color: #666;
}

.danger-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.handler {
    font-size: 14px;
    color: #333;
}

.time {
    font-size: 12px;
    color: #999;
}

/* 无数据提示 */
.no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.no-data-text {
    font-size: 16px;
    color: #999;
}

/* 底部按钮固定 */
.bottom-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0;
    padding: 16px;
    background: #1890ff;
    border-radius: 0;
    text-align: center;
    transition: background-color 0.3s;
    z-index: 999;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

    .report-btn {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
    }
}

.report-btn:active {
    opacity: 0.8;
}
</style>