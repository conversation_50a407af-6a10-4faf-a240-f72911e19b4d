// 高德地图配置文件
export default {
    // 高德地图Web服务API密钥
    // 请在高德开放平台 (https://lbs.amap.com/) 申请Web服务API密钥
    webServiceKey: '0dcbc761d88f10427bc28ecde70478ee',

    // 高德地图JavaScript API密钥（如果需要）
    jsApiKey: '0dcbc761d88f10427bc28ecde70478ee',

    // API配置
    apiConfig: {
        // 步行路线规划API
        walkingRoute: {
            baseUrl: 'https://restapi.amap.com/v3/direction/walking',
            timeout: 10000,
            extensions: 'all' // 返回详细路线信息
        },

        // 驾车路线规划API
        drivingRoute: {
            baseUrl: 'https://restapi.amap.com/v3/direction/driving',
            timeout: 10000,
            extensions: 'all'
        },

        // 公交路线规划API
        transitRoute: {
            baseUrl: 'https://restapi.amap.com/v3/direction/transit/integrated',
            timeout: 15000,
            extensions: 'all'
        }
    },

    // 地图样式配置
    mapStyle: {
        polyline: {
            walking: {
                color: '#1890ff',
                width: 6,
                borderColor: '#ffffff',
                borderWidth: 2,
                arrowLine: true,
                dottedLine: false
            },
            driving: {
                color: '#52c41a',
                width: 8,
                borderColor: '#ffffff',
                borderWidth: 2,
                arrowLine: true,
                dottedLine: false
            },
            fallback: {
                color: '#ff4d4f',
                width: 4,
                borderColor: '#ffffff',
                borderWidth: 1,
                arrowLine: true,
                dottedLine: true
            }
        }
    }
};
