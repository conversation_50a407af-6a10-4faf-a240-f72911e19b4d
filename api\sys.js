// 系统信息相关接口返回处理
// 1 格栅类设备
// 2 备件
// 3 用途
// 4 设备制度说明
// 5 状态（库存、正常、维修、报废、采购中）
// 6 设备状态（正常、停用、维修）
// 7 故障类型
// 8 维修事务分类
// 9 生产商
// 10 流程状态
// 11 设备类型（设备、自控、安防、办公、设施）
// 12 手段
// 13 水厂地点
import {
	request
} from './server.js'

var pre = '/potevio-device'
var preSys = '/potevio-system'
// var pre = ''

//获取用户信息
// export const getUserInfo =
// 	function() {
// 		uni.setStorageSync('isSys', true);
// 		var url = pre + '/sys-user-extend/' + getApp().globalData.username;
// 		console.log(url);
// 		return request.get(url)
// 			.then(data => {
// 				console.log('orderStates back');
// 				uni.setStorageSync('isSys', false);
// 				return data.data
// 			})
// 	}
export const getUserInfo =
	function(params) {
		return request.get('/auth/user/info', params)
			.then(data => {
				return data.data
			})
	}
//工单状态
export const orderStates =
	function() {
		uni.setStorageSync('isSys', true);
		// var url = '/potevio-device/sys-meta-type-extend/10';
		var url = preSys+'/sys-meta-type-extend/10';

		console.log(url);
		return request.get(url)
			.then(data => {
				console.log('orderStates back');
				uni.setStorageSync('isSys', false);
				return data.data
			})
	}
//备件
export const spareParts =
	function() {
		console.log("spareParts:");
		uni.setStorageSync('isDevice', true);
		uni.setStorageSync('isSys', false);
		// var url = '/potevio-device/sys-meta-type-extend/2';
		var url = pre + '/device-main-extend/?deviceSysType=2&deviceCategory=&pageNum=1&pageSize=5000';
		console.log(url);
		return request.get(url)
			.then(data => {
				// console.log('spareParts back');
				uni.setStorageSync('isDevice', false);
				return data.data
			})
	}
// export const spareParts =
// 	function() {

// 		uni.setStorageSync('isSys', true);
// 		// var url = '/potevio-device/sys-meta-type-extend/2';
// 		var url = pre + '/sys-meta-type-extend/2';
// 		console.log(url);
// 		return request.get(url)
// 			.then(data => {
// 				console.log('spareParts back');
// 				uni.setStorageSync('isSys', false);
// 				return data.data
// 			})
// 	}
//故障类型
export const faultType =
	function() {
		uni.setStorageSync('isSys', true);
		// var url = '/potevio-device/sys-meta-type-extend/121';
		var url = preSys + '/sys-meta-type-extend/121';
		console.log(url);
		return request.get(url)
			.then(data => {
				console.log('faultType back');
				uni.setStorageSync('isSys', false);
				return data.data
			})
	}
//维修手段
export const repairMeasure =
	function() {
		uni.setStorageSync('isSys', true);
		var url = preSys + '/sys-meta-type-extend/12';
		// var url = '/device/sys-meta-type-extend/12';
		console.log(url);
		return request.get(url)
			.then(data => {
				console.log('repairMeasure back');
				uni.setStorageSync('isSys', false);
				return data.data
			})
	}

//备件仓库
export const getWarehouseDict =
	function(sparePartId) {
		uni.setStorageSync('isDevice', true);
		var url = pre + '/device-main-extend/' + sparePartId;
		// var url = '/potevio-device/sys-meta-type-extend/12';
		// console.log(url);
		return request.get(url)
			.then(data => {
				console.log('getWarehouseDict back');
				uni.setStorageSync('isDevice', false);
				return data.data
			})
	}


//用户角色
export const getRoleChoice =
	function(pdKey, taskKey) {
		uni.setStorageSync('isJBPM', true);
		// var url = '/jbpm' + '/processDefinition/candidate-groups?pdKey='+pdKey+'&taskKey=' + taskKey;弃用了
		var url = '/jbpm' + '/processDefinition/candidate-groups/next?pdKey='+pdKey+'&taskKey=' + taskKey;
		
		// console.log(url);
		return request.get(url)
			.then(data => {
				// console.log('getRoleChoice back');
				uni.setStorageSync('isJBPM', false);
				return data.data
			})
	}

//用户角色保养暂时用这个获取角色
export const getRoleChoiceTempMaintain =
	function(pdKey, taskKey) {
		uni.setStorageSync('isJBPM', true);
		var url = '/jbpm' + '/processDefinition/candidate-groups/next?pdKey='+pdKey+'&taskKey=' + taskKey;
		// console.log(url);
		return request.get(url)
			.then(data => {
				// console.log('getRoleChoice back');
				uni.setStorageSync('isJBPM', false);
				return data.data
			})
	}

export const getSysRoleId =
	function(params) {
		var url = preSys + '/sys-role/list'
		// console.log(url);
		// console.log("roleValueList" + JSON.stringify(params));
		return request.post(url, params)
			.then(data => {
				// console.log('getSysRoleId back');
				return data.data
			})
	}
//获取角色对应用户
export const getRoleUser =
	function(id) {
		var url = preSys + '/sys-role/' + id + "/users"
		console.log(url);

		return request.get(url)
			.then(data => {
				// console.log('getRoleUser back');
				return data.data
			})
	}
//获取部门树
export const getSysOrgTree =
	function() {
		var url = preSys + '/sys-org/tree'
		console.log(url);

		return request.get(url)
			.then(data => {
				// console.log('getSysOrgTree获取部门树 back');
				return data.data
			})
	}
//获取用户id
export const getUserId =
	function() {
		var url = preSys + '/auth/user/id'
		console.log(url);

		return request.get(url)
			.then(data => {
				console.log('获取用户 back');
				return data.data
			})
	}