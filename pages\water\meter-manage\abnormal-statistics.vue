<!-- 异常统计 -->
<template>
	<view class="meter-main">
		<image src="/static/img/event-bg.png" class="meter-bg" mode="widthFix"></image>
		<view class="content-box">
			<view class="top-box">
				<u-search :showAction="false" bgColor="rgba(243,246,252,0.63)" placeholder="搜索"
					v-model="keyword"></u-search>
					<div class="tjbox">
						<view class="sort1">
							<view class="">
								待办
							</view>
							<view class="active">
								全部
							</view>
						</view>
					<!-- 	<div class="sort">
							<text style="margin-right: 20rpx;">综合排序</text>
							<u-icon name="arrow-down-fill"></u-icon>
						</div> -->
					</div>
				
			</view>

			<view class="table-box">
				<scroll-view scroll-y="true" style="height: 78vh;" class="scroll-box">
					<u-empty v-if="listData.length==0"
					        mode="data"
					        icon="http://cdn.uviewui.com/uview/empty/data.png"
					>
					</u-empty>
					<view class="lists">
						<div class="list" v-for="(item,index) in listData" :key="index">
							<div class="list-left">
								<image style="width: 110rpx;" src="/static/img/tg-1.png" mode="widthFix"></image>
							</div>
							<div class="list-right">
								<label for="">{{item.examDesc}}</label>
								<text>时间：{{item.examTime}} </text>
							</div>
							<div class="btn" v-if="reviewStatus==0">待办</div>
							<div class="btn btn1" v-if="reviewStatus==1">已办</div>
						</div>
					<!-- 	<div class="list">
							<div class="list-left">
								<image style="width: 110rpx;" src="/static/img/tg-1.png" mode="widthFix"></image>
							</div>
							<div class="list-right">
								<label for="">异常情况描述,异常情况描述异常情况
									异常情况描述异常情况描述...</label>
								<text>时间：2023-10-28 </text>
							</div>
							
						</div> -->

					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>
<script>
	import Navbar from './navbar/Navbar.vue'
	import * as EventApi from '@/api/meter-manage/event.js'
	export default {
		components: {
			Navbar,
		},
		data() {
			return {
				keyword: "",
				listData: [

				]
			};
		},
		onLoad(options) {
			console.log(options)
			const queryString = options.item; // options.data是从url参数中获取的数据
			const item = JSON.parse(decodeURIComponent(queryString));
			console.log(item)
			// this.getList(item)
		},
		methods: {
			navTo(url, item) {
				const queryString = encodeURIComponent(JSON.stringify(item));
				uni.navigateTo({
					url: url + queryString
				})
			},
			// 获取册本列表
			getList(item) {
				const app = this
				EventApi.getDataAbnormalList({
					meterVolumesNum: item.meterVolumesNum,
					dutyUser: app.$storage.get('user').userId,
				}).then(res => {
					console.log(res)
					// res.data.
					app.listData = res.rows

				})
			},
		},
	};
</script>
<style lang="scss" scoped>
	.meter-main {
		overflow-y: hidden;
		height: 100vh;
		background-color: #EFF3F9;

		.meter-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			z-index: 40;
		}

		.content-box {
			position: relative;
			z-index: 42;


			.top-box {
				padding: 24rpx;
				height: 286rpx;
				box-sizing: border-box;
				.tjbox{
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 60rpx;
					.sort1 {
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 24rpx;
						font-weight: 400;
						color: #616E83;
						width: 242rpx;
						height: 68rpx;
						background: #FFFFFF;
						box-shadow: 5rpx 3rpx 12rpx 0rpx rgba(156, 176, 221, 0.34);
						border-radius: 34rpx;
					
						view {
							display: flex;
							align-items: center;
							justify-content: center;
							width: 112rpx;
							height: 52rpx;
						}
					
						.active {
					
							width: 112rpx;
							height: 52rpx;
							background: #E2EBFA;
							border-radius: 26rpx;
							color: #1172FD;
						}
					}
				}

				.sort {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 188rpx;
					height: 56rpx;
					background: #FFFFFF;
					opacity: 0.66;
					border-radius: 8rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #2F3849;
					line-height: 30rpx;
					
				}
			}

			.table-box {
				position: relative;


				width: 100%;

				.scroll-box {
					position: absolute;
					background-color: #EFF3F9;
					top: -10px;
					left: 0;
					z-index: 77;
					padding: 0 22rpx;
					//
					box-sizing: border-box;
				}

				.lists {
					min-height: 100%;

					box-shadow: 0rpx 6rpx 9rpx 1rpx rgba(107, 146, 214, 0.26);
					border-radius: 16rpx 16rpx 0rpx 0rpx;
					box-sizing: border-box;

					.list {
						position: relative;
						width: 100%;
						padding: 20rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 0rpx 12rpx 0rpx rgba(182, 195, 203, 0.15);
						border-radius: 30rpx;
						margin-bottom: 16rpx;
						display: flex;
						align-items: flex-start;
						box-sizing: border-box;

						.list-left {
							width: 110rpx;
							margin-right: 20rpx;
						}

						.list-right {
							display: flex;
							align-items: flex-start;
							flex-direction: column;
							width: 440rpx;

							label {
								font-size: 28rpx;
								font-weight: 400;
								color: #3E3E52;
								line-height: 1.5;
							}

							text {
								font-size: 24rpx;

								font-weight: 400;
								color: #AAAFBC;
							}
						}

						&:nth-child(1) {
							margin-top: 0;
						}

						&:last-child {
							border-bottom: none;
						}

						.btn {
							position: absolute;
							display: flex;
							align-items: center;
							justify-content: center;
							right: 30rpx;
							bottom: 20rpx;
							width: 130rpx;
							height: 56rpx;
							background: #E5F8F0;
							border: 2px solid #A6E7CC;
							border-radius: 28rpx;
							font-size: 26rpx;
							font-weight: 400;
							color: #1BC790;
						}

						.btn1 {
							background: #EAF2FF;
							border: 2px solid #E4EBF7;

							color: #1172FD;
						}
					}
				}


			}
		}
	}
</style>