<template>
	<!-- 
				// test_ss_ro_sc,测试-狮山维修自控专员
				// test_ss_ro_meter,测试-狮山维修仪表专员
				// test_ss_ro_vs,测试-狮山视频安防专员
				// test_ss_ro_fac,测试-狮山设施专员
				// test_ss_ro_office,测试-狮山维修办公专员
				// test_ro_third,测试-维修委外人员
				// test_ss_dep_header,测试-狮山厂子部门负责人'       
				// 狮山维修工单发起人: test_ss_ro_creator
				// 厂长审核:test_ss_header
				// 指派工单: xj--技保中心负责人:test_tec_header
				// 狮山维修设备专员test_ss_ro_dev
				// 工单发起人确认:test_ss_ro_creator (委外)
				// 水厂确认:test_ss_header
				// 专员确认-谁执行了工单谁确认:test_ro_third (委外)
				// 职能部门审核: test_ss_dep_header
				// 职能部门确认:test_ss_dep_header
				// 办公设备指派: test_ss_ro_office
				// 技障中心确认test_tec_header
				// test_ss_mo_sp 测试-狮山保养专员A
				// test_tec_meter_sp 测试-仪表专员A
				// test_tec_device_sp测试-设备专员A

				//密码都是 gci@000000 -->
	<view class="wrap">
		<u-loading-page :loading="loading"></u-loading-page>
		<div class="img">
			<text class="welcome">欢迎登录</text>
			<text class="app_name">山西综改区智慧水务</text>

		</div>
		<view class="panel">
			<view class="content">

				<view>

					<!--账号-->
					<view class="block_line" style="margin-top:80rpx;">
						<text
							style="margin-bottom: 8px; width:60rpx; font-size: 14px; color:#B3B3B3; 	white-space: nowrap;">用户名</text>

						<input class="input" v-model="username" del="password" placeholder="请输入账号" clearable />


					</view>


					<view class="login_parting_line_hori"></view>

					<!--密码-->
					<view class="block_line" style="margin-top: 40rpx;">
						<text
							style="margin-bottom: 8px; width:60rpx; font-size: 14px; color: #B3B3B3; 	white-space: nowrap;">密码</text>
						<!-- style="margin-top: 10px; width:60rpx; font-size: 14px; color: #606266; 	white-space: nowrap;" -->
						<view class="block_pwd">
							<input style="width: 80%;" :password="isPassword" v-model="password" del="password"
								placeholder="请输入密码" clearable />
							<u-icon name="eye" size="20" v-if="isPassword" @click="changeShowPwd"
								style="margin-bottom: 10px;" />
							<u-icon name="eye-fill" size="20" v-if="!isPassword" @click="changeShowPwd"
								style="margin-bottom: 10px;" />

						</view>
					</view>

					<view class="login_parting_line_hori"></view>
					<!--验证码-->
					<view class="block_capture" v-if="isShow">
						<!-- -->
						<view class="block_line">
							<text
								style="margin-top: 10px; width:60rpx; font-size: 14px; color: #606266; 	white-space: nowrap;">验证码</text>
							<!-- :style="{height:inputHeight}" -->
							<view class="block_pwd">
								<input style=" height: 5%;" clearable placeholder="请输入验证码" v-model="inputCode" />

								<view class="code-img-wrapper" @click="updateImageCode">
									<canvas canvas-id="canvas" background="#ffffff "></canvas>
								</view>
							</view>
						</view>
					</view>
					<view class="login_parting_line_hori" style="width: 90%; margin-top: 30rpx; " v-if="isShow"></view>
					<u-checkbox-group class="cb" v-model="checkboxValue">
						<u-checkbox activeColor="#0D72BA" @change="rmdPwd" v-for="(item, index) in checkboxList1"
							:key="index" :label="item.name" :name="item.name" v-model="isRemember"></u-checkbox>
						<!-- -->
					</u-checkbox-group>

					<!-- 登录按钮 -->
					<u-button style="margin-top: 68rpx; background:#0D72BA;  color:white;" @tap="submit"
						shape="circle" :loading="loading" loading-text="登录中">登录
					</u-button>
					<!-- <view class="text_line">
						当前软件版本:V1.0.3</view>
					<view class="text_line" v-show="isShowUrl">
						服务地址:{{ currproxy }}
					</view> -->


				</view>
			</view>
		</view>
	</view>
</template>

<script>
import md5 from '../../../js_sdk/js-md5/src/md5.js' //引入
import {
	Mcaptcha
} from '../../../utils/mcapture.js'
import {
	login,
	getCode
} from "../../../api/login.js"
import {
	auth, proxy
} from '../../../api/config.js'
export default { //数据绑定
	data() {
		return {
			loading: false,
			dataList: [{
				id: 1,
				name: "star"
			}, {
				id: 2,
				name: "star"
			}, {
				id: 3,
				name: "star"
			}],
			inputHeight: '',
			username: '',
			imgSrc: '',
			password: '',
			isRemember: true, //密码是否记住  				
			isPassword: true, //密码是否显示  				
			code: '', //验证码
			inputCode: '', //输入的验证码
			codeTag: '', //验证码Tag
			// type: "password",
			res: "",
			title: "登录",
			userArea: "", //测试地区数据获取
			currproxy: "",
			isShow: false, //是否显示验证码
			isShowUrl: true,

			checkboxList1: [{
				name: '记住密码',
				disabled: false
			},],
			checkboxValue: [""],

		}
	},

	onLoad() {
		console.log("action onload")
		// #ifdef APP-PLUS
		this.isIos = (plus.os.name == "iOS")
		// #endif
	}, //设置页面全屏
	onShow() {
		// #ifdef APP-PLUS
		plus.navigator.setFullscreen(true); //隐藏手机顶部状态栏
		plus.navigator.hideSystemNavigation(); //隐藏手机底部导航按键
		// #endif
	}, //监听页面卸载事件 如果不加这句，会导致跳转到别的页面后也是全屏
	onUnload() {
		// #ifdef APP-PLUS
		plus.navigator.setFullscreen(false); //显示手机顶部状态栏
		plus.navigator.showSystemNavigation(); //显示手机底部导航按键
		// #endif
	},
	onReady() {
		// uni.setStorageSync('isSys', true);
		getApp().globalData.username = uni.getStorageSync('username') || ""
		this.username = getApp().globalData.username
		console.log("this.username =" + this.username);
		// if (this.username == null || this.username == "") { //测试用
		// 	if (proxy == "http://10.1.74.53:18888" || proxy == "http://dev.microservice.chinagci.com/suzhou") { //测试库
		// 		// this.username = 'test_ss_mo_sp';
		// 		// this.username = 'repair_r_t_a';
		// 		this.password = "gci@000000"; //for test
		// 		// this.username = 'test_ss_xj_sp';
		// 		this.username = 'xj'; //for test
		// 		// this.password = "gci@000000"; //for test
		// 		// this.username = 'test_ss_ro_dev';
		// 		// this.username = 'test_tec_meter_sp';
		// 		// this.username = 'test_ss_mo_sp';
		// 		// this.username = 'test_tec_header';

		// 		// this.password = "gci@000000"; //for test
		// 	}
		// }
		console.log("onshow");
		this.currproxy = proxy
		console.log("currproxy:" + this.currproxy)
		this.isShowUrl = true
		this.isRemember = uni.getStorageSync('isRemember') || false

		console.log("isRemember :" + this.isRemember)
		if (this.isRemember) {
			this.password = uni.getStorageSync('pwd') || ""

			this.checkboxValue = ["记住密码"]
			console.log("remember pwd :" + this.password + "	this.checkboxValue:" + this.checkboxValue)
		} else this.checkboxValue = []

		var width, height;
		uni.getSystemInfo({
			success: function (res) {
				width = res.windowWidth * 0.7 * 0.5;
				height = res.windowHeight * 0.05;
				console.log(res.windowWidth);
			}
		});
		// var mheight;
		let view = uni.createSelectorQuery().select(".block_pwd");
		view.boundingClientRect(data => {
			console.log("height =" + data.height);
			this.inputHeight = data.height;
		}).exec();

		// if (this.inputHeight == 0&&height>0)
		// 	this.inputHeight = height;
		// else 
		this.inputHeight = 30
		this.mcaptcha = new Mcaptcha({
			el: 'canvas',
			width: width,
			height: this.inputHeight,
			createCodeImg: ""
		});
		console.log("inputheight =" + this.inputHeight);

	},
	onShow() {

	},
	computed: {
		inputStyle() {
			let style = {};
			if (this.tel) {
				style.color = "#fff";
				style.backgroundColor = this.$u.color['warning'];
			}
			return style;
		}
	},
	methods: {
		rmdPwd(n) {
			this.isRemember = n;
			getApp().globalData.isRemember = this.isRemember;

			console.log("rmdPwd()", this.isRemember);
			console.log("rmdPwd()", getApp().globalData.isRemember);
		},

		getCode() {
			console.log("getCode()");
			// getCode().then(res => {
			// 	// console.log('code:'+res.data[0].code+'tag:'+res.data[0].codeTag);
			// 	this.code = res.data[0].code;
			// 	this.codeTag = res.data[0].codeTag;
			// 	console.log('code:' + this.code + 'tag:' + this.codeTag);
			// 	this.mcaptcha.refresh(this.code);
			// 	if (proxy == "http://chinagci.dajtech.net") {
			// 		this.inputCode = this.code; //for test
			// 		this.password = "123456"
			// 	}

			// })
		},

		// 刷新验证码
		updateImageCode() {
			console.log('updateImageCode');
			this.getCode();
		},
		submit() {
			console.log("进入方法")

			uni.setStorageSync('token', "");
			uni.setStorageSync('isDevice', false);
			uni.setStorageSync('isSys', false);
			var that = this;
			if (that.username == null || that.username.length == 0) {
				console.log("请输入用户名")
				this.$u.toast("请输入用户名", 1000)
				return
			}

			if (that.password == null || that.password.length == 0) {
				console.log("请输入密码")
				this.$u.toast("请输入密码", 1000)
				return
			}
			var md5pwd;
			var param
			md5pwd = md5(that.password)

			param = {
				account: that.username,
				// username: that.username,
				password: md5pwd,
				//device: "APP"
			}


			// console.log(JSON.stringify(param));
			uni.removeStorageSync('token')
			this.loading = true;
			login(param).then(res => {
				this.loading = false;

				that.res = JSON.stringify(res);
				console.log(that.res)
				if (res.success) {



					uni.setStorageSync('token', "Bearer " + res.msg);
					var globalData = getApp().globalData
					globalData.token = "Authorization:Bearer " + res.msg;
					console.log("get token:" + getApp().globalData.token)

					uni.setStorageSync('username', that.username);
					getApp().globalData.username = that.username;
					console.log("username:" + getApp().globalData.username)
					getApp().globalData.isRemember = that.isRemember
					if (that.isRemember) {
						getApp().globalData.pwd = that.password;
						uni.setStorageSync('pwd', that.password);

					} else getApp().globalData.pwd = "";
					uni.setStorageSync('isRemember', that.isRemember);
					console.log("res:" + JSON.stringify(res))

					uni.reLaunch({
						url: '/pages/tabs/main'
					})
				} else {
					console.log("失败信息：" + res.msg);
					if (res.code == 500) {
						if (res.msg.includes("503 SERVICE_UNAVAILABLE "))
							this.$u.toast("获取服务失败，请联系管理员", 1000)
						else
							this.$u.toast("请检查用户名、密码是否正确", 3000)
					} else
						this.$u.toast("登录失败 " + res.msg, 1000)
				}
			}).catch(() => {
				this.loading = false;
			})
		},
		tip() {
			uni.reLaunch({
				url: '/pages/tabs/home'
			})

		},
		// clearPwd() {
		// 	console.log("clearPwd")
		// },
		// clearName() {
		// 	console.log("clearName")
		// },
		confirm(value) {
			// 输入框的值
			console.log(value)
			// TODO 做一些其他的事情，手动执行 close 才会关闭对话框
			this.$refs.popup.close()
		},

		changeShowPwd() {


			if (this.isPassword) {
				this.isPassword = false
				this.type = "text"
			} else {
				this.isPassword = true
				this.type = "password"
			}
			console.log("changeShowPwd" + this.isPassword + 'type' + this.type)
		},
		handleInputCount(e, item) {
			if (item.count) {
				if (parseInt(e) !== 0) {
					item.value = e;
				}
			}
		}
	}
};
</script>

<style>
page {
	background-color: white;
}
</style>

<style lang="scss" scoped>
.wrap {
	display: flex;

	font-size: 28rpx;
	background: white;
	height: 100vh; //若页面占不满时加
	width: 100%;
	height: 100%;

	.img {
		height: 35vh;
		width: 100%;
		display: flex;
		flex-direction: column;
		background-image: url('../../../static/uview/common/login_logo.png');
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center center;
	}

	.welcome {
		color: white;
		text-align: left;
		margin-top: 144rpx;
		font-weight: bold;
		margin-left: 42px;
		font-size: 80rpx;
	}

	.app_name {
		color: white;
		text-align: left;
		margin-top: 5rpx;
		margin-left: 42px;
		font-size: 32rpx;
		font-weight: 400;
	}

	.panel {
		position: absolute;
		border-top-left-radius: 33px;
		border-top-right-radius: 33px;
		width: 100%;
		background-color: white;
		margin-top: 28vh;
		height: 70vh; //若页面占不满时加
	}

	.content {
		width: 80%;
		margin: 5px auto 0;
		padding-top: 0rpx;
		height: 100%; //若页面占不满时加


		.block_line {
			// margin-top: 74px;
			margin-left: 10px;
			margin-right: 10px;
			display: flex;
			height: 128rpx;
			background-color: white;
			flex-direction: column;



			.input {
				background-color: white;
				width: auto;
				color: #1F1F1F;
				// font-size: 40rpx;
			}
		}

		.block_pwd {
			border: gray;
			display: flex;
			flex-direction: row;
		}

		.pwdicon {
			margin-left: 10px;
		}

		.block_capture {
			height: 50px;
			margin-top: 10rpx;
			display: flex;
			flex-direction: row;
			margin-left: 5px;
			align-items: center;

			.u--input {
				width: 50%;
				font-size: 60rpx;
			}

			.code-img-wrapper {
				width: 45%;
				height: 30rpx;
				margin-right: 15%;
			}

		}

		// .title {
		// 	text-align: left;
		// 	font-size: 60rpx;
		// 	font-weight: 500;
		// 	margin-bottom: 100rpx;
		// 	background-color: rgb(253, 243, 208);
		// }



		.getCaptcha {
			background-color: #E04B28;
			// color: $u-tips-color;
			border: none;
			font-size: 30rpx;
			padding: 12rpx 0;

			&::after {
				border: none;
			}
		}

		.alternative {
			// color: $u-tips-color;
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
		}

		.text_line {
			margin-top: 20px;
			color: #999999;
			font-size: 14px;
			text-align: center;
			font-weight: 400;
		}
	}


	.cb {
		margin-top: 32rpx;
		margin-left: 10px;
	}
}
</style>