// 工单接口返回处理
import {
	proxy,
	proxyDevice
} from './config.js'
import {
	request
} from './server.js'

// var pre = ''
var pre = '/potevio-device'
var preSys = '/potevio-system'
//备件出库
export const putout =
	function(params) {

		uni.setStorageSync('isDevice', true);
		var url = pre + '/device-main-extend/putout' ;
		console.log("putout url：" + url);
		return request.post(url,params)
			.then(data => {
				uni.setStorageSync('isDevice', false);
				return data.data
			})
	}
export const deviceList =
	function(pageNum, pageSize, deviceCategory, deviceType, deviceSysType, queryWords) {

		uni.setStorageSync('isDevice', true);
		var url;

		// url = pre + '/device/device-detail-extend/?' +
		url = pre + '/device-detail-extend/?' +
			"deviceType=" + deviceType + '&deviceCategory=' + deviceCategory +
			"&deviceSysType=" + deviceSysType +
			"&queryWords=" + queryWords +
			"&pageNum=" + pageNum +
			"&pageSize=" + pageSize //+	"&totalSize=6018"

		console.log("deviceList url：" + url);
		return request.get(url)
			.then(data => {
				uni.setStorageSync('isDevice', false);
				return data.data
			})
	}
	
	//
//保养列表获取fkDeviceId
export const getfkDeviceId =
	function(id) {
		uni.setStorageSync('isDevice', true);

		var url = pre + '/device-detail/' + id

		console.log('getfkDeviceId url:'+url);
		return request.get(url)
			.then(res => {
				// console.log('getfkDeviceId back res.data:'+JSON.stringify(res.data));
				// console.log('getfkDeviceId back res.data.data.fkDeviceId:'+JSON.stringify(res.data.data.fkDeviceId));
				uni.setStorageSync('isDevice', false);
				return res.data
			})
	}	
//设备详情
export const deviceDetail =
	function(id) {
		uni.setStorageSync('isDevice', true);
		// var url = pre + '/device/device-detail-extend/' + id
		var url = pre + '/device-detail-extend/' + id

		console.log('deviceDetail url:'+url);
		return request.get(url)
			.then(data => {
				uni.setStorageSync('isDevice', false);
				return data.data
			})
	}
//设备技术规格
export const deviceProperty =
	function(id) {
		uni.setStorageSync('isDevice', true);
		// var url = pre + '/device/device-property/'
		var url = pre + '/device-property/'

		// console.log(url);
		return request.get(url)
			.then(data => {
				uni.setStorageSync('isDevice', false);
				return data.data
			})
	}

//设备状态
export const getDeviceState =
	function() {
		uni.setStorageSync('isDevice', true);
		// var url = pre + '/device/sys-meta-type-extend/5';
		var url = preSys + '/sys-meta-type-extend/5';
		// console.log(url);
		var mUrl = proxyDevice + url;
		const Token = uni.getStorageSync('token') ? uni.getStorageSync('token') : ''

		if (Token != null & Token.length > 0) {
			return new Promise((resolve, reject) => {
				uni.request({
					url: mUrl,
					method: 'GET',
					header: {
						'Authorization': Token //携带token
					},

					complete: (res) => {
						console.log("deviceState complete:" + JSON.stringify(res));
						uni.setStorageSync('isDevice', false);
						if (res.data.success) {
							var result = res.data.data
							uni.setStorageSync('deviceState', result);
							console.log("getDeviceState success :")
							// console.log(JSON.stringify( uni.getStorageSync('deviceState')))
							resolve(result)
						}
					},
				})
			})
		}

	}
//归属部门
export const getSysOrg =
	function() {
		uni.setStorageSync('isDevice', true);
		// var url = pre + '/device/sys-org/';
		var url = pre + '/sys-org/';
		// console.log(url);

		var mUrl = proxyDevice + url;
		const Token = uni.getStorageSync('token') ? uni.getStorageSync('token') : ''

		if (Token != null & Token.length > 0) {
			return new Promise((resolve, reject) => {
				uni.request({
					url: mUrl,
					method: 'GET',
					header: {
						'Authorization': Token //携带token
					},

					complete: (res) => {
						// console.log("complete:"+JSON.stringify(res));
						uni.setStorageSync('isDevice', false);
						if (res.data.success) {
							var result = res.data.data
							uni.setStorageSync('sysOrg', result);
							// console.log("getSysOrg success :")
							// console.log(JSON.stringify( uni.getStorageSync('sysOrg')))
							resolve(result)
						}
					},
				})
			})
		}


	};


//图片
export const getFile =
	function(pkid) {
		uni.setStorageSync('isDevice', true);
		var url = pre + '/file-extend/' + pkid;
		// var url = pre + '/device/file-extend/'+pkid;
		// console.log(url);
		return request.get(url)
			.then(data => {
				// console.log('device getFile  back');
				uni.setStorageSync('isDevice', false);
				return data.data
			})
	}