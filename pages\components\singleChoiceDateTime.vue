<template>
	
		
	<view class="line" >

		<text class="title">{{title}}</text>

		<uni-datetime-picker class="info" :show="show" type="datetime"  @change="changeLog" 
		:border="false"/>
			

	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => "请选择"
			},
			show: {
				type: Boolean,
				default () {
					false
				}
			},


		},


		methods: {
			changeLog(e) {
					console.log("changeLog：" )
				this.value =e;
					console.log("changeLog：" + this.value)
				this.$emit('update:msg',this.value)
		
				this.show = false;
			},

			close() {
				this.show = false
			},
		
		}
	}
</script>

<style lang="scss" scoped>


</style>