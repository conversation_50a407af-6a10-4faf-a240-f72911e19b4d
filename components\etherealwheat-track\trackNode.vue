<template>
	<div class="total-wrap">
		<div class="node-container">
			<div class="node-container-left">
				<div class="tag-container">
					<img v-if="isMainNode" :src="nodeIconUrl" />
					<div v-else class="node-tag-container">
						<div class="node-tag"></div>
					</div>
				</div>
				<div class="line-container" v-if="!nodeData.isLast">
					<!-- :style="{height: isMainNode?'142rpx':'88rpx', paddingTop: isMainNode?'22rpx':'8rpx'}" -->
					<!-- 	<div v-if="!isFirst" class="line" :style="{height: isMainNode?'120rpx':'80rpx', }"></div> -->
				</div>
			</div>
			<div class="node-container-right" :style="{paddingTop: isMainNode?'0':'8rpx'}">
				<view class="node-title-line">
					<div class="node-title">
						{{nodeData.actUserName}} <!--  处理人 -->
					</div>
					<div class="node-time">{{nodeData.actDate}}</div>
				</view>
				<view class="detailBlock" style="margin-top: 3px;">
					<text class="node-desc"
						:style="{color: isNewest?'#4b4b4b':'#4b4b4b', marginTop: isMainNode?'10rpx':'0'}">
						[{{nodeData.actMsgTitle}}]{{nodeData.actMsg}}
					</text>
					<div class="node-detail">{{nodeData.nodeDetail}}</div>
				<!-- 	<div v-if="nodeData.phone" class="node-phone">{{nodeData.phone}}</div> -->
					<u-album :urls="nodeData.pics" style="margin-left: 100rpx;	margin-right: 22rpx; "></u-album>
					<!-- v-if="nodeData.pics!=null&&nodeData.pics.length>0" -->
				</view>
			</div>
		</div>
	</div>
</template>
<script>
	export default {
		props: {
			isNewest: {
				type: Boolean,
				default: true
			},
			isFirst: {
				type: Boolean,
				default: false
			},

			isMainNode: {
				type: Boolean,
				default: false
			},
			nodeData: {
				type: Object,
				default () {
					return {
						actUserName: '',
						actMsgTitle: '',
						statusName: '',
						actDate: '',
						nodeTitle: 'xxxxxx',
						pics: [],
						isLast: false,
					}
				}
			}
		},
		computed: {
			nodeIconUrl() {
				if (this.nodeData.status === 'WATTING_PAY') { // 待付款
					return this.isNewest ? '/static/images/ic-order-commit-G.png' : '/static/images/ic-order-commit.png'
				} else if (this.nodeData.status === 'PAYED') { // 待发货
					return this.isNewest ? '/static/images/ic-paied-G.png' : '/static/images/ic-paied.png'
				} else if (this.nodeData.status === 'WATTING_DELIVER') { // 已揽件
					return this.isNewest ? '/static/images/ic-pacakaging-G.png' : '/static/images/ic-pacakaging.png'
				} else if (this.nodeData.status === 'DELIVERING') { // 运输中
					return this.isNewest ? '/static/images/ic-sending.png' : '/static/images/ic-sending-G.png'
				} else if (this.nodeData.status === 'COMPLETE') { // 已完成
					return this.isNewest ? '/static/images/ic-delivering-G.png' : '/static/images/ic-delivering.png'
				}
			},
			nodeTitleFixed() {
				if (!this.nodeData.nodeTitle) return ''
				return this.nodeData.nodeTitle.replace(/(\d{3})\d{4}(\d{4})/, '')
			}
		}
	}
</script>
<style lang="scss" scoped>
	.total-wrap {

		padding-top: 15rpx;
		width: 100%;
		background-color: transparent;

		.node-container {
			width: 100%;
			height: auto;
			display: flex;

			margin-left: 22rpx;
			background-color: transparent;

			&-left {
				width: 44rpx;
				height: auto;

				.tag-container {
					width: auto;
					height: auto;
					margin-top: 30rpx;

					.img {

						width: auto;
						height: auto;
					}

					.node-tag-container {
						width: auto;
						height: auto;
						display: flex;
						justify-content: center;
						align-items: center;

						.node-tag {
							//那个未到达的点
							width: 14rpx;
							height: 14rpx;
							background-color: #dcdcdc;
							border-radius: 50%;
						}
					}
				}

				.line-container {
					background-color: transparent;
					width: 3rpx;
					height: 100%;
					margin-left: 22rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: lightgray;

					// .line {
					// 	padding: 0 0 0 5rpx;
					// 	background-color: lightgray;
					// }
				}
			}

			&-right {
				flex: 1;

				box-sizing: border-box;
				padding: 0 10rpx 0 0; //上右下左


				.node-title-line {
					margin-left: 10rpx;
					// margin-right: 10rpx;
					display: flex;
					flex-direction: line;
					align-items: center;
					width: 93%;
					height: auto;

					.node-title {
						width: 50%;
						height: 40rpx;
						line-height: 44rpx;
						color: #000000;
						font-size: 28rpx;
						justify-content: left;
						font-family: 'PingFangSC-Medium';
					}

					.node-time {
						position: absolute;
						right: 40rpx;
						width: wrap;
						height: auto;
						white-space: nowrap;
						vertical-align: center;
						font-size: 20rpx;
						vertical-align: center;
						background-color: transparent;
						color: #999999;
						font-family: 'PingFangSC-Medium';
					}
				}


				.detailBlock {
					width: 90%;
					height: auto;
					padding: 15rpx 0rpx 15rpx 20rpx;
					background-color: white;
					border-radius: 10rpx;
				}


				.node-desc {
					margin-left: 14rpx;
					// margin-top: 16rpx;
					white-space: normal;
					word-wrap: break-word;
					word-break: normal;
					min-height: 30rpx;
					line-height: auto;
					color: #666666;
					font-size: 24rpx;
					font-family: 'PingFangSC-Regular';

				}

				.node-detail {
					// margin-top: 16rpx;
					width: 100%;
					min-height: 30rpx;
					line-height: auto;
					color: #666666;
					flex-wrap: wrap;
					font-size: 32rpx;
					font-family: 'PingFangSC-Regular';
					word-wrap: break-word;
					word-break: normal;
				}

				// .node-phone {
				// 	margin-top: 8rpx;
				// 	width: 100%;
				// 	height: 26rpx;
				// 	line-height: 26rpx;
				// 	color: #666666;
				// 	font-size: 32rpx;
				// 	font-family: 'Avenir-Medium';
				// }


			}
		}
	}
</style>