//巡检util
import {
	orderStates
} from "../api/sys.js"
import {
	checkItemPaging,

} from "../../../api/workorder.js"
// 工单工具
export function getInspectionItems(pkid,model,) { //工单关联的巡检项List
	console.log("getInspectionItems")
	this.model.workorderItemList = [];
	checkItemPaging(pkid, 1, 100).then(res => {
		if (res.success) {
			// console.log("巡检项res:" + JSON.stringify(res));
			this.workorderItemDicts = res.data;

			if (this.workorderItemDicts == null || this.workorderItemDicts.length == 0) {

			} else {
				var tempArray = Array();
				this.workorderItemDicts.forEach(function(element, index, array) {
					// console.log("workorderItemDicts:" + index + ",element:" + JSON.stringify(
					// 	element));
					var workorderItemModel = {
						"pkId": element.pkId, //项ID
						"itemId": element.pkId, //项ID
						"itemName": element.itemName, //项名
						"itemDetail": element.itemDetail, //项描述
						// 项执行状态，true：已执行，false：未执行
						"itemExecutionState": false,
						// 项异常状态：true：异常，false：正常
						"itemAbnormalState": false,
						"orderItemResultDescription": "" //对应的结果描述
					};

					tempArray.push(workorderItemModel);
				});
				// console.log("tempArray:" + JSON.stringify(tempArray));
				this.model.workorderItemList = tempArray

				// console.log("workorderItemList:" + JSON.stringify(this.model.workorderItemList));
			}


		} else {
			console.log("失败信息：" + res.msg);
			this.$u.toast(res.msg, 1000)
		}
	})
},