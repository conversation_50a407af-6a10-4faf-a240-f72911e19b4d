<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="nav-bar">
				<uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
				<text class="title">数据统计</text>
				<uni-icons type="reload" size="20" color="#fff" @click="refreshData"></uni-icons>
			</view>
		</view>
		<!--生产指标-->
		<view class="card">
			<view class="card-title">
				<view class="title-indicator"></view>
				<text>生产指标</text>
			</view>
			<view class="data-section">
				<view class="data-item">
					<view class="data-label">今日进水量 m³</view>
					<view class="data-value">
						<view class="digit-box" v-for="(digit, index) in waterInlet" :key="'inlet-' + index">{{ digit }}
						</view>
					</view>
				</view>
				<view class="data-item" style="margin-top: 10px;">
					<view class="data-label">今日产水量 m³</view>
					<view class="data-value">
						<view class="digit-box" v-for="(digit, index) in waterOutlet" :key="'outlet-' + index">{{ digit
						}}
						</view>
					</view>
				</view>
			</view>
			<!-- <view class="legend">
				<view class="legend-item">
					<view class="color-box inlet"></view>
					<text>进水量</text>
				</view>
				<view class="legend-item">
					<view class="color-box outlet"></view>
					<text>产水量</text>
				</view>
			</view> -->
			<view class="chart-container">
				<qiun-data-charts type="area" :opts="opts" :chartData="chartData" />
			</view>
		</view>
		<!--药耗指标-->
		<view class="card" style="margin-top: 10px;">
			<view class="card-title">
				<view class="title-indicator"></view>
				<text>药耗统计</text>
			</view>
			<view class="drug-legend">
				<view class="drug-legend-item">
					<view class="drug-color-box pac"></view>
					<text>PAC {{ chemicalConsumption[0] }} L</text>
				</view>
				<view class="drug-legend-item">
					<view class="drug-color-box naocl"></view>
					<text>次氯酸钠 {{ chemicalConsumption[1] }} L</text>
				</view>
				<view class="drug-legend-item">
					<view class="drug-color-box pam"></view>
					<text>PAM {{ chemicalConsumption[2] }} L</text>
				</view>
			</view>
			<view class="chart-container">
				<qiun-data-charts type="line" :opts="drugChartOpts" :chartData="drugChartData" />
			</view>
		</view>
		<!--生产值班
		<view class="card" style="margin-top: 10px;">
			<view class="card-title">
				<view class="title-indicator"></view>
				<text>生产值班</text>
			</view>
			<view class="duty-info">
				<text>日班 08:30至17:30 值班人数 5人</text>
				<text>值班成员: 杨紫轩; 陈清宇; 郭美琳; 黄志强; 吴昊天</text>
			</view>
			<view class="card-title" style="margin-top: 20px;">
				<view class="title-indicator"></view>
				<text>情况说明</text>
			</view>
			<view class="situation-list">
				<view class="situation-item" v-for="(item, index) in situationList" :key="index">
					<text class="situation-text">{{ index + 1 }}. {{ item.text }}</text>
					<text class="situation-time">{{ item.time }}</text>
				</view>
			</view>
		</view>-->
		<!--实时水质-->
		<view class="card" style="margin-top: 10px;">
			<view class="card-title">
				<view class="title-indicator"></view>
				<text>实时水质</text>
			</view>
			<view class="water-quality-section">
				<view class="water-quality-column">
					<text class="water-quality-title">实时进水水质</text>
					<view class="water-quality-item" v-for="(item, index) in inletWaterQuality" :key="index">
						<text class="quality-label">{{ item.name }}</text>
						<view class="quality-value">
							<view class="progress-bar" :style="{ width: item.processWidth + '%' }"></view>
							<text class="value-text">{{ item.value }}</text>
						</view>
					</view>
				</view>
				<view class="water-quality-column">
					<text class="water-quality-title">实时出水水质</text>
					<view class="water-quality-item" v-for="(item, index) in outletWaterQuality" :key="index">
						<text class="quality-label" style="border-color: #67C23A;">{{ item.name }}</text>
						<view class="quality-value">
							<view class="progress-bar" :style="{ width: item.processWidth + '%' }"></view>
							<text class="value-text">{{ item.value }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--供水压力流量-->
		<view class="card" style="margin-top: 10px;">
			<view class="card-title">
				<view class="title-indicator"></view>
				<text>供水压力流量</text>
			</view>
			<view class="pressure-flow-section">
				<view class="pressure-flow-item" v-for="(item, index) in realtimePressureFlowData" :key="index">
					<text class="pressure-flow-label">{{ item.name }}</text>
					<text class="pressure-flow-value">{{ item.value }} {{ item.unit }}</text>
				</view>
			</view>
			<view class="chart-container">
				<qiun-data-charts type="line" :opts="pressureFlowOpts" :chartData="pressureFlowData" />
			</view>
		</view>
		<!--各车间运行状态-->
		<view class="card" style="margin-top: 10px;">
			<view class="card-title">
				<view class="title-indicator"></view>
				<text>各车间运行状态</text>
			</view>
			<view class="tabs-container">
				<scroll-view scroll-x class="tabs">
					<view v-for="(tab, index) in tabs" :key="index" class="tab-item"
						:class="{ active: activeWorkshopTab === tab.name }" @click="getDeviceStatus(tab.name)">
						{{ tab.name }}
					</view>
				</scroll-view>
			</view>
			<view class="table-container">
				<view class="table" v-if="tableData.rows.length > 0">
					<view class="thead">
						<view class="tr">
							<view class="th"></view>
							<view class="th" v-for="(header, index) in tableData.headers" :key="index">{{ header }}
							</view>
						</view>
					</view>
					<view class="tbody">
						<view class="tr" v-for="(row, rowIndex) in tableData.rows" :key="rowIndex">
							<view class="td">{{ row.time }}</view>
							<view class="td" v-for="(status, colIndex) in row.statuses" :key="colIndex">
								<view class="status-dot" :class="status === 1 ? 'green' : 'red'"></view>
							</view>
						</view>
					</view>
				</view>
				<view class="no-data" v-else>
					<text>暂无数据</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import qiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
import { getStatisticaValue, getChartList, getRealList, getHisList } from '@/api/total';
import { formatDateTime } from '../../../utils/timeUtils.js';
import { formatNumberWithCommas } from '../../../utils/index.js';

export default {
	components: { qiunDataCharts },
	data() {
		return {
			queryParams: {
				processName: "",
				countName: "",
				startDate: formatDateTime(new Date(), 'yyyy-MM-dd 00:00'),
				endDate: formatDateTime(new Date(new Date().getTime() + 86400000), 'yyyy-MM-dd 00:00')
			},
			activeWorkshopTab: '臭氧接触',
			timeList: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'],
			chemicalConsumption: [0, 0, 0],
			tabs: [
				{ name: '配水车间' },
				{ name: '净水车间' },
				{ name: '臭氧接触' },
				{ name: '炭滤车间' },
				{ name: '回收水池' },
				{ name: '送水泵房' }
			],
			tableData: {
				headers: ['1#', '2#', '3#', '4#'],
				rows: [
					{ time: '07:00', statuses: [1, 1, 1, 1] },
					{ time: '08:00', statuses: [1, 1, 1, 1] },
					{ time: '09:00', statuses: [1, 0, 1, 0] },
					{ time: '10:00', statuses: [1, 0, 1, 0] },
					{ time: '11:00', statuses: [1, 0, 1, 0] },
					{ time: '12:00', statuses: [1, 1, 1, 0] },
					{ time: '13:00', statuses: [1, 1, 1, 0] },
					{ time: '14:00', statuses: [1, 1, 1, 0] }
				]
			},
			waterInlet: ['3', '5', ',', '8', '7', '4'],
			waterOutlet: ['3', '9', ',', '4', '2', '5'],
			chartData: {},
			drugChartData: {},
			pressureFlowData: {},
			pressureFlowOpts: {
				color: ["#4e8af9", "#facc14"],
				padding: [30, 15, 15, 15],
				dataLabel: false,
				dataPointShape: false,
				legend: {
					show: true,
					position: 'top'
				},
				xAxis: {
					disableGrid: true,
					axisLineColor: '#CCCCCC',
					labelCount: 4
				},
				yAxis: [
					{
						gridType: "dash",
						dashLength: 2,
						data: [{
							min: 0,
							max: 0.5,
							format: "{value} Mpa"
						}]
					},
					{
						gridType: "dash",
						dashLength: 2,
						data: [{
							format: "{value} m³/h"
						}]
					}
				],
				extra: {
					line: {
						type: "straight",
						width: 2
					}
				}
			},
			drugChartOpts: {
				color: ["#4e8af9", "#52d583", "#facc14"],
				padding: [30, 15, 15, 15],
				dataLabel: false,
				dataPointShape: false,
				legend: {
					show: true,
					position: 'top',
				},
				xAxis: {
					disableGrid: true,
					axisLineColor: '#CCCCCC',
					labelCount: 4
				},
				yAxis: {
					gridType: "dash",
					dashLength: 2,
					splitNumber: 5,
				},
				extra: {
					line: {
						type: "straight",
						width: 2
					}
				}
			},
			opts: {
				color: ["#82d8d5", "#4e8af9"],
				padding: [15, 15, 0, 15],
				dataLabel: false,
				dataPointShape: false,
				legend: {
					show: true,
					position: 'top'
				},
				xAxis: {
					disableGrid: true,
					labelCount: 4
				},
				yAxis: {
					gridType: "dash",
					dashLength: 2
				},
				extra: {
					area: {
						type: "curve",
						opacity: 0.2,
						addLine: true,
						width: 2,
						gradient: true
					}
				}
			},
			situationList: [
				{
					text: '水质安全保障及检测调度计划: 清水池#1清洗',
					time: '09:01'
				},
				{
					text: '净水设施运行与维护调度方案: 加氯混药器-DU0921校验',
					time: '10:21'
				},
				{
					text: '清洗沉淀池, 开启反冲洗设备',
					time: '12:33'
				},
				{
					text: '保养泵房设备, 根据保养单要求保养泵房设备',
					time: '15:02'
				},
				{
					text: '通过样本化验监测出水水质, 确保达标排放',
					time: '16:54'
				}
			],
			realtimePressureFlowData: [],
			timer: null,
			inputTotalList: {
				"TU": 12, //浊度
				"TCL": 0.4,//总氯
				"CL": 10,//余氯
				"PH": 12,//PH
				"CD": 1200,//电导率
				"SS": 1300,//SS
				"COD": 8,//COD
				"ALK": 13000,//碱度
				"NHN": 1.3//氨氮
			},
			inletWaterQuality: [],
			outputTotalList: {
				"TU": 0.65, //浊度
				"TCL": 4,//总氯
				"CL": 2.6,//余氯
				"PH": 11,//PH
			},
			outletWaterQuality: []
		};
	},
	onReady() {
		this.refreshData();
		// 设置定时器，每3分钟执行一次refreshData
		this.timer = setInterval(() => {
			this.refreshData();
		}, 3 * 60 * 1000);
	},
	onUnload() {
		// 页面卸载时清除定时器
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		//刷新数据
		async refreshData() {
			this.getProductionData();
			this.getDrugData();
			this.getWaterQualityData();
			this.getWaterPressureFlowData();
			this.getDeviceStatus(this.activeWorkshopTab);
		},
		//生产指标
		async getProductionData() {
			//今日进水量
			const queryParams1 = Object.assign({}, this.queryParams);
			queryParams1.processName = "进水厂流量";
			queryParams1.countName = "进厂水累计流量";
			const res1 = await getStatisticaValue(queryParams1);
			let value1 = '0';
			if (res1.code === 200 && res1.data.length) {
				value1 = formatNumberWithCommas(res1.data[0].value.toString(), 0);
			}
			this.waterInlet = value1.split('');
			//今日产水量
			const queryParams2 = Object.assign({}, this.queryParams);
			queryParams2.processName = "出水厂流量";
			queryParams2.countName = "出水厂累计流量";
			const res2 = await getStatisticaValue(queryParams2);
			let value2 = '0';
			if (res2.code === 200 && res2.data.length) {
				value2 = formatNumberWithCommas(res2.data[0].value.toString(), 0);
			}
			this.waterOutlet = value2.split('');
			//今日进水量和产水量曲线
			queryParams1.interval = 60;
			const res3 = await getChartList(queryParams1);
			if (res3.code === 200 && res3.data.length) {
				if (res3.data[0].list.length && JSON.stringify(res3.data[0].list[0] !== '{}')) {
					const seriesData1 = Object.values(res3.data[0].list[0]);
					queryParams2.interval = 60;
					const res4 = await getChartList(queryParams2);
					if (res4.code === 200 && res4.data.length) {
						if (res4.data[0].list.length && JSON.stringify(res4.data[0].list[0] !== '{}')) {
							const seriesData2 = Object.values(res4.data[0].list[0]);
							let res = {
								categories: this.timeList,
								series: [
									{
										name: "进水量",
										data: seriesData1
									},
									{
										name: "产水量",
										data: seriesData2
									}
								]
							};
							this.chartData = JSON.parse(JSON.stringify(res));
						}
					}
				}
			}
		},
		//药耗统计
		async getDrugData() {
			//PAC
			const queryParams1 = Object.assign({}, this.queryParams);
			queryParams1.processName = "加药间";
			queryParams1.countName = "PAC加药量";
			queryParams1.deviceName = "加药量";
			const res1 = await getStatisticaValue(queryParams1);
			if (res1.code === 200 && res1.data.length) {
				this.chemicalConsumption[0] = res1.data[0].value ? res1.data[0].value.toFixed(1) : 0;
			}
			//次氯酸钠
			const queryParams2 = Object.assign({}, this.queryParams);
			queryParams2.processName = "加药间";
			queryParams2.countName = "次钠";
			queryParams2.deviceName = "加药量";
			const res2 = await getStatisticaValue(queryParams2);
			if (res2.code === 200 && res2.data.length) {
				this.chemicalConsumption[1] = res2.data[0].value ? res2.data[0].value.toFixed(1) : 0;
			}
			//PAC
			const queryParams3 = Object.assign({}, this.queryParams);
			queryParams3.processName = "加药间";
			queryParams3.countName = "PAM加药量";
			queryParams3.deviceName = "加药量";
			const res3 = await getStatisticaValue(queryParams3);
			if (res3.code === 200 && res3.data.length) {
				this.chemicalConsumption[2] = res3.data[0].value ? res3.data[0].value.toFixed(2) : 0;
			}
			//今日进水量和产水量曲线
			queryParams1.interval = 60;
			const res4 = await getChartList(queryParams1);
			if (res4.code === 200 && res4.data.length) {
				if (res4.data[0].list.length && JSON.stringify(res4.data[0].list[0] !== '{}')) {
					const seriesData1 = Object.values(res4.data[0].list[0]);
					let seriesData2 = [];
					queryParams2.interval = 60;
					const res5 = await getChartList(queryParams2);
					if (res5.code === 200 && res5.data.length) {
						if (res5.data[0].list.length && JSON.stringify(res5.data[0].list[0] !== '{}')) {
							seriesData2 = Object.values(res5.data[0].list[0]);
						}
					}
					let seriesData3 = [];
					queryParams3.interval = 60;
					const res6 = await getChartList(queryParams3);
					if (res6.code === 200 && res6.data.length) {
						if (res6.data[0].list.length && JSON.stringify(res6.data[0].list[0] !== '{}')) {
							seriesData3 = Object.values(res6.data[0].list[0]);
						}
					}
					let res = {
						categories: this.timeList,
						series: [{
							name: "PAC",
							data: seriesData1
						}, {
							name: "次氯酸钠",
							data: seriesData2
						}, {
							name: "PAM",
							data: seriesData3
						}]
					};
					this.drugChartData = JSON.parse(JSON.stringify(res));
				}
			}
		},
		//实时水质
		async getWaterQualityData() {
			const res1 = await getRealList({ "processName": "进厂水水质" });
			if (res1.code === 200 && res1.data.length) {
				this.inletWaterQuality = res1.data.map((item) => {
					const processWidth = item.value / this.inputTotalList[item.paramTag] * 100;
					return {
						name: item.paramName,
						value: item.value ? Number(item.value.toFixed(3)) : 0,
						processWidth
					}
				});
			}
			const res2 = await getRealList({ "processName": "出厂水水质" });
			if (res2.code === 200 && res2.data.length) {
				this.outletWaterQuality = res2.data.map((item) => {
					const processWidth = item.value / this.outputTotalList[item.paramTag] * 100;
					return {
						name: item.paramName,
						value: item.value ? Number(item.value.toFixed(3)) : 0,
						processWidth
					}
				});
			}
		},
		//供水压力流量
		async getWaterPressureFlowData() {
			const res1 = await getRealList({ "processName": "供水压力与流量" });
			if (res1.code === 200 && res1.data.length) {
				const array = res1.data.map((item) => {
					return {
						name: item.deviceName,
						value: item.value.toFixed(3),
						unit: item.paramName === '压力' ? 'Mpa' : item.paramName === '瞬时流量' ? 'm³/h' : ''
					}
				});
				this.realtimePressureFlowData = [array[2], array[0], array[3], array[1]];
				console.log("this.realtimePressureFlowData", this.realtimePressureFlowData);
			}
			const queryParams2 = Object.assign({}, this.queryParams);
			delete queryParams2.countName;
			queryParams2.processName = "供水压力与流量";
			queryParams2.interval = 60;
			const res2 = await getChartList(queryParams2);
			if (res2.code === 200 && res2.data.length) {
				const seriesData = [];
				for (let i = 0; i < res2.data.length; i++) {
					if (res2.data[i].list.length && JSON.stringify(res2.data[i].list[0] !== '{}')) {
						seriesData.push({
							name: res2.data[i].deviceName,
							index: res2.data[i].countName.includes('压力') ? 0 : 1,
							data: Object.values(res2.data[i].list[0])
						});
					}
				}
				let res = {
					categories: this.timeList,
					series: seriesData
				};
				this.pressureFlowData = JSON.parse(JSON.stringify(res));
			}
		},
		//设备运行状态
		async getDeviceStatus(countName) {
			this.activeWorkshopTab = countName;
			const params = Object.assign({}, this.queryParams);
			params.processName = "重点设备设施运行情况";
			params.countName = countName;
			params.interval = 60;
			const res = await getHisList(params);
			if (res.code === 200 && res.data.length) {
				this.tableData.headers = [];
				this.tableData.rows = [];
				for (let i = 0; i < res.data.length; i++) {
					this.tableData.headers.push(i + 1 + "#");
				}
				const data = res.data[0];
				let timeList = [];
				if (data.list && Array.isArray(data.list)) {
					data.list.forEach((item) => {
						timeList.push(Object.keys(item)[0]);
					});
				}
				if (timeList.length) {
					for (let i = 0; i < timeList.length; i++) {
						const obj = { time: `${timeList[i].substring(11, 16)}`, statuses: [] };
						for (let j = 0; j < res.data.length; j++) {
							obj.statuses.push(Object.values(res.data[j].list[i])[0] === 'true' ? 1 : 0);
						}
						this.tableData.rows.push(obj);
					}
				}
			}
		}
	},
};
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	height: 48px;
	background: #1890ff;
	padding-top: var(--status-bar-height);

	.nav-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;

		.title {
			width: 100%;
			color: #fff;
			font-size: 18px;
			font-weight: 500;
			text-align: center;
		}
	}
}

.card {
	background-color: #fff;
	border-radius: 8px;
	padding: 15px;
}

.card-title {
	display: flex;
	align-items: center;
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 20px;
}

.title-indicator {
	width: 4px;
	height: 16px;
	background-color: #2979ff;
	margin-right: 8px;
}

.data-section {
	height: auto;
	margin-bottom: 20px;
}

.data-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.data-label {
	font-size: 14px;
	color: #999;
	margin-bottom: 5px;
}

.data-value {
	display: flex;
}

.digit-box {
	width: 30px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	background-color: #f0f0f0;
	border: 1px solid #e0e0e0;
	border-radius: 4px;
	margin: 0 2px;
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.legend {
	display: flex;
	justify-content: center;
	margin-bottom: 15px;
}

.legend-item {
	display: flex;
	align-items: center;
	margin: 0 15px;
	font-size: 14px;
}

.color-box {
	width: 12px;
	height: 12px;
	margin-right: 8px;
}

.inlet {
	background-color: #82d8d5;
}

.outlet {
	background-color: #4e8af9;
}

.drug-legend {
	display: flex;
	justify-content: center;
}

.drug-legend-item {
	display: flex;
	align-items: center;
	font-size: 14px;
	background-color: #fff;
	padding: 5px 10px;
	border-radius: 12px;
	border: 1px solid #e0e0e0;
	margin: 0 5px;
}

.drug-color-box {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	margin-right: 8px;
}

.pac {
	background-color: #4e8af9;
}

.naocl {
	background-color: #52d583;
}

.pam {
	background-color: #facc14;
}

.chart-container {
	height: 250px;
}

.duty-info {
	display: flex;
	flex-direction: column;
	font-size: 14px;
	color: #666;
}

.duty-info text:last-child {
	margin-top: 5px;
}

.situation-list {
	margin-top: 10px;
}

.situation-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 0;
	font-size: 14px;
	border-bottom: 1px solid #f0f0f0;
}

.situation-item:last-child {
	border-bottom: none;
}

.situation-text {
	color: #333;
}

.situation-time {
	color: #999;
}

.water-quality-section {
	display: flex;
	justify-content: space-between;
}

.water-quality-column {
	width: 48%;
}

.water-quality-title {
	display: block;
	font-size: 14px;
	color: #666;
	margin-bottom: 15px;
}

.water-quality-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
}

.quality-label {
	font-size: 14px;
	color: #333;
	padding-bottom: 2px;
	border-bottom: 2px solid #A0D9EF;
}

.quality-value {
	display: flex;
	align-items: center;
}

.progress-bar {
	height: 10px;
	background-color: #e0e0e0;
	margin-right: 10px;
	border-radius: 5px;
}

.progress-bar::after {
	content: '';
	display: block;
	height: 100%;
	background-color: #4CAF50;
	border-radius: 5px;
}

.value-text {
	font-weight: bold;
}

.pressure-flow-section {
	margin-top: 20px;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.pressure-flow-item {
	width: 48%;
	background-color: #f9f9f9;
	padding: 10px;
	border-radius: 4px;
	margin-bottom: 10px;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}

.pressure-flow-label {
	font-size: 12px;
	color: #999;
	margin-bottom: 5px;
}

.pressure-flow-value {
	font-size: 16px;
	font-weight: bold;
	color: #4e8af9;
}

.tabs-container {
	margin: 10px 0;
}

.table-container {
	height: 300px;
	overflow-y: auto;
	border: 1px solid #e0e0e0;
	border-radius: 4px;
}

.no-data {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	color: #999;
	font-size: 14px;
}

.tabs {
	white-space: nowrap;
	width: 100%;
	background-color: #f5f5f5;
	border-radius: 4px;
}

.tab-item {
	display: inline-block;
	padding: 8px 16px;
	font-size: 14px;
	color: #666;
	cursor: pointer;
}

.tab-item.active {
	color: #2979ff;
	position: relative;
}

.tab-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 20px;
	height: 2px;
	background-color: #2979ff;
}

.table {
	width: 100%;
	border-radius: 4px;
	overflow: hidden;
}

.thead {
	background-color: #f5f5f5;
}

.tr {
	display: flex;
	align-items: center;
}

.th,
.td {
	flex: 1;
	padding: 10px;
	text-align: center;
	font-size: 14px;
}

.th:first-child,
.td:first-child {
	width: 80px;
	flex: none;
	color: #666;
}

.status-dot {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	margin: 0 auto;
}

.status-dot.green {
	background-color: #52c41a;
}

.status-dot.red {
	background-color: #ff4d4f;
}
</style>