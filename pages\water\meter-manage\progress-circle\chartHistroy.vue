<template>
  <view class="charts-box">
    <qiun-data-charts
	v-if="show"
      type="area"
      :opts="opts"
      :chartData="dataList"
    />
  </view>
</template>

<script>
export default {
	// props:{
	// 	dataList:{
	// 		type: Object,
	// 	}
	// },
  data() {
    return {
		show:false,
      dataList: {},
      //您可以通过修改 config-ucharts.js 文件中下标为 ['area'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
        padding: [15,0,0,0],
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true
        },
        yAxis: {
          gridType: "dash",
          dashLength: 2
        },
        extra: {
          area: {
            type: "curve",
            opacity: 0.2,
            addLine: true,
            width: 2,
            gradient: true,
            activeType: "hollow"
          }
        }
      }
    };
  },
  onReady() {
    // this.getServerData();
  },
  methods: {
	  close(){
		  console.log(1)
		  this.show=false
	  },
    getServerData(data) {

		console.log(data)
		this.show=true
		// this.dataList = JSON.parse(JSON.stringify(data));
      //模拟从服务器获取数据时的延时
      setTimeout(() => {
        let res = {

            categories: data.categories,
            series: [
              {
                name: "用水历史",
                data: data.series
              },
            ]
          };
        this.dataList = JSON.parse(JSON.stringify(res));
      }, 500);
    },
  }
};
</script>

<style scoped>
  /* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
  .charts-box {
    width: 100%;
    height: 220px;
  }
</style>
