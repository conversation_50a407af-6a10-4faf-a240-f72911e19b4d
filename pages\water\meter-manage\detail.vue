<!-- 册本详情 -->
<template>
  <view class="meter-main">
    <view class="search-box">
      <u-search v-model="queryParams.selectKey" :showAction="false" bgColor="#fff" placeholder="客户姓名、客户户号"
                @search="search"></u-search>
    </view>
    <view class="tongji-bg">
      <image src="/static/img/tj.jpg" style="width: 100%;"></image>
      <view class="tongji-lists">
        <view class="tj-list">
          <span>抄表总数</span>
          <label for="">{{ tjInfo.allCount }}</label>
        </view>
        <view class="tj-list">
          <span>已抄表</span>
          <label for="">{{ tjInfo.havingReadingCount }}</label>
        </view>
        <view class="tj-list">
          <span>未抄表</span>
          <label for="">{{ tjInfo.noReadingCount }}</label>
        </view>
      </view>
    </view>
    <view class="content-box">
      <view class="title-box">
        <view class="choose" @click="show = true">
          <span style="margin-right: 10rpx;">{{ cardName }}</span>
          <u-icon color="#1172FD" name="arrow-down"></u-icon>

        </view>
        <view class="sort">
          <view :class="readingStatus===0?'active':''" @click="chose(0)">
            未抄表
          </view>
          <view :class="readingStatus==1?'active':''" @click="chose(1)">
            已抄表
          </view>
        </view>
      </view>

      <view v-for="(item,index) in list" :key="index" class="list" @click="navTo(item)">
        <view class="top">
          <view class="left">
            <!-- 		<em></em> -->
            <span>{{ item.userName || '暂无' }}</span>
          </view>
          <view v-if="item.readingStatus==0" class="right">
            未抄表
          </view>
          <view v-if="item.readingStatus==1" class="right right1">
            已抄表
          </view>
        </view>
        <view class="info-box">
          <view class="addr">
            <image mode="widthFix" src="/static/img/a1.png" style="width: 24rpx;"></image>
            <span>水表地址：</span>
            <label for="">{{ item.meterAddress || '暂无' }}</label>
          </view>
          <!-- <view class="info-list">
            <image src="/static/img/i2.png" mode="widthFix" style="width: 24rpx;"></image>
            <span>户主：</span>
            <label for="">{{item.userName||'暂无'}}</label>
          </view> -->
          <view class="info-list">
            <image mode="widthFix" src="/static/img/i2.png" style="width: 24rpx;"></image>
            <span>用水量：</span>
            <label for="">{{ item.waterConsumption || '暂无' }}</label>
          </view>
          <view class="info-list">
            <image mode="widthFix" src="/static/img/i3.png" style="width: 24rpx;"></image>
            <span>上期读数：</span>
            <label for="">{{ item.previousReading || '暂无' }}</label>
          </view>
          <view class="info-list">
            <image mode="widthFix" src="/static/img/i3.png" style="width: 24rpx;"></image>
            <span>本期读数：</span>
            <label for="">{{ item.currentReading || '暂无' }} <b v-if="item.isAbnormal==1"
                                                                 style="color: #FD4141;">(异常)</b></label>
          </view>
        </view>
      </view>

    </view>
    <u-picker :columns="cardList" :show="show" keyName="volumeName" @cancel="cancel"
              @confirm="confirm"></u-picker>
  </view>
</template>
<script>
import {getVolumeCardList, getVolumesReadingStat, getWxVolumesReadingList} from "@/api/meter-manage";

export default {

  data() {
    return {
      list: [],
      info: null,
      tjInfo: {},
      readingStatus: '',
      show: false,
      cardList: [],
      cardName: '全部',

      queryParams: {
        volumeSchedulingId: '',
        readingStatus: '',
        cardSchedulingId: '',
        selectKey: ''
      }
    }
  },
  onLoad(options) {
    const queryString = options.item; // options.data是从url参数中获取的数据
    const item = JSON.parse(decodeURIComponent(queryString));
    this.queryParams.volumeSchedulingId = item.volumeSchedulingId || item.schedulingId
    this.getList()
    this.tongji()
    this.getVolumeCardList()
  },
  onShow() {
    this.getList()
  },
  methods: {
    chose(type) {
      this.readingStatus = type
      this.queryParams.readingStatus = type
      this.getList()
    },
    // tongji
    tongji() {
      const app = this
      getVolumesReadingStat(
          this.queryParams.volumeSchedulingId
      ).then(res => {
        app.tjInfo = res.data
      })
    },
    // 获取册本列表
    getList() {
      const app = this
      getWxVolumesReadingList(this.queryParams).then(res => {
        app.list = res.rows
      })
    },
    async getVolumeCardList() {
      const app = this
      const {rows} = await getVolumeCardList(this.queryParams.volumeSchedulingId)
      rows.unshift({
        volumeName: "全部",
        schedulingId: " ",
      })
      this.cardList.push(rows)
    },
    confirm(e) {
      this.show = false
      this.cardName = e.value[0].volumeName
      this.queryParams.cardSchedulingId = e.value[0].schedulingId
      this.getList()
    },
    cancel(e) {
      this.show = false
    },
    navTo(item) {
      // debugger
      // const form = {
      //   meterNum: item.meterNum,
      //   volumeSchedulingId: item.volumeSchedulingId
      // }
      // const queryString = encodeURIComponent(JSON.stringify(form));
      uni.navigateTo({
        url: '/pages/water/meter-manage/read-index?meterNum=' + item.meterNum + '&volumeSchedulingId=' + item.volumeSchedulingId,
      })
    },
    search(e) {

    }
  },
};
</script>
<style lang="scss" scoped>
.meter-main {
  width: 100vw;
  min-height: 100vh;
  background-color: #EFF3F9;
  padding: 24rpx;
  box-sizing: border-box;

  .search-box {
    width: 100%;
    margin-bottom: 20rpx;
  }

  .tongji-bg {

    position: relative;
    width: 100%;
    height: 154rpx;
    margin-bottom: 50rpx;

    image {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
    }

    .tongji-lists {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      height: 100%;
      padding: 20rpx;
      box-sizing: border-box;

      .tj-list {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 25%;

        span {
          font-size: 28rpx;
          font-weight: normal;
          color: #0C71FF;
        }

        label {
          font-size: 40rpx;
          font-weight: 500;
          color: #5677FC;
        }
      }
    }
  }

  .content-box {
    .title-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .choose {
        // width: 224rpx;
        height: 68rpx;
        background: #FFFFFF;
        box-shadow: 5rpx 3rpx 12rpx 0rpx rgba(156, 176, 221, 0.34);
        border-radius: 34rpx;
        padding: 0 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        color: #1172FD;
      }


      .sort {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 400;
        color: #616E83;
        width: 242rpx;
        height: 68rpx;
        background: #FFFFFF;
        box-shadow: 5rpx 3rpx 12rpx 0rpx rgba(156, 176, 221, 0.34);
        border-radius: 34rpx;

        view {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 112rpx;
          height: 52rpx;
        }

        .active {

          width: 112rpx;
          height: 52rpx;
          background: #E2EBFA;
          border-radius: 26rpx;
          color: #1172FD;
        }
      }

    }

    .list {
      position: relative;
      padding: 20rpx 15rpx;
      background-color: #fff;
      margin-bottom: 15rpx;
      box-sizing: border-box;

      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          display: flex;
          align-items: center;

          em {
            display: block;
            width: 8rpx;
            height: 28rpx;
            background: #1172FD;
            margin-right: 20rpx;
          }

          span {
            color: #8A9095;
            font-size: 30rpx;
            font-weight: 600;
            color: #000000;
          }
        }

        .right {
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: 0;
          top: 0;
          font-size: 22rpx;
          font-weight: 400;
          color: #FD4141;
          width: 102rpx;
          height: 40rpx;
          background: #FDDDE1;
          border-radius: 0rpx 16rpx 0rpx 16rpx;
        }

        .right1 {

          background: #DDEAFD;

          color: #1172FD;
        }
      }

      .info-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .addr {
          width: 100%;
          margin-bottom: 10rpx;

          span {
            color: #4D4D4D;
            font-size: 24rpx;
            margin-left: 8rpx;
          }

          label {
            color: #999999;
            font-size: 24rpx;
            margin-left: 8rpx;
          }
        }

        .info-list {
          width: 50%;
          margin-bottom: 10rpx;

          span {
            color: #4D4D4D;
            font-size: 24rpx;
            margin-left: 8rpx;
          }

          label {
            color: #0C71FF;
            font-size: 24rpx;
            margin-left: 8rpx;
            display: inline-flex;
            align-items: center;

          }
        }
      }

      .remark {
        border-top: 2rpx dotted #D2D2D2;
        padding-top: 18rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #999999;
        line-height: 1.5;
        letter-spacing: 1rpx;;

      }


    }
  }
}
</style>
