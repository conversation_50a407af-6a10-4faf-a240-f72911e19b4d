<template>
	<view>
		<view class="line">
			<text class="title">{{title}}</text>
			<u-radio-group class="radioGroup" v-model="value" placement="row" @change="groupChange">
				<u-radio :customStyle="{marginTop: '8px',marginBottom: '8px',marginRight: '8px'}"
					v-for="(item, index) in choices" :key="index" :label="item.name" :name="item.name"
					@change="radioChange">
				</u-radio>
			</u-radio-group>
		</view>

	</view>
</template>

<script>
	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => "请选择"
			},

			choices: {
				type: Array,
				default () {
					return []
				}
			}
		},


		methods: {
			groupChange(n) {
				// console.log('groupChange', n);
			},
			radioChange(n) {
				// console.log('radioChange', n);
				this.value = n;
				this.$emit('update:msg', n);
				this.$emit('changeShutDown');
			}

		}
	}
</script>

<style lang="scss" scoped>
	.radioGroup {
		background-color: transparent;
		display: flex;
		justify-content: flex-end; //水平方向右对齐
	}
</style>