# 抄表导航组件使用说明

## 功能概述

这个导航组件为抄表员提供了完整的导航功能，包括：

1. **抄表员定位**：自动获取抄表员当前位置
2. **水表定位**：在地图上显示目标水表位置
3. **路线规划**：规划从抄表员位置到水表位置的路线
4. **外部导航**：支持调用外部地图应用进行导航

## 主要功能

### 1. 位置显示
- 自动获取抄表员当前GPS位置
- 在地图上显示抄表员位置标记（蓝色图标）
- 在地图上显示水表位置标记（绿色图标）

### 2. 路线规划
- 自动计算两点间的直线距离
- 在地图上绘制路线（蓝色线条）
- 显示预估距离信息
- 自动调整地图视野以显示完整路线

### 3. 导航控制
- **开始导航**：调用外部地图应用进行实际导航
- **重新规划**：重新获取位置并更新路线
- **清除路线**：清除当前显示的路线

### 4. 地图操作
- 支持地图缩放、拖拽、旋转
- 定位按钮：快速回到当前位置
- 关闭按钮：关闭导航弹窗

## 使用方法

### 1. 组件引入

```vue
<template>
  <view>
    <!-- 你的页面内容 -->
    <button @click="openNavigation">水表定位</button>
    
    <!-- 导航组件 -->
    <navigation ref="navigation"></navigation>
  </view>
</template>

<script>
import navigation from './navigation.vue';

export default {
  components: {
    navigation
  },
  methods: {
    openNavigation() {
      // 调用导航组件
      this.$refs.navigation.openEvent(meterData);
    }
  }
};
</script>
```

### 2. 数据格式

传入 `openEvent` 方法的水表数据格式：

```javascript
const meterData = {
  meterLongitude: '112.5384',  // 水表经度（必需）
  meterLatitude: '37.8756',    // 水表纬度（必需）
  meterAddress: '水表具体地址', // 水表地址（可选，用于显示）
  // 其他水表相关数据...
};
```

### 3. 调用示例

```javascript
// 在需要打开导航的地方调用
openMeterNavigation() {
  const meterInfo = {
    meterLongitude: '112.5384',
    meterLatitude: '37.8756',
    meterAddress: '山西省太原市小店区某某小区1号楼'
  };
  
  // 打开导航
  this.$refs.navigation.openEvent(meterInfo);
}
```

## 技术特性

### 1. 坐标系统
- 使用 GCJ02 坐标系（国测局坐标系）
- 支持 WGS84 到 GCJ02 的坐标转换
- 兼容高德地图坐标系统

### 2. 外部地图支持
- **App环境**：支持调用高德地图、百度地图App
- **H5环境**：支持打开网页版地图
- 自动检测并选择可用的地图应用

### 3. 距离计算
- 使用 Haversine 公式计算球面距离
- 精确计算两点间的实际距离
- 支持米和公里单位自动转换

### 4. 响应式设计
- 适配不同屏幕尺寸
- 支持横竖屏切换
- 优化的触摸交互体验

## 权限要求

### 1. 位置权限
```json
// manifest.json 中需要配置位置权限
"permissions": {
  "android.permission.ACCESS_FINE_LOCATION": {},
  "android.permission.ACCESS_COARSE_LOCATION": {}
}
```

### 2. 网络权限
```json
// 用于地图数据加载
"permissions": {
  "android.permission.INTERNET": {},
  "android.permission.ACCESS_NETWORK_STATE": {}
}
```

## 注意事项

1. **位置精度**：建议在室外使用以获得更好的GPS定位精度
2. **网络连接**：需要网络连接来加载地图数据
3. **地图应用**：外部导航功能需要设备安装相应的地图应用
4. **坐标准确性**：确保传入的水表坐标数据准确无误

## 错误处理

组件内置了完善的错误处理机制：

- 位置获取失败时的提示
- 坐标数据不完整时的验证
- 外部地图调用失败时的备用方案
- 网络异常时的友好提示

## 自定义配置

可以通过修改组件内的配置来自定义：

- 地图初始缩放级别
- 标记点图标样式
- 路线颜色和宽度
- 按钮样式和位置

## 性能优化

- 使用防抖机制避免频繁的位置更新
- 智能的地图视野调整算法
- 优化的标记点渲染性能
- 内存泄漏防护机制
