<template>
  <view class="meter-main">
    <image class="meter-bg" mode="widthFix" src="/static/img/meter-bg.png"></image>
    <Navbar :h5Show="true" bgColor="#4582FD" title="抄表管理"></Navbar>
    <view class="content-box">
      <view class="top-box">
        <u-search v-model="keyword" :showAction="false" bgColor="rgba(243,246,252,0.63)"
                  placeholder="请输入楼栋名称" @blur="search" @search="search"></u-search>
        <!-- <text class="title1">{{info.districtName}}</text> -->
        <text class="title1">{{ info.volumeNum }}</text>
        <!-- <span>地址：{{info.addr.replace(/\//g, '')}}</span> -->
        <span>册本名称：{{ info.volumeName }}</span>
      </view>

      <view class="table-box">
        <view class="table-thbox">
          <view class="th-list">抄表卡</view>
          <view class="th-list">用户名</view>
          <view class="th-list">状态</view>
        </view>
        <view class="table-body">
          <view class="table-left">
            <scroll-view scroll-y="true" style="height: 60vh;">
              <view v-for="(item,index) in  listData"
                    :key="index" :class="ischoose==index?'table-left-list table-left-list-active':'table-left-list'"
                    @click="onchange(index)">
                {{ index }}
              </view>
            </scroll-view>
          </view>
          <view class="table-right">
            <scroll-view scroll-y="true" style="height: 60vh;">
              <view v-for="(item,index) in  chooseList" :key="index" class="table-right-list" @click="navTo(item)">
                <text>{{ item.userName }}</text>
                <!-- recordStatus 修改为 readingStatus -->
                <text :class="{'active': item.readingStatus==0, 'color2': item.readingStatus==3}">
                  {{ item.readingStatus == 0 ? '未抄' : item.readingStatus == 1 ? '已抄' : '待复查' }}
                </text>
              </view>
            </scroll-view>
          </view>
        </view>

      </view>
    </view>
  </view>
</template>
<script>
// import Navbar from '@/components/navbar/Navbar'
import Navbar from './navbar/Navbar.vue'
// import * as MeterApi from '@/api/meter-manage/meter.js'
import {getDistrictReadingList} from '@/api/meter-manage/meter.js'

export default {
  components: {
    Navbar,
  },
  data() {
    return {
      listData: [],
      chooseList: [],
      ischoose: '',
      meterVolumesNum: '',
      info: null,
      keyword: ''

    };
  },
  onLoad(options) {
    const queryString = options.item; // options.data是从url参数中获取的数据
    const item = JSON.parse(decodeURIComponent(queryString));
    console.log(item)
    // this.meterVolumesNum=options.meterVolumesNum
    this.info = item
    console.log("当前页面参数：", this.info);
    this.getList(this.info)
  },
  onShow() {
    this.getList(this.info);
  },
  methods: {
    search() {
      this.getList(this.info)
    },
    onchange(index) {
      this.ischoose = index
      this.chooseList = this.listData[index]
    },
    getList(item) {
      const app = this
      /*
        {
          district: item.district,
          meterVolumesNum:this.meterVolumesNum,
          buildingUnit:this.keyword
        }
      */
      getDistrictReadingList(this.info.schedulingId).then(res => {

        app.listData = res.data
        console.log("当前页面数据", this.listData);
        // console.log(Object.keys(res.data)[0])
        app.ischoose = Object.keys(res.data)[0]
        app.chooseList = res.data[Object.keys(res.data)[0]]
        // console.log(app.chooseList)
        if (app.chooseList == undefined) {
          app.chooseList = []
        }
        // console.log("左侧数据",this.listData);
      })
    },
    navTo(item) {
      console.log(item)
      // console.log("1",this.meterVolumesNum)
      // console.log("2",item.userAccount)
      // console.log("3",this.info.district)
      // cardSchedulingId
      // meterNum
      // volumeSchedulingId
      uni.navigateTo({
        url: '/pages/water/meter-manage/read-index?cardSchedulingId=' + item.cardSchedulingId + '&meterNum=' + item.meterNum + '&volumeSchedulingId=' + item.volumeSchedulingId
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.meter-main {
  overflow-y: hidden;
  height: 100vh;

  .meter-bg {
    position: absolute;
    // top: 140rpx;
    top: -30rpx;
    left: 0;
    width: 100%;
    z-index: 40;
  }

  .content-box {
    position: relative;
    z-index: 42;

    .top-box {
      padding: 24rpx;

      .title1 {
        display: block;
        font-size: 54rpx;
        font-weight: 500;
        color: #275EEE;
        line-height: 56rpx;
        margin-top: 32rpx;
        margin-left: 20rpx;
      }

      span {
        margin-top: 22rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #00044B;
        line-height: 56rpx;
        margin-left: 20rpx;
      }
    }

    .table-box {
      width: 750rpx;
      min-height: 1182rpx;
      background: #FFFFFF;
      border-radius: 24rpx 24rpx 0rpx 0rpx;

      .table-thbox {
        display: flex;
        align-items: center;

        .th-list {
          width: 33.3%;
          height: 80rpx;
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          font-size: 26rpx;
          font-weight: 400;
          color: #000000;
        }
      }

      .table-body {
        display: flex;
        align-items: flex-start;
        justify-content: center;

        .table-left {
          width: 33.3%;
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          border-right: 2rpx solid #EFF3F9;

          .table-left-list {
            height: 80rpx;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            color: '#5A5D60';
            font-size: 26rpx;

          }

          .table-left-list-active {
            color: #1172FD;
          }
        }

        .table-right {
          width: 66.6%;

          .table-right-list {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 80rpx;
            border-bottom: 2rpx solid #EFF3F9;

            text:nth-child(1) {
              display: block;
              width: 50%;
              // display: flex;
              // align-items: center;
              // justify-content: center;
              color: '#5A5D60';
              font-size: 20rpx;
              padding-left: 15rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              // background-color: pink;
            }

            text:nth-child(2) {
              display: block;
              width: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: '#5A5D60';
              font-size: 20rpx;
              // background-color: yellow;
            }

            .active {
              color: #1DD9CE;
            }

            .color2 {
              color: red;
            }
          }
        }
      }
    }
  }
}
</style>
