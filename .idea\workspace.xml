<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e7150827-83fa-47e0-bf2f-bf67acbf526e" name="更改" comment="抄表">
      <change beforePath="$PROJECT_DIR$/.hbuilderx/launch.json" beforeDir="false" afterPath="$PROJECT_DIR$/.hbuilderx/launch.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/androidPrivacy.json" beforeDir="false" afterPath="$PROJECT_DIR$/androidPrivacy.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/api/meter-manage/http/config.js" beforeDir="false" afterPath="$PROJECT_DIR$/api/meter-manage/http/config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/tabs/main.vue" beforeDir="false" afterPath="$PROJECT_DIR$/pages/tabs/main.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/water/meter-manage/navigation.vue" beforeDir="false" afterPath="$PROJECT_DIR$/pages/water/meter-manage/navigation.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/water/meter-manage/navigation1.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/static/icons/user-location.svg" beforeDir="false" afterPath="$PROJECT_DIR$/static/icons/user-location.svg" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FormatOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="OptimizeOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zROSyfcSSY1dxoxp125E6SxY9l" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;UniApp.dev.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/java项目/智慧水务/zg-zhsw/zhsw_zg_new/shanxi-yingxiao/pages/water/meter-manage/utils&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;rearrange.code.on.save&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ\\WebStorm\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\java项目\智慧水务\zg-zhsw\zhsw_zg_new\shanxi-yingxiao\pages\water\meter-manage\utils" />
      <recent name="D:\java项目\智慧水务\zg-zhsw\zhsw_zg_new\shanxi-yingxiao\pages\water\meter-manage" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="dev" type="AppRunConfiguration" factoryName="AppRunConfiguration">
      <option name="mpDescribeName" />
      <option name="mpIdName" />
      <option name="mpKeyName" />
      <option name="mpVersionName" />
      <option name="scriptName" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-WS-251.25410.117" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e7150827-83fa-47e0-bf2f-bf67acbf526e" name="更改" comment="" />
      <created>1751690423636</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751690423636</updated>
      <workItem from="1751690425121" duration="71000" />
      <workItem from="1751690507777" duration="591000" />
      <workItem from="1751761481607" duration="1662000" />
      <workItem from="1751763671009" duration="1542000" />
      <workItem from="1751765481396" duration="46000" />
      <workItem from="1751765558786" duration="66000" />
      <workItem from="1751767100293" duration="8635000" />
      <workItem from="1751845993430" duration="4836000" />
      <workItem from="1751855950997" duration="82000" />
      <workItem from="1751876111371" duration="13920000" />
      <workItem from="1751932017794" duration="1758000" />
      <workItem from="1751938856153" duration="6463000" />
      <workItem from="1751945442878" duration="2795000" />
      <workItem from="1752236428208" duration="969000" />
      <workItem from="1752285415289" duration="2924000" />
      <workItem from="1752307009285" duration="1633000" />
      <workItem from="1752309230760" duration="3082000" />
      <workItem from="1752313781574" duration="66000" />
      <workItem from="1752313862296" duration="61000" />
      <workItem from="1752314071524" duration="149000" />
      <workItem from="1752314370905" duration="268000" />
      <workItem from="1752314651745" duration="79000" />
      <workItem from="1752315437990" duration="3339000" />
    </task>
    <task id="LOCAL-00001" summary="抄表">
      <option name="closed" value="true" />
      <created>1751849865745</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751849865745</updated>
    </task>
    <task id="LOCAL-00002" summary="抄表">
      <option name="closed" value="true" />
      <created>1751850783440</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751850783440</updated>
    </task>
    <task id="LOCAL-00003" summary="抄表">
      <option name="closed" value="true" />
      <created>1751932163281</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751932163281</updated>
    </task>
    <task id="LOCAL-00004" summary="抄表">
      <option name="closed" value="true" />
      <created>1751932236858</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751932236858</updated>
    </task>
    <task id="LOCAL-00005" summary="抄表">
      <option name="closed" value="true" />
      <created>1751945193769</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751945193769</updated>
    </task>
    <task id="LOCAL-00006" summary="抄表">
      <option name="closed" value="true" />
      <created>1751945227222</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751945227222</updated>
    </task>
    <task id="LOCAL-00007" summary="抄表">
      <option name="closed" value="true" />
      <created>1751948128368</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751948128368</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="抄表" />
    <option name="LAST_COMMIT_MESSAGE" value="抄表" />
  </component>
</project>