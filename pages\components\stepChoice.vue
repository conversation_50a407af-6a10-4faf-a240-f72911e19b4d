<!-- 多选 -->
<template>
	<view>
		<stepChoiceComponent ref='stepChoice' :mChoices="choices" @select="successStepChoice"
			@selectId="successStepChoiceId">
		</stepChoiceComponent>


		<u-cell :value="value" v-model="value" :isLink="true" @click="open()">
			<view slot="title" class="u-slot-title">
				<text class="u-cell-text">{{title}}</text>
			</view>
		</u-cell>
	</view>
</template>

<script>
	import stepChoiceComponent from '@/components/stepChoice/stepChoiceComponent.vue'


	export default {
		props: {
			title: {
				type: String,
				default: () => ""
			},
			value: {
				type: String,
				default: () => "请选择"
			},


			choices: {
				type: Array,
				default () {
					return {}
				}
			},
			checkboxValue1: [],

		},
		data() {
			return {
				choiceId: "",
				showPop: false,
				chosen: []
			}
		},
		components: {
			"stepChoiceComponent": stepChoiceComponent
		},
		methods: {
			successStepChoice(choice) {
				console.log("stepChoice back:" + choice)
				var index = choice.lastIndexOf(";") + 1
				console.log(" choice.sub(index):" + choice.substr(index))
				this.value = choice.substr(index)
			},

			successStepChoiceId(choiceId) {
				console.log("stepChoiceId back:" + choiceId)
				this.$emit("selectId",choiceId)
			},
			close() {

				this.show = false

			},
			open() {
				console.log('open');
				this.$refs.stepChoice.show()
			},
		}
	}
</script>

<style lang="scss" scoped>


</style>