/* required styles */

.leaflet-pane,
.leaflet-tile,
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-tile-container,
.leaflet-pane > svg,
.leaflet-pane > canvas,
.leaflet-zoom-box,
.leaflet-zoom-animated,
.leaflet-map-pane,
.leaflet-tile-pane,
.leaflet-overlay-pane,
.leaflet-shadow-pane,
.leaflet-marker-pane,
.leaflet-tooltip-pane,
.leaflet-popup-pane,
.leaflet-map-pane canvas,
.leaflet-map-pane svg {
	position: absolute;
	left: 0;
	top: 0;
	}
.leaflet-tile {
	width: 256px;
	height: 256px;
	}
.leaflet-marker-icon,
.leaflet-marker-shadow {
	-webkit-user-select: none;
	   -moz-user-select: none;
	        user-select: none;
	-webkit-user-drag: none;
	}
/* map is broken in FF if you have max-width: 100% on tiles */
.leaflet-tile-container img {
	max-width: none !important;
	}
/* hack for https://github.com/Leaflet/Leaflet/issues/1178 */
.leaflet-zoom-box {
	width: 0;
	height: 0;
	-moz-box-sizing: content-box;
	     box-sizing: content-box;
	z-index: 800;
	}
/* workaround for https://bugzilla.mozilla.org/show_bug.cgi?id=888319 */
.leaflet-overlay-pane svg,
.leaflet-overlay-pane canvas {
	-webkit-user-select: none;
	   -moz-user-select: none;
	        user-select: none;
	}

.leaflet-container {
	background: #ddd;
	outline: 0;
	}
.leaflet-container a {
	color: #0078A8;
	}
.leaflet-container a.leaflet-active {
	outline: 2px solid orange;
	}
.leaflet-zoom-animated {
	-webkit-transform-origin: 0 0;
	    -ms-transform-origin: 0 0;
	        transform-origin: 0 0;
	}
.leaflet-zoom-animated .leaflet-tile-container {
	will-change: transform;
	}
.leaflet-zoom-animated .leaflet-zoom-hide {
	visibility: hidden;
	}

.leaflet-fade-anim .leaflet-tile {
	will-change: opacity;
	}
.leaflet-fade-anim .leaflet-map-pane .leaflet-tile {
	visibility: hidden;
	}

.leaflet-tile-loaded {
	visibility: inherit;
	}

.leaflet-control {
	position: relative;
	z-index: 800;
	pointer-events: auto;
	float: left;
	clear: both;
	}
.leaflet-top,
.leaflet-bottom {
	position: absolute;
	z-index: 1000;
	pointer-events: none;
	}
.leaflet-top {
	top: 0;
	}
.leaflet-right {
	right: 0;
	}
.leaflet-bottom {
	bottom: 0;
	}
.leaflet-left {
	left: 0;
	}
.leaflet-control {
	margin-bottom: 10px;
	}
.leaflet-right .leaflet-control {
	margin-right: 10px;
	}
.leaflet-left .leaflet-control {
	margin-left: 10px;
	}
.leaflet-top .leaflet-control {
	margin-top: 10px;
	}

.leaflet-control-zoom-in, .leaflet-control-zoom-out {
	font: bold 18px 'Lucida Console', Monaco, monospace;
	text-indent: 1px;
	}
.leaflet-control-zoom-in, .leaflet-control-zoom-out {
	display: block;
	width: 30px;
	height: 30px;
	background-color: #fff;
	border: 1px solid #ccc;
	border-bottom: 1px solid #bbb;
	border-radius: 4px;
	text-align: center;
	line-height: 30px;
	color: #333;
	text-decoration: none;
	}
.leaflet-control-zoom-in:hover, .leaflet-control-zoom-out:hover {
	background-color: #f4f4f4;
	}
.leaflet-control-zoom-in {
	border-bottom: 1px solid #ccc;
	border-radius: 4px 4px 0 0;
	}
.leaflet-control-zoom-out {
	border-radius: 0 0 4px 4px;
	}

.leaflet-control-layers {
	box-shadow: 0 1px 5px rgba(0,0,0,0.4);
	background: #fff;
	border-radius: 5px;
	}
.leaflet-control-layers-toggle {
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAABjElEQVRIS+2VvUvDUBTGP9M/iB+4eJBEcPAgIji4+AEOji5O4qLg4qDu4uDgoDsXJ3Fxk+A/8A8ODk4OIk7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4-AAAABJRU5ErkJggg==);
	width: 36px;
	height: 36px;
	}
.leaflet-retina .leaflet-control-layers-toggle {
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAACxElEQVRoQ+2YvUvDUBTGP9M/iB+4eJBEcPAgIji4+AEOji5O4qLg4qDu4uDgoDsXJ3Fxk+A/8A8ODk4OIk7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4iA4iA7i4-AAAABJRU5ErkJggg==);
	background-size: 26px 26px;
	}
.leaflet-control-layers .leaflet-control-layers-list,
.leaflet-control-layers-expanded .leaflet-control-layers-toggle {
	display: none;
	}
.leaflet-control-layers-expanded .leaflet-control-layers-list {
	display: block;
	position: relative;
	}
.leaflet-control-layers-expanded {
	padding: 6px 10px 6px 6px;
	color: #333;
	background: #fff;
	}
.leaflet-control-layers-scrollbar {
	overflow-y: scroll;
	padding-right: 5px;
	}
.leaflet-control-layers-selector {
	margin-top: 2px;
	position: relative;
	float: left;
	}
.leaflet-control-layers label {
	display: block;
	}
.leaflet-control-layers-separator {
	height: 0;
	border-top: 1px solid #ddd;
	margin: 5px -10px 5px -6px;
	}

.leaflet-container .leaflet-control-attribution {
	background: #fff;
	background: rgba(255, 255, 255, 0.8);
	margin: 0;
	}
.leaflet-control-attribution,
.leaflet-control-scale-line {
	padding: 0 5px;
	color: #333;
	}
.leaflet-control-attribution a {
	text-decoration: none;
	}
.leaflet-control-attribution a:hover {
	text-decoration: underline;
	}
.leaflet-container .leaflet-control-attribution,
.leaflet-container .leaflet-control-scale {
	font-size: 11px;
	}
.leaflet-left .leaflet-control-scale {
	margin-left: 5px;
	}
.leaflet-bottom .leaflet-control-scale {
	margin-bottom: 5px;
	}
.leaflet-control-scale-line {
	border: 2px solid #777;
	border-top: none;
	line-height: 1.1;
	padding: 2px 5px 1px;
	font-size: 11px;
	white-space: nowrap;
	overflow: hidden;
	-moz-box-sizing: content-box;
	     box-sizing: content-box;

	background: #fff;
	background: rgba(255, 255, 255, 0.5);
	}
.leaflet-control-scale-line:not(:first-child) {
	border-top: 2px solid #777;
	border-top: none;
	margin-top: -2px;
	}
.leaflet-control-scale-line:not(:first-child):not(:last-child) {
	border-bottom: 2px solid #777;
	}

.leaflet-touch .leaflet-control-layers-toggle {
	width: 44px;
	height: 44px;
	}
.leaflet-touch .leaflet-control-zoom-in, .leaflet-touch .leaflet-control-zoom-out {
	width: 30px;
	height: 30px;
	line-height: 30px;
	font-size: 22px;
	}

.leaflet-popup {
	position: absolute;
	text-align: center;
	margin-bottom: 20px;
	}
.leaflet-popup-content-wrapper {
	padding: 1px;
	text-align: left;
	border-radius: 12px;
	}
.leaflet-popup-content {
	margin: 13px 24px;
	line-height: 1.4;
	font-size: 12px;
	}
.leaflet-popup-content p {
	margin: 18px 0;
	}
.leaflet-popup-tip-container {
	width: 40px;
	height: 20px;
	position: absolute;
	left: 50%;
	margin-left: -20px;
	overflow: hidden;
	pointer-events: none;
	}
.leaflet-popup-tip {
	width: 17px;
	height: 17px;
	padding: 1px;

	margin: -10px auto 0;

	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	}
.leaflet-popup-content-wrapper, .leaflet-popup-tip {
	background: white;
	color: #333;
	box-shadow: 0 3px 14px rgba(0,0,0,0.4);
	}
.leaflet-container a.leaflet-popup-close-button {
	position: absolute;
	top: 0;
	right: 0;
	padding: 8px 8px 0 0;
	border: none;
	text-align: center;
	width: 24px;
	height: 24px;
	font: 20px/24px 'Helvetica Neue', Arial, Helvetica, sans-serif;
	color: #777;
	text-decoration: none;
	font-weight: bold;
	background: transparent;
	}
.leaflet-container a.leaflet-popup-close-button:hover {
	color: #555;
	}
.leaflet-popup-scrolled {
	overflow: auto;
	border-bottom: 1px solid #ddd;
	border-top: 1px solid #ddd;
	}

.leaflet-div-icon {
	background: #fff;
	border: 1px solid #666;
	}

.leaflet-tooltip {
	position: absolute;
	padding: 6px;
	background-color: #fff;
	border: 1px solid #fff;
	border-radius: 3px;
	color: #222;
	white-space: nowrap;
	-webkit-user-select: none;
	   -moz-user-select: none;
	        user-select: none;
	pointer-events: none;
	box-shadow: 0 1px 3px rgba(0,0,0,0.4);
	}
.leaflet-tooltip.leaflet-clickable {
	cursor: pointer;
	pointer-events: auto;
	}
.leaflet-tooltip-top:before, .leaflet-tooltip-bottom:before, .leaflet-tooltip-left:before, .leaflet-tooltip-right:before {
	position: absolute;
	pointer-events: none;
	border: 6px solid transparent;
	background: transparent;
	content: "";
	}

.leaflet-tooltip-bottom {
	margin-top: 6px;
	}
.leaflet-tooltip-top {
	margin-top: -6px;
	}
.leaflet-tooltip-bottom:before, .leaflet-tooltip-top:before {
	left: 50%;
	margin-left: -6px;
	}
.leaflet-tooltip-top:before {
	bottom: 0;
	margin-bottom: -12px;
	border-top-color: #fff;
	}
.leaflet-tooltip-bottom:before {
	top: 0;
	margin-top: -12px;
	margin-left: -6px;
	border-bottom-color: #fff;
	}
.leaflet-tooltip-left {
	margin-left: -6px;
	}
.leaflet-tooltip-right {
	margin-left: 6px;
	}
.leaflet-tooltip-left:before, .leaflet-tooltip-right:before {
	top: 50%;
	margin-top: -6px;
	}
.leaflet-tooltip-left:before {
	right: 0;
	margin-right: -12px;
	border-left-color: #fff;
	}
.leaflet-tooltip-right:before {
	left: 0;
	margin-left: -12px;
	border-right-color: #fff;
	}

.leaflet-marker-icon {
	cursor: pointer;
	}
.leaflet-marker-pane,
.leaflet-popup-pane {
	z-index: 650;
	}
.leaflet-marker-icon,
.leaflet-marker-shadow {
	display: block;
	}

/* IE 6-8 filters for marker shadows */
.leaflet-oldie .leaflet-marker-shadow {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=60) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand')";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=60) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand');
	}

.leaflet-oldie .leaflet-tile-pane {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=0)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=0);
	}
.leaflet-oldie .leaflet-map-pane .leaflet-tile-loaded {
	-ms-filter: "";
	filter: none;
	}

.leaflet-oldie .leaflet-popup-content-wrapper {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}
.leaflet-oldie .leaflet-popup-tip {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand')";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand');
	}

.leaflet-oldie .leaflet-control-zoom-in, .leaflet-oldie .leaflet-control-zoom-out {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}

.leaflet-oldie .leaflet-control-layers {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}

.leaflet-oldie .leaflet-div-icon {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}

.leaflet-control-attribution,
.leaflet-control-scale-line {
	padding: 0 5px;
	color: #333;
	}
.leaflet-control-attribution a {
	text-decoration: none;
	}
.leaflet-control-attribution a:hover {
	text-decoration: underline;
	}
.leaflet-container .leaflet-control-attribution,
.leaflet-container .leaflet-control-scale {
	font-size: 11px;
	}
.leaflet-left .leaflet-control-scale {
	margin-left: 5px;
	}
.leaflet-bottom .leaflet-control-scale {
	margin-bottom: 5px;
	}
.leaflet-control-scale-line {
	border: 2px solid #777;
	border-top: none;
	line-height: 1.1;
	padding: 2px 5px 1px;
	font-size: 11px;
	white-space: nowrap;
	overflow: hidden;
	-moz-box-sizing: content-box;
	     box-sizing: content-box;

	background: #fff;
	background: rgba(255, 255, 255, 0.5);
	}
.leaflet-control-scale-line:not(:first-child) {
	border-top: 2px solid #777;
	border-top: none;
	margin-top: -2px;
	}
.leaflet-control-scale-line:not(:first-child):not(:last-child) {
	border-bottom: 2px solid #777;
	}

.leaflet-touch .leaflet-control-layers-toggle {
	width: 44px;
	height: 44px;
	}
.leaflet-touch .leaflet-control-zoom-in, .leaflet-touch .leaflet-control-zoom-out {
	width: 30px;
	height: 30px;
	line-height: 30px;
	font-size: 22px;
	}

.leaflet-popup {
	position: absolute;
	text-align: center;
	margin-bottom: 20px;
	}
.leaflet-popup-content-wrapper {
	padding: 1px;
	text-align: left;
	border-radius: 12px;
	}
.leaflet-popup-content {
	margin: 13px 24px;
	line-height: 1.4;
	font-size: 12px;
	}
.leaflet-popup-content p {
	margin: 18px 0;
	}
.leaflet-popup-tip-container {
	width: 40px;
	height: 20px;
	position: absolute;
	left: 50%;
	margin-left: -20px;
	overflow: hidden;
	pointer-events: none;
	}
.leaflet-popup-tip {
	width: 17px;
	height: 17px;
	padding: 1px;

	margin: -10px auto 0;

	-webkit-transform: rotate(45deg);
	    -ms-transform: rotate(45deg);
	        transform: rotate(45deg);
	}
.leaflet-popup-content-wrapper, .leaflet-popup-tip {
	background: white;
	color: #333;
	box-shadow: 0 3px 14px rgba(0,0,0,0.4);
	}
.leaflet-container a.leaflet-popup-close-button {
	position: absolute;
	top: 0;
	right: 0;
	padding: 8px 8px 0 0;
	border: none;
	text-align: center;
	width: 24px;
	height: 24px;
	font: 20px/24px 'Helvetica Neue', Arial, Helvetica, sans-serif;
	color: #777;
	text-decoration: none;
	font-weight: bold;
	background: transparent;
	}
.leaflet-container a.leaflet-popup-close-button:hover {
	color: #555;
	}
.leaflet-popup-scrolled {
	overflow: auto;
	border-bottom: 1px solid #ddd;
	border-top: 1px solid #ddd;
	}

.leaflet-div-icon {
	background: #fff;
	border: 1px solid #666;
	}

.leaflet-tooltip {
	position: absolute;
	padding: 6px;
	background-color: #fff;
	border: 1px solid #fff;
	border-radius: 3px;
	color: #222;
	white-space: nowrap;
	-webkit-user-select: none;
	   -moz-user-select: none;
	        user-select: none;
	pointer-events: none;
	box-shadow: 0 1px 3px rgba(0,0,0,0.4);
	}
.leaflet-tooltip.leaflet-clickable {
	cursor: pointer;
	pointer-events: auto;
	}
.leaflet-tooltip-top:before, .leaflet-tooltip-bottom:before, .leaflet-tooltip-left:before, .leaflet-tooltip-right:before {
	position: absolute;
	pointer-events: none;
	border: 6px solid transparent;
	background: transparent;
	content: "";
	}

.leaflet-tooltip-bottom {
	margin-top: 6px;
	}
.leaflet-tooltip-top {
	margin-top: -6px;
	}
.leaflet-tooltip-bottom:before, .leaflet-tooltip-top:before {
	left: 50%;
	margin-left: -6px;
	}
.leaflet-tooltip-top:before {
	bottom: 0;
	margin-bottom: -12px;
	border-top-color: #fff;
	}
.leaflet-tooltip-bottom:before {
	top: 0;
	margin-top: -12px;
	margin-left: -6px;
	border-bottom-color: #fff;
	}
.leaflet-tooltip-left {
	margin-left: -6px;
	}
.leaflet-tooltip-right {
	margin-left: 6px;
	}
.leaflet-tooltip-left:before, .leaflet-tooltip-right:before {
	top: 50%;
	margin-top: -6px;
	}
.leaflet-tooltip-left:before {
	right: 0;
	margin-right: -12px;
	border-left-color: #fff;
	}
.leaflet-tooltip-right:before {
	left: 0;
	margin-left: -12px;
	border-right-color: #fff;
	}

.leaflet-marker-icon {
	cursor: pointer;
	}
.leaflet-marker-pane,
.leaflet-popup-pane {
	z-index: 650;
	}
.leaflet-marker-icon,
.leaflet-marker-shadow {
	display: block;
	}

/* IE 6-8 filters for marker shadows */
.leaflet-oldie .leaflet-marker-shadow {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=60) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand')";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=60) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand');
	}

.leaflet-oldie .leaflet-tile-pane {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=0)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=0);
	}
.leaflet-oldie .leaflet-map-pane .leaflet-tile-loaded {
	-ms-filter: "";
	filter: none;
	}

.leaflet-oldie .leaflet-popup-content-wrapper {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}
.leaflet-oldie .leaflet-popup-tip {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand')";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100) progid:DXImageTransform.Microsoft.Matrix(M11=0.7, M12=0, SizingMethod='auto expand');
	}

.leaflet-oldie .leaflet-control-zoom-in, .leaflet-oldie .leaflet-control-zoom-out {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}

.leaflet-oldie .leaflet-control-layers {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}

.leaflet-oldie .leaflet-div-icon {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(opacity=100)";
	filter: progid:DXImageTransform.Microsoft.Alpha(opacity=100);
	}