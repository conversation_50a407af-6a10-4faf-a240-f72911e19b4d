// 登录接口返回处理
import {
	request
} from './server.js'





export const login =
	function(params) {

		return request.post('/auth/login', params)
			.then(data => {
				// console.log("header:" + data.header.authorization);
				// uni.setStorageSync('token', data.header.authorization);
				// getApp().globalData.token = data.header.authorization;
				// console.log("get token:" + getApp().globalData.token)



				return data.data
			})

	}

// export const getCode =
// 	function(params) {
// 		return request.get('/info/getCode', params)
// 			.then(data => {
// 				return data.data
// 			})
// 	}

export const logout =
	function() {
		return request.post('/auth/logout')
			.then(data => {
				return data.data
			})
	}

export const webResetPassword =
	function(params) {
		return request.post('/login/webResetPassword', params)
			.then(data => {
				return data.data
			})
	}
export const updateCity =
	function(params) {
		return request.post('/user/updateCity', params)
			.then(data => {
				return data.data
			})
	}
//改成 用户登录接口与机遇sa-token的用户鉴权组件使用说明 7.5
// export const getInfo =
// 	function(params) {
// 		return request.post('/user/userInfoByUserId', params)
// 			.then(data => {
// 				return data.data
// 			})
// 	}
