<template>
    <view class="container">
        <!-- 顶部导航栏 -->
        <view class="header">
            <view class="nav-bar">
                <uni-icons type="left" size="20" color="#fff" @click="goBack"></uni-icons>
                <text class="title">隐患详情</text>
                <view style="width: 20px;"></view>
            </view>
        </view>

        <!-- Tab栏 -->
        <view class="tab-container">
            <view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }"
                @click="switchTab(index)">
                <text class="tab-text">{{ tab.name }}</text>
            </view>
        </view>

        <!-- 内容区域 -->
        <scroll-view class="content" scroll-y="true">
            <!-- 日志Tab -->
            <view v-if="activeTab === 0" class="log-content">
                <view v-for="(log, index) in logList" :key="index" class="log-item">
                    <view class="log-header">
                        <view class="log-user">
                            <uni-icons type="person" size="16" color="#1890ff"></uni-icons>
                            <text class="user-name">{{ log.user }}</text>
                        </view>
                        <text class="log-time">{{ log.time }}</text>
                    </view>
                    <view class="log-content-box">
                        <view class="log-type">【{{ log.type }}】</view>
                        <text class="log-description">{{ log.description }}</text>
                    </view>
                </view>
            </view>

            <!-- 详情Tab -->
            <view v-if="activeTab === 1" class="detail-content">
                <view class="detail-item">
                    <text class="detail-label">隐患编号</text>
                    <text class="detail-value">{{ dangerDetail.id }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">状态</text>
                    <text class="detail-value" :class="getStatusClass(dangerDetail.status)">{{ dangerDetail.status
                        }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">隐患来源</text>
                    <text class="detail-value">{{ dangerDetail.source }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">隐患类型</text>
                    <text class="detail-value">{{ dangerDetail.type }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">隐患描述</text>
                    <text class="detail-value">{{ dangerDetail.description }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">所属分区</text>
                    <text class="detail-value">{{ dangerDetail.area }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">上报时间</text>
                    <text class="detail-value">{{ dangerDetail.reportTime }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">上报人</text>
                    <text class="detail-value">{{ dangerDetail.reporter }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">负责人</text>
                    <text class="detail-value">{{ dangerDetail.handler }}</text>
                </view>
                <view class="detail-item">
                    <text class="detail-label">隐患地址</text>
                    <text class="detail-value">{{ dangerDetail.address }}</text>
                </view>

                <!-- 图片展示 -->
                <view v-if="dangerDetail.images && dangerDetail.images.length > 0" class="image-section">
                    <view class="image-grid">
                        <view v-for="(image, index) in dangerDetail.images" :key="index" class="image-item"
                            @click="previewImage(index)">
                            <image :src="image" class="danger-image" mode="aspectFill"></image>
                        </view>
                    </view>
                </view>
            </view>
        </scroll-view>

        <!-- 底部按钮 -->
        <view class="bottom-buttons">
            <button class="btn btn-secondary" @click="closeDanger">关闭</button>
            <button class="btn btn-primary" @click="handleDanger">隐患处理</button>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            activeTab: 0,
            tabs: [
                { name: '日志', key: 'log' },
                { name: '详情', key: 'detail' }
            ],
            dangerDetail: {
                id: 'DWRJ323131',
                status: '待处理',
                source: '巡检上报',
                type: '管道渗漏',
                description: '中电科西南200米管道轻微渗漏',
                area: '城南1区',
                reportTime: '2025-04-21 12:03',
                reporter: '谢小雅',
                handler: '谢小雅',
                address: '山北路5号井路交界',
                images: [
                    '/static/images/danger1.jpg',
                    '/static/images/danger2.jpg',
                    '/static/images/danger3.jpg',
                    '/static/images/danger4.jpg'
                ]
            },
            logList: [
                {
                    user: '李一一',
                    time: '2020-04-20 12:00',
                    type: '发现隐患',
                    description: '电缆基座与管线间距不足'
                },
                {
                    user: '李一一',
                    time: '2020-04-20 12:10',
                    type: '隐患处理',
                    description: '告知迁改'
                },
                {
                    user: '李一一',
                    time: '2020-04-21 12:10',
                    type: '隐患复检',
                    description: '隐患照常处理'
                }
            ]
        };
    },
    onLoad(options) {
        // 接收传递的隐患ID或数据
        if (options.id) {
            this.loadDangerDetail(options.id);
        }
        if (options.data) {
            try {
                const data = JSON.parse(decodeURIComponent(options.data));
                this.dangerDetail = { ...this.dangerDetail, ...data };
            } catch (e) {
                console.error('解析隐患数据失败:', e);
            }
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 切换Tab
        switchTab(index) {
            this.activeTab = index;
        },

        // 获取状态样式类
        getStatusClass(status) {
            switch (status) {
                case '待处理':
                    return 'status-pending';
                case '处理中':
                    return 'status-processing';
                case '已完成':
                    return 'status-completed';
                default:
                    return 'status-pending';
            }
        },

        // 预览图片
        previewImage(index) {
            uni.previewImage({
                urls: this.dangerDetail.images,
                current: index
            });
        },

        // 关闭隐患
        closeDanger() {
            uni.showModal({
                title: '确认关闭',
                content: '确定要关闭此隐患吗？',
                success: (res) => {
                    if (res.confirm) {
                        uni.showToast({
                            title: '关闭成功',
                            icon: 'success'
                        });
                        setTimeout(() => {
                            uni.navigateBack();
                        }, 1500);
                    }
                }
            });
        },

        // 隐患处理
        handleDanger() {
            uni.navigateTo({
                url: '/pages/water/danger-report/process?id=' + this.dangerDetail.id
            });
        },

        // 加载隐患详情
        loadDangerDetail(id) {
            // 这里可以根据ID从服务器获取详细数据
            console.log('加载隐患详情:', id);
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
}

.header {
    background: #1890ff;
    padding-top: var(--status-bar-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;

    .nav-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;

        .title {
            color: #fff;
            font-size: 18px;
            font-weight: 500;
        }
    }
}

/* Tab栏 */
.tab-container {
    background-color: #fff;
    display: flex;
    height: 50px;
    border-bottom: 1px solid #e8e8e8;
    position: fixed;
    top: calc(var(--status-bar-height) + 44px);
    left: 0;
    right: 0;
    z-index: 999;
}

.tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.tab-item.active {
    color: #1890ff;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: #ff6600;
    border-radius: 2px;
}

.tab-text {
    font-size: 16px;
    color: #666;
}

.tab-item.active .tab-text {
    color: #ff6600;
    font-weight: bold;
}

/* 内容区域 */
.content {
    flex: 1;
    margin-top: calc(var(--status-bar-height) + 44px + 50px);
    margin-bottom: 80px;
    background: #f5f5f5;
}

/* 日志内容 */
.log-content {
    padding: 16px;
}

.log-item {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.log-user {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-name {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.log-time {
    font-size: 12px;
    color: #999;
}

.log-content-box {
    .log-type {
        font-size: 14px;
        color: #1890ff;
        font-weight: bold;
        margin-bottom: 4px;
    }

    .log-description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
    }
}

/* 详情内容 */
.detail-content {
    background: #fff;
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
}

.detail-item {
    display: flex;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}

.detail-label {
    width: 80px;
    font-size: 14px;
    color: #666;
    flex-shrink: 0;
}

.detail-value {
    flex: 1;
    font-size: 14px;
    color: #333;
    word-break: break-all;

    &.status-pending {
        color: #1890ff;
    }

    &.status-processing {
        color: #faad14;
    }

    &.status-completed {
        color: #52c41a;
    }
}

/* 图片展示 */
.image-section {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
}

.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.image-item {
    width: calc(25% - 6px);
    height: 80px;
}

.danger-image {
    width: 100%;
    height: 100%;
    border-radius: 4px;
}

/* 底部按钮 */
.bottom-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    gap: 8px;
    z-index: 999;
}

.btn {
    flex: 1;
    height: 44px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
}

.btn-primary {
    background: #ff6600;
    color: #fff;
}
</style>